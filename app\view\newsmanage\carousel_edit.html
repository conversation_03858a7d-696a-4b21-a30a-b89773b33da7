{include file="public/_header"}

<div id="app">
    <el-container>
        <el-header>
            <h2>{{ isEdit ? '编辑轮播' : '添加轮播' }}</h2>
        </el-header>
        
        <el-main>
            <el-card>
                <el-form :model="form" :rules="rules" ref="form" label-width="120px">
                    <el-form-item label="轮播标题" prop="title">
                        <el-input v-model="form.title" placeholder="请输入轮播标题" style="width: 400px;"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="轮播图片" prop="image_path">
                        <el-upload
                            class="upload-demo"
                            action="/bs-upload-carousel"
                            :on-success="handleImageSuccess"
                            :on-error="handleImageError"
                            :before-upload="beforeImageUpload"
                            :show-file-list="false"
                            accept="image/*"
                            name="file">
                            <el-button size="small" type="primary">点击上传</el-button>
                            <div slot="tip" class="el-upload__tip">只能上传jpg/png/gif文件，且不超过10MB</div>
                        </el-upload>
                        
                        <div v-if="form.image_path" style="margin-top: 10px;">
                            <el-image 
                                style="width: 200px; height: 120px" 
                                :src="form.image_path" 
                                fit="cover">
                            </el-image>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="关联新闻" prop="news_id">
                        <el-select v-model="form.news_id" placeholder="请选择关联新闻" style="width: 400px;">
                            <el-option
                                v-for="news in newsList"
                                :key="news.id"
                                :label="news.title + ' (' + news.class_name + ')'"
                                :value="news.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="状态">
                        <el-switch
                            v-model="form.status"
                            :active-value="1"
                            :inactive-value="0"
                            active-text="启用"
                            inactive-text="禁用">
                        </el-switch>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
                        <el-button @click="goBack">返回</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </el-main>
    </el-container>
</div>

<script>
new Vue({
    el: '#app',
    data() {
        return {
            loading: false,
            isEdit: false,
            carouselId: null,
            form: {
                title: '',
                image_path: '',
                news_id: '',
                status: 1
            },
            newsList: [],
            rules: {
                title: [
                    { required: true, message: '请输入轮播标题', trigger: 'blur' }
                ],
                image_path: [
                    { required: true, message: '请上传轮播图片', trigger: 'change' }
                ],
                news_id: [
                    { required: true, message: '请选择关联新闻', trigger: 'change' }
                ]
            }
        }
    },
    created() {
        this.getNewsList();
        this.checkEditMode();
    },
    methods: {
        // 检查是否为编辑模式
        checkEditMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            if (id) {
                this.isEdit = true;
                this.carouselId = id;
                this.getCarouselInfo();
            }
        },
        
        // 获取新闻列表
        getNewsList() {
            axios.get('/news-carousel-newslist')
                .then(response => {
                    if (response.data.status === 'success') {
                        this.newsList = response.data.message.data;
                    } else {
                        this.$message.error('获取新闻列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取新闻列表错误:', error);
                    this.$message.error('获取新闻列表失败');
                });
        },
        
        // 获取轮播信息
        getCarouselInfo() {
            axios.get('/news-carousel-info?id=' + this.carouselId)
                .then(response => {
                    if (response.data.status === 'success') {
                        this.form = response.data.message;
                    } else {
                        this.$message.error('获取轮播信息失败');
                    }
                })
                .catch(error => {
                    console.error('获取轮播信息错误:', error);
                    this.$message.error('获取轮播信息失败');
                });
        },
        
        // 图片上传成功
        handleImageSuccess(response, file) {
            if (response.status === 'success') {
                this.form.image_path = response.location;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },
        
        // 图片上传失败
        handleImageError(err, file) {
            this.$message.error('图片上传失败');
        },
        
        // 图片上传前验证
        beforeImageUpload(file) {
            const isImage = file.type.startsWith('image/');
            const isLt10M = file.size / 1024 / 1024 < 10;
            
            if (!isImage) {
                this.$message.error('只能上传图片文件!');
                return false;
            }
            if (!isLt10M) {
                this.$message.error('图片大小不能超过 10MB!');
                return false;
            }
            return true;
        },
        
        // 提交表单
        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    
                    const url = this.isEdit ? '/news-carousel-edit' : '/news-carousel-add';
                    const data = { data: this.form };
                    
                    if (this.isEdit) {
                        data.id = this.carouselId;
                    }
                    
                    axios.post(url, data).then(response => {
                        if (response.data.status === 'success') {
                            this.$message.success(this.isEdit ? '编辑成功' : '添加成功');
                            setTimeout(() => {
                                this.goBack();
                            }, 1500);
                        } else {
                            this.$message.error(response.data.message);
                        }
                    }).catch(error => {
                        console.error('提交表单错误:', error);
                        this.$message.error('操作失败');
                    }).finally(() => {
                        this.loading = false;
                    });
                }
            });
        },
        
        // 返回
        goBack() {
            window.close();
        }
    }
});
</script>

<style scoped>
.el-header {
    background-color: #f5f5f5;
    line-height: 60px;
    padding: 0 20px;
}

.el-main {
    padding: 20px;
}

.upload-demo {
    margin-bottom: 10px;
}
</style>

{include file="public/_footer"} 