{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/dc/concludeproject.css">
<style>
  body{
    overflow-x: hidden;
    /* padding: 0 3rem; */
  }
</style>
<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">大创平台</a>
    >
    <a style="text-decoration: none; color: #00000097;" :href="'dc-concludeproject?uid=' + uid">结题报告</a>

  </p>

  <el-row :gutter="40">
    <h2>“大学生创新创业训练计划”项目结题验收报告</h2>
    <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="15rem" >
      <el-col  :sm="24" :md="12" >
      <el-form-item label="项目成果简介" prop="results">
        <el-input
        type="textarea"
        show-word-limit
        :autosize="{ minRows: 5, maxRows: 20}"
        placeholder="重点介绍特色及创新点"
        v-model="form.results">
       </el-input>
      </el-form-item>
      <el-form-item label="项目结题报告" prop="conclusion">
        <el-input
        type="textarea"
        minlength="2000"
        show-word-limit
        :autosize="{ minRows: 5, maxRows: 20}"
        placeholder="项目实施过程中创新思维和创新实践方面收获（不少于2000字）"
        v-model="form.conclusion">
       </el-input>
      </el-form-item>
      <el-form-item label="项目实施过程中存在的问题和建议" prop="problem">
        <el-input
        type="textarea"
        show-word-limit
        :autosize="{ minRows: 5, maxRows: 20}"
        placeholder=""
        v-model="form.problem">
       </el-input>
      </el-form-item>
      <el-form-item label="是否申请优秀项目" prop="excellent_project">
        <el-radio v-model="form.excellent_project" label="0">否</el-radio>
        <el-radio v-model="form.excellent_project" label="1">是</el-radio>
      </el-form-item>
      <el-form-item label="是否申请优秀论文" prop="excellent_lunwen">
        <el-radio v-model="form.excellent_lunwen" label="0">否</el-radio>
        <el-radio v-model="form.excellent_lunwen" label="1">是</el-radio>
      </el-form-item>
      <el-form-item label="项目结题经费支出情况" prop="expenses">
        <el-button plain type="primary" @click="addExpense" style="margin-top: 20px;">添加支出</el-button>
        <div v-for="(expense, index) in form.expenses" :key="index" class="expense-row">
          <el-input v-model="expense.detail" placeholder="支出明细" style="width: 150px;"></el-input>
          <el-input-number :min="0" :max="10000" v-model="expense.amount" placeholder="金额" style="width: 200px; margin-left: 20px;"></el-input-number>
          <el-button plain type="danger" @click="removeExpense(index)">删除</el-button>
        </div>
      </el-form-item>
      <el-form-item label="支出总计" prop="total">
        <el-input type="text"  readonly :value="computedTotalAmount"> </el-input>
      </el-form-item>
    </el-col>
  
    <el-col  :sm="24" :md="12" >
      <el-form-item label="支撑材料" prop="fileurls">
        <el-upload
        class="upload-demo"
        drag
        action="bs-addfile?class=dc&type=conclude"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-remove="removeFile"
        :show-file-list="false"
        >
        <i  class="el-icon-upload"></i>
        <div  class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传PDF，PPT, Word, Excel及压缩包文件，且不超过20MB</div>
        {if isset($data)}
        <div class="el-upload__tip" slot="tip">修改后之前结题材料将被覆盖</div>
        {/if}
      </el-upload>
      <div style="display: flex;" v-if="form.fileurls.length > 0" class="el-upload-list__item" v-for="file in form.fileurls" :key="file.uid">
        <span class="el-upload-list__item-name">{{ file.name }}</span>
        <el-button size="mini" type="danger" plain @click="removeFile(file)">删除</el-button>
      </div>
      </el-form-item>

      <el-form-item label="项目结题成果" prop="achievement">
        <el-checkbox-group v-model="form.achievement">
          <el-checkbox label="论文"></el-checkbox>
            <div v-if="form.achievement.includes('论文')">
              <el-button plain type="primary" @click="add('lunwen')" style="margin-top: 20px;">添加论文</el-button>  
              <div v-for="(lunwen, index) in form.extras.lunwen" :key="index" class="expense-row">
                <el-input v-model="lunwen.title" placeholder="论文题目" ></el-input>
                <el-input v-model="lunwen.author" placeholder="作者" ></el-input>
                <el-input v-model="lunwen.main" placeholder="刊物名及刊号" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="lunwen.time" type="date" placeholder="发表作品时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="lunwen.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="lunwen.remark" placeholder="备注" ></el-input>
            
                <el-button plain type="danger" @click="remove(index, 'lunwen')">删除</el-button>
              </div>
            </div>
            <el-checkbox label="专利"></el-checkbox>
            <div v-if="form.achievement.includes('专利')">
              <el-button plain type="primary" @click="add('zhuanli')" style="margin-top: 20px;">添加专利</el-button>  
              <div v-for="(zhuanli, index) in form.extras.zhuanli" :key="index" class="expense-row">
                <el-input v-model="zhuanli.title" placeholder="专利名称" ></el-input>
                <el-input v-model="zhuanli.author" placeholder="发明人" ></el-input>
                <el-input v-model="zhuanli.main" placeholder="专利申请号" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="zhuanli.time" type="date" placeholder="发表专利时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="zhuanli.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="zhuanli.remark" placeholder="备注" ></el-input>
                
                <el-button plain type="danger" @click="remove(index, 'zhuanli')">删除</el-button>
              </div>
            </div>
            <el-checkbox label="调查报告"></el-checkbox>
            <div v-if="form.achievement.includes('调查报告')">
              <el-button plain type="primary" @click="add('diaochabaogao')" style="margin-top: 20px;">添加调查报告</el-button>  
              <div v-for="(diaochabaogao, index) in form.extras.diaochabaogao" :key="index" class="expense-row">
                <el-input v-model="diaochabaogao.title" placeholder="报告名称" ></el-input>
                <el-input v-model="diaochabaogao.author" placeholder="作者" ></el-input>
                <el-input v-model="diaochabaogao.main" placeholder="主要内容" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="diaochabaogao.time" type="date" placeholder="时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="diaochabaogao.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="diaochabaogao.remark" placeholder="备注" ></el-input>
                
                <el-button plain type="danger" @click="remove(index, 'diaochabaogao')">删除</el-button>
              </div>
            </div>
            <el-checkbox label="商业计划书"></el-checkbox>
            <div v-if="form.achievement.includes('商业计划书')">
              <el-button plain type="primary" @click="add('shangyejihuashu')" style="margin-top: 20px;">添加商业计划书</el-button>  
              <div v-for="(shangyejihuashu, index) in form.extras.shangyejihuashu" :key="index" class="expense-row">
                <el-input v-model="shangyejihuashu.title" placeholder="名称" ></el-input>
                <el-input v-model="shangyejihuashu.author" placeholder="计划人" ></el-input>
                <el-input v-model="shangyejihuashu.main" placeholder="主要内容" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="shangyejihuashu.time" type="date" placeholder="时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="shangyejihuashu.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="shangyejihuashu.remark" placeholder="备注" ></el-input>
                
                <el-button plain type="danger" @click="remove(index, 'shangyejihuashu')">删除</el-button>
              </div>
            </div>
            <el-checkbox label="著作"></el-checkbox>
            <div v-if="form.achievement.includes('著作')">
              <el-button plain type="primary" @click="add('zhuzuo')" style="margin-top: 20px;">添加著作</el-button>  
              <div v-for="(zhuzuo, index) in form.extras.zhuzuo" :key="index" class="expense-row">
                <el-input v-model="zhuzuo.title" placeholder="书名" ></el-input>
                <el-input v-model="zhuzuo.author" placeholder="作者" ></el-input>
                <el-input v-model="zhuzuo.main" placeholder="出版单位" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="zhuzuo.time" type="date" placeholder="出版时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="zhuzuo.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="zhuzuo.remark" placeholder="备注" ></el-input>
                
                <el-button plain type="danger" @click="remove(index, 'zhuzuo')">删除</el-button>
              </div>
            </div>
            <el-checkbox label="工商注册"></el-checkbox>
            <div v-if="form.achievement.includes('工商注册')">
              <el-button plain type="primary" @click="add('gongshangzhuce')" style="margin-top: 20px;">添加工商注册</el-button>  
              <div v-for="(gongshangzhuce, index) in form.extras.gongshangzhuce" :key="index" class="expense-row">
                <el-input v-model="gongshangzhuce.title" placeholder="注册名称" ></el-input>
                <el-input v-model="gongshangzhuce.author" placeholder="注册人" ></el-input>
                <el-input v-model="gongshangzhuce.main" placeholder="执照代码" ></el-input>
                <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="gongshangzhuce.time" type="date" placeholder="注册时间" ></el-date-picker>
                <el-input-number :step="1" :min="0" :max="10000" v-model="gongshangzhuce.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
                <el-input v-model="gongshangzhuce.remark" placeholder="备注" ></el-input>
                
                <el-button plain type="danger" @click="remove(index, 'gongshangzhuce')">删除</el-button>
              </div>
            </div>
<el-checkbox label="公众号"></el-checkbox>
<div v-if="form.achievement.includes('公众号')">
  <el-button plain type="primary" @click="add('gzh')" style="margin-top: 20px;">添加公众号</el-button>  
  <div v-for="(gzh, index) in form.extras.gzh" :key="index" class="expense-row">
    <el-input v-model="gzh.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="gzh.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="gzh.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="gzh.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'gzh')">删除</el-button>
  </div>
</div>

<el-checkbox label="网站"></el-checkbox>
<div v-if="form.achievement.includes('网站')">
  <el-button plain type="primary" @click="add('wz')" style="margin-top: 20px;">添加网站</el-button>  
  <div v-for="(wz, index) in form.extras.wz" :key="index" class="expense-row">
    <el-input v-model="wz.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="wz.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="wz.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="wz.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'wz')">删除</el-button>
  </div>
</div>

<el-checkbox label="软件"></el-checkbox>
<div v-if="form.achievement.includes('软件')">
  <el-button plain type="primary" @click="add('rj')" style="margin-top: 20px;">添加软件</el-button>  
  <div v-for="(rj, index) in form.extras.rj" :key="index" class="expense-row">
    <el-input v-model="rj.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="rj.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="rj.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="rj.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'rj')">删除</el-button>
  </div>
</div>

<el-checkbox label="小程序"></el-checkbox>
<div v-if="form.achievement.includes('小程序')">
  <el-button plain type="primary" @click="add('xcx')" style="margin-top: 20px;">添加小程序</el-button>  
  <div v-for="(xcx, index) in form.extras.xcx" :key="index" class="expense-row">
    <el-input v-model="xcx.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="xcx.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="xcx.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="xcx.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'xcx')">删除</el-button>
  </div>
</div>

<el-checkbox label="APP"></el-checkbox>
<div v-if="form.achievement.includes('APP')">
  <el-button plain type="primary" @click="add('app')" style="margin-top: 20px;">添加APP</el-button>  
  <div v-for="(app, index) in form.extras.app" :key="index" class="expense-row">
    <el-input v-model="app.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="app.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="app.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="app.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'app')">删除</el-button>
  </div>
</div>

<el-checkbox label="运营号"></el-checkbox>
<div v-if="form.achievement.includes('运营号')">
  <el-button plain type="primary" @click="add('yyh')" style="margin-top: 20px;">添加运营号</el-button>  
  <div v-for="(yyh, index) in form.extras.yyh" :key="index" class="expense-row">
    <el-input v-model="yyh.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="yyh.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="yyh.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="yyh.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'yyh')">删除</el-button>
  </div>
</div>

<el-checkbox label="微课"></el-checkbox>
<div v-if="form.achievement.includes('微课')">
  <el-button plain type="primary" @click="add('wk')" style="margin-top: 20px;">添加微课</el-button>  
  <div v-for="(wk, index) in form.extras.wk" :key="index" class="expense-row">
    <el-input v-model="wk.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="wk.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="wk.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="wk.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'wk')">删除</el-button>
  </div>
</div>

<el-checkbox label="视频"></el-checkbox>
<div v-if="form.achievement.includes('视频')">
  <el-button plain type="primary" @click="add('sp')" style="margin-top: 20px;">添加视频</el-button>  
  <div v-for="(sp, index) in form.extras.sp" :key="index" class="expense-row">
    <el-input v-model="sp.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="sp.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="sp.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="sp.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'sp')">删除</el-button>
  </div>
</div>

<el-checkbox label="绘本（图册）"></el-checkbox>
<div v-if="form.achievement.includes('绘本（图册）')">
  <el-button plain type="primary" @click="add('hb')" style="margin-top: 20px;">添加绘本（图册）</el-button>  
  <div v-for="(hb, index) in form.extras.hb" :key="index" class="expense-row">
    <el-input v-model="hb.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="hb.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="hb.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="hb.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'hb')">删除</el-button>
  </div>
</div>
<el-checkbox label="其他"></el-checkbox>
<div v-if="form.achievement.includes('其他')">
  <el-button plain type="primary" @click="add('other')" style="margin-top: 20px;">添加其他</el-button>  
  <div v-for="(other, index) in form.extras.other" :key="index" class="expense-row">
    <el-input v-model="other.title" placeholder="名称" ></el-input>
    <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="other.time" type="date" placeholder="时间" ></el-date-picker>
    <el-input-number :step="1" :min="0" :max="10000" v-model="other.num" placeholder="数量" style=" margin-left: 20px;"></el-input-number>
    <el-input v-model="other.remark" placeholder="备注" ></el-input>
    
    <el-button plain type="danger" @click="remove(index, 'other')">删除</el-button>
  </div>
</div>


          <!-- <el-checkbox label="专利"></el-checkbox>
          <div v-if="form.expected.includes('专利')">
            <el-input-number v-model="form.extras.zhuanli" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="调查报告"></el-checkbox>
          <div v-if="form.expected.includes('调查报告')">
            <el-input-number v-model="form.extras.diaochabaogao" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="商业计划书"></el-checkbox>
          <div v-if="form.expected.includes('商业计划书')">
            <el-input-number v-model="form.extras.shangyejihuashu" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="著作"></el-checkbox>
          <div v-if="form.expected.includes('著作')">
            <el-input-number v-model="form.extras.zhuzuo" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="作品"></el-checkbox>
          <div v-if="form.expected.includes('作品')">
            <el-select
            v-model="form.extras.zuopin.class"
            placeholder="作品类别"
            >
            <el-option  lable="软件" value="软件"></el-option>
            <el-option  lable="课件" value="课件"></el-option>
            <el-option  lable="视频" value="视频"></el-option>
            <el-option  lable="微课" value="微课"></el-option>
            <el-option  lable="其他" value="其他"></el-option>
          </el-select>
            <el-input-number v-model="form.extras.zuopin.num" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="公众号"></el-checkbox>
          <div v-if="form.expected.includes('公众号')">
            <el-input-number v-model="form.extras.gzh" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="网站"></el-checkbox>
          <div v-if="form.expected.includes('网站')">
            <el-input-number v-model="form.extras.wz" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="软件"></el-checkbox>
          <div v-if="form.expected.includes('软件')">
            <el-input-number v-model="form.extras.rj" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="小程序"></el-checkbox>
          <div v-if="form.expected.includes('小程序')">
            <el-input-number v-model="form.extras.xcx" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="APP"></el-checkbox>
          <div v-if="form.expected.includes('APP')">
            <el-input-number v-model="form.extras.app" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="运营号"></el-checkbox>
          <div v-if="form.expected.includes('运营号')">
            <el-input-number v-model="form.extras.yyh" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="微课"></el-checkbox>
          <div v-if="form.expected.includes('微课')">
            <el-input-number v-model="form.extras.wk" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="视频"></el-checkbox>
          <div v-if="form.expected.includes('视频')">
            <el-input-number v-model="form.extras.sp" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="绘本（图册）"></el-checkbox>
          <div v-if="form.expected.includes('绘本（图册）')">
            <el-input-number v-model="form.extras.hb" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="其他"></el-checkbox>
          <div v-if="form.expected.includes('其他')">
            <el-input v-model="form.extras.other" maxlength="10" show-word-limit  placeholder="请输入预期成果"></el-input>
        </div> -->
        </el-checkbox-group>
      </el-form-item>

    </el-col>

      <el-col  :sm="24"  style="text-align: center;">
        <el-button type="primary " plain @click="onSubmit">立即提交结题报告</el-button>
      
          </el-col>

    </el-form>



  </el-row>





</div>
<script src="../../static/js/dc/concludeproject.js"></script>
<script>
        {if isset($data)}
    app.updateform({:json_encode($data)})
{/if}
</script>
</body>
</html>