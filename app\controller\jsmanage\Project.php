<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Dclevel;
use app\model\Dcproject;
use app\model\Dcstatus;
use app\model\Department;
use app\model\Jsaward;
use app\model\Jscompetition;
use app\model\Jsproject;
use app\model\Jsstatus;

class Project extends BaseController
{
    public function index()
    {
        LogExecution('进入竞赛项目列表');
        $search=[
            'status'=>JSstatus::where('is_delete',0)->select(),
            'level'=>Jsaward::where('is_delete',0)->field('id, CONCAT(type, " - ", level) as name')->select(),
            'department'=>Department::where('is_delete',0)->select(),
            'competition'=>Jscompetition::where('is_delete',0)->select(),
        ];
        return view('jsmanage/project',['search'=>$search]);
    }
    public function select_project(){
        // 检查用户是否有权限查看竞赛项目
        if (!$this->checkPermission('js_view')) {
            LogExecution(session('user.username') . '尝试访问竞赛项目管理');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $search=input('post.search');
        $where=[];
        
        // 根据用户权限过滤数据
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        // 教师只能看自己指导的项目
        if ($userMode == 2) {
            $where[] = ['t.username', '=', session('user.username')];
        }
        
        // 院级管理员只能查看本学院的项目
        if ($userMode == 5) {
            $where[]=['m.rank','=',1];
            $where[]=['mu.college','=',$userCollege];
            // 同时确保只能查看本学院的竞赛项目
            $where[]=['c.college','=',$userCollege];
        }
        // 校级管理员可以查看所有项目，不需要额外过滤
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[]=['p.uid|p.class|p.name','like','%'.$text.'%'];
            }
        }
        if ($search['status']!='all'){
            $where[]=['p.status','=',$search['status']];
        }
        if ($search['level']!='all'){
            $where[]=['p.award','=',$search['level']];
        }
        if ($search['competition']!='all'){
            $where[]=['p.cuid','=',$search['competition']];
        }
        if ($search['user']!=''){
            $where[]=['m.username|t.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[]=['m.rank','=',1];
            $where[]=['mu.college','=',$search['department']];
        }
        $data = Jsproject::alias('p')
            ->field('
        p.uid,p.name,p.time,p.class,
        c.name as c_name,
        s.name as s_status,
        CONCAT(a.level, a.type) AS award,
        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames
    ')
            ->join('jscompetition c', 'c.cuid = p.cuid', 'LEFT')
            ->join('jsaward a', 'a.id = p.award', 'LEFT')

            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
            ->join('user mu', 'mu.username = m.username', 'LEFT')
            ->join('user tu', 'tu.username = t.username', 'LEFT')
            ->join('jsstatus s','s.id=p.status','LEFT')
            ->where('p.is_delete', 0)
            ->where('c.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
            ->where('mu.status', 0)
            ->where('tu.status', 0)
            ->where($where)
            ->group('p.uid')
            ->select();
        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($data),
            'data'=>$data
        ]]);
    }
    
    /**
     * 导出竞赛项目数据
     */
    public function export_projects()
    {
        // 检查用户是否有权限导出竞赛项目
        $userMode = session('user.usermode');
        if (!in_array($userMode, [5, 6, 11])) {
            LogExecution(session('user.username') . '尝试导出竞赛项目数据');
            return json(['status' => 'error', 'message' => '权限不足，只有竞赛管理员可以导出数据']);
        }
        
        LogExecution('开始导出竞赛项目数据');
        $search = input('post.search');
        $where = [];
        
        // 院级管理员只能导出本学院的数据
        if ($userMode == 5) {
            $userCollege = session('user.college');
            $where[] = ['m.rank','=',1];
            $where[] = ['mu.college','=',$userCollege];
            // 同时确保只能导出本学院的竞赛项目
            $where[] = ['c.college','=',$userCollege];
        }
        
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[] = ['p.name|p.class','like','%'.$text.'%'];
            }
        }
        if ($search['status']!='all'){
            $where[] = ['p.status','=',$search['status']];
        }
        if ($search['level']!='all'){
            $where[] = ['p.award','=',$search['level']];
        }
        if ($search['user']!=''){
            $where[] = ['m.username|t.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[] = ['m.rank','=',1];
            $where[] = ['mu.college','=',$search['department']];
        }
        if ($search['competition']!='all'){
            $where[] = ['p.cuid','=',$search['competition']];
        }
        
        $data = Jsproject::alias('p')
            ->field('
        p.uid,p.cuid,p.name,p.class,p.time,
        c.name as c_name,
        s.name as s_status,
        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames,
        CONCAT(a.level, " - ", a.type) as award
    ')
            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
            ->join('user mu', 'mu.username = m.username', 'LEFT')
            ->join('user tu', 'tu.username = t.username', 'LEFT')
            ->join('jsstatus s','s.id=p.status','LEFT')
            ->join('jscompetition c','c.cuid=p.cuid','LEFT')
            ->join('jsaward a','a.id=p.award','LEFT')
            ->where('p.is_delete', 0)
            ->where('c.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
            ->where($where)
            ->where('mu.status', 0)
            ->where('tu.status', 0)
            ->group('p.uid')
            ->select();
            
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '竞赛名称',
            '项目名称',
            '赛道/类别',
            '负责人',
            '指导教师',
            '立项时间',
            '获奖信息',
            '状态'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatJsProjectData($data);
        
        // 生成文件名
        $filename = $exportService->generateFilename('竞赛项目列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        LogExecution('竞赛项目导出完成，文件：' . $filename);
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
}