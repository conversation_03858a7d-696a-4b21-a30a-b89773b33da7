<?php /*a:2:{s:46:"E:\code\cxcysys\app\view\jsmanage\project.html";i:1756369924;s:44:"E:\code\cxcysys\app\view\public\_header.html";i:1749341264;}*/ ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>创新创业管理平台</title>
    <title>创新创业学院</title>
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script> -->
    <!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script> -->
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->

    <link rel="stylesheet" href="../../static/css/<EMAIL>">

    <script src="../../static/js/vue2.js"></script>
    <script src="../../static/js/<EMAIL>"></script>
    <script src="../../static/js/axios.min.js"></script>




	<style>
		body,html{
			margin: 0;
			padding: 0;
		}
		body{
			width: 100%;
		}
	</style>
    
</head>
<body>



  <script>
    window.userinfo = {
      username: '<?php echo session('user.username'); ?>',
      usermode: '<?php echo session('user.usermode'); ?>'
    };
    console.log('页面加载，用户信息：', window.userinfo);
  </script>
  <link rel="stylesheet" href="../../static/css/js/project.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">竞赛平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-project">项目管理</a>
</p>
<el-row style="margin: auto;" :gutter="5">

<el-col :span="3" v-if="canExportProjects">
<el-button plain type="primary" @click="exportProjects" id="exportBtn">导出当前{{ totalData.length }}个项目</el-button>
</el-col>

<!-- <el-col :span="3" v-if="canAddProject">
<el-button plain type="primary" @click="addProject">添加项目</el-button>
</el-col> -->

  <el-col :span="3">
    <el-select v-model="search.department" placeholder="请选择部门" style="width: 100%">
      <el-option value="all" label="全部部门"></el-option>

      <?php foreach($search['department'] as $department): ?>
      <el-option value="<?php echo htmlentities((string) $department['id']); ?>" label="<?php echo htmlentities((string) $department['name']); ?>"></el-option>
      <?php endforeach; ?>
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select
    style="width: 100%;"
    v-model="search.user"
    filterable
    remote
    clearable
    reserve-keyword
    placeholder="全部成员"
    :remote-method="remoteusers"
    :loading="loading">
    <el-option
      v-for="item in users"
      :key="item.value"
      :label="item.label"
      :value="item.value">
      <span style="float: left">{{ item.label }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
    </el-option>
  </el-select>
  </el-col>
  <el-col :span="3">
    <el-select v-model="search.competition" placeholder="请选择竞赛" style="width: 100%">
      <el-option value="all" label="全部竞赛"></el-option>
          <?php foreach($search['competition'] as $competition): ?>
      <el-option value="<?php echo htmlentities((string) $competition['cuid']); ?>" label="<?php echo htmlentities((string) $competition['name']); ?>"></el-option>
      <?php endforeach; ?>
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select v-model="search.level" placeholder="请选择获奖级别" style="width: 100%">
      <el-option value="all" label="全部级别"></el-option>
          <?php foreach($search['level'] as $level): ?>
      <el-option value="<?php echo htmlentities((string) $level['id']); ?>" label="<?php echo htmlentities((string) $level['name']); ?>"></el-option>
      <?php endforeach; ?>
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select v-model="search.status" placeholder="请选择状态" style="width: 100%">
      <el-option value="all" label="全部状态"></el-option>
          <?php foreach($search['status'] as $status): ?>
      <el-option value="<?php echo htmlentities((string) $status['id']); ?>" label="<?php echo htmlentities((string) $status['name']); ?>"></el-option>
      <?php endforeach; ?>
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select
    style="width: 100%"
    v-model="search.texts"
    multiple
    filterable
    allow-create
    default-first-option
    placeholder="以回车分割(项目名称，编号，赛道)">
  </el-select>
  </el-col>
  <el-col :span="3">
    <el-button plain type="primary" @click="select_competition">搜索</el-button>
  </el-col>
</el-row>
<!-- <el-row>
  <el-button plain type="primary">竞赛立项</el-button>
</el-row> -->
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
          <!-- <el-table-column
            prop="uid"
            label="申请编号"
            min-width="120"
            align="center"
          ></el-table-column> -->

          <el-table-column
            prop="c_name"
            label="竞赛名称"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="name"
          label="项目名称"
          min-width="180"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="class"
            label="赛道/类别"
            min-width="120"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="m_names[0]"
          label="负责人"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="t_names"
          label="指导教师"
          min-width="120"
          align="center"
        ></el-table-column>
          <el-table-column
          prop="time"
          label="立项时间"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
        prop="award"
        label="获奖信息"
        min-width="120"
        align="center"
      ></el-table-column>
                <el-table-column
            prop="s_status"
            label="状态"
            min-width="150"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="goto(scope.row)">详情</el-button>
              <el-button size="mini" v-if="canEditProject()" @click="editProject(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
</div>
<script src="../../static/js/js/project.js?v=2.3"></script>
</body>
</html>