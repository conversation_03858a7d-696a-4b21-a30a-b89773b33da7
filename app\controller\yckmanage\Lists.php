<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\Department;
use app\model\Jsaward;
use app\model\Jscompetition;
use app\model\Jsstatus;
use app\model\Yckuser;

class Lists extends BaseController
{
    public function index()
    {
        LogExecution('进入英才库列表');
        $search=[
            'department'=>Department::where('is_delete',0)->select(),
        ];
        return view('yckmanage/lists',['search'=>$search]);
    }
    public function select_lists(){
        $search=input('post.search');
        $where=[];
        
        // 根据用户权限过滤数据
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        // 英才库校级管理员只能查看本学院的数据
        if ($userMode == 7) {
            $where[]=['u.college','=',$userCollege];
        }
        
        if ($search['type']=='英才学生'){
            $where[]=['u.usermode','=',1];
        }elseif ($search['type']=='精英教师'){
            $where[]=['u.usermode','=',2];

        }
        if ($search['user']!=''){
            $where[]=['yu.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[]=['u.college','=',$search['department']];
        }

//        if ($search['texts']){
//            foreach ($search['texts'] as $text){
//                $where[]=['r.room_number|r.name|r.area|r.nature|r.management_unit|r.manager|r.safety_manager|r.remarks|r.use|r.phone1|r.phone','like','%'.$text.'%'];
//            }
//        }
//        if ($search['building_id']!='all'){
//            $where[]=['r.building_id','=',$search['building_id']];
//        }
//        if (session('user.level')==2){
//            $where[]=['r.building_id','=',session('user.building_id')];
//        }
        $data = Yckuser::alias('yu')
            ->field('
               yu.username,yu.mark,yu.index,
               u.name,
               d.name as department,
               m.name as major,
               j.name as job,
               GROUP_CONCAT(DISTINCT t.name ORDER BY t.id ASC) as tags
               ')
            ->join('user u', 'u.username = yu.username', 'LEFT')
            ->join('yckuser y','y.username=u.username','LEFT')
            ->join('yckjob j','y.job=j.id','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->join('ycktag t','t.username=yu.username','LEFT')
            ->where($where)
            ->where('y.is_delete',0)
            ->where('u.status',0)
            ->where('yu.status',2)
            ->order('yu.index', 'asc')
            ->order('yu.mark', 'asc')
            ->order('yu.created_at', 'asc')
            ->group('yu.username, yu.mark, yu.index, u.name, d.name, m.name, j.name,yu.created_at') // 添加 GROUP BY 子句
            ->select();

        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($data),
            'data'=>$data
        ]]);
    }
    
    /**
     * 导出英才列表数据
     */
    public function export_lists()
    {
        $search = input('post.search');
        $where = [];
        
        if ($search['type']=='英才学生'){
            $where[]=['u.usermode','=',1];
        }elseif ($search['type']=='精英教师'){
            $where[]=['u.usermode','=',2];
        }
        
        if ($search['user']!=''){
            $where[] = ['yu.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[] = ['u.college','=',$search['department']];
        }
        
        $data = Yckuser::alias('yu')
            ->field('
               yu.username,yu.mark,yu.index,
               u.name,
               d.name as department_name,
               m.name as major_name,
               j.name as job,
               GROUP_CONCAT(DISTINCT t.name ORDER BY t.id ASC) as tags
               ')
            ->join('user u', 'u.username = yu.username', 'LEFT')
            ->join('yckuser y','y.username=u.username','LEFT')
            ->join('yckjob j','y.job=j.id','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->join('ycktag t','t.username=yu.username','LEFT')
            ->where($where)
            ->where('y.is_delete',0)
            ->where('u.status',0)
            ->where('yu.status',2)
            ->order('yu.index', 'asc')
            ->order('yu.mark', 'asc')
            ->order('yu.created_at', 'asc')
            ->group('yu.username, yu.mark, yu.index, u.name, d.name, m.name, j.name,yu.created_at')
            ->select();
            
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '排序',
            '姓名',
            '学院',
            '专业',
            '标签',
            '评分'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatYckListData($data);
        
        // 生成文件名
        $filename = $exportService->generateFilename($search['type'] . '列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
    public function update_lists(){
        LogExecution('更新英才库排行');
        $data=input('post.data');
        if (!$data){
            return json(['status' => 'error', 'message' => '请输入排序']);
        }else{
            $update=Yckuser::where('is_delete',0)->where('username',$data['username'])->update(['index'=>$data['index']]);
            if ($update){
                return json(['status' => 'success', 'message' => '排序成功']);

            }else{
                return json(['status' => 'error', 'message' => '未作修改']);

            }
        }
    }
    
    /**
     * 获取官网首页英才库数据
     */
    public function get_homepage_talents()
    {
        $type = input('get.type', '英才学生');
        $limit = input('get.limit', 10);
        
        $where = [];
        if ($type == '英才学生'){
            $where[] = ['u.usermode','=',1];
        }elseif ($type == '精英教师'){
            $where[] = ['u.usermode','=',2];
        }
        
        $data = Yckuser::alias('yu')
            ->field('
               yu.username,yu.mark,yu.index,
               u.name,
               d.name as department,
               m.name as major_name,
               j.name as job,
               GROUP_CONCAT(DISTINCT t.name ORDER BY t.id ASC) as tags,
               GROUP_CONCAT(DISTINCT dom.name ORDER BY dom.id ASC) as domain
               ')
            ->join('user u', 'u.username = yu.username', 'LEFT')
            ->join('yckuser y','y.username=u.username','LEFT')
            ->join('yckjob j','y.job=j.id','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->join('ycktag t','t.username=yu.username','LEFT')
            ->join('yckdomain dom','dom.username=yu.username','LEFT')
            ->where($where)
            ->where('y.is_delete',0)
            ->where('u.status',0)
            ->where('yu.status',2)
            ->order('yu.index', 'asc')
            ->order('yu.mark', 'asc')
            ->order('yu.created_at', 'asc')
            ->group('yu.username, yu.mark, yu.index, u.name, d.name, m.name, j.name,yu.created_at')
            ->limit($limit)
            ->select();
            
        return json(['status' => 'success', 'message' => [
            'total' => count($data),
            'data' => $data
        ]]);
    }
}