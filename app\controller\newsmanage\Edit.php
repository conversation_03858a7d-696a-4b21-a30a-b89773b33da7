<?php

namespace app\controller\newsmanage;

use app\BaseController;
use app\model\Newsclass;
use app\model\Newsdetail;
use think\route\dispatch\Controller;

class Edit extends BaseController
{
    public function index(){
        $data=input('post.data');
        if (!$data){
            //新增新闻页面
            LogExecution('进入新闻编辑页新增新闻');
            $classes=Newsclass::where('is_delete',0)->order('rank','asc')->select();
            return view('newsmanage/edit',['classes'=>$classes]);
        }elseif($data&&!isset($data['news'])){
            //新增新闻
            LogExecution('新增新闻'.$data['title']);
            //判空
            $data['user']=session('user.username');
            if (Newsdetail::insert($data)){
                LogExecution('新增新闻'.$data['title'].'成功');
                return json(['status'=>'success','message' => '新增新闻成功']);
            }else{
                LogExecution('新增新闻'.$data['title'].'插入异常');
                return json(['status'=>'error','message' => '新增新闻异常']);
            }

        }elseif($data&&isset($data['news'])){
            //修改新闻
            //判断新闻存在
            LogExecution('修改新闻'.$data['title']);
            if (Newsdetail::where('is_delete',0)->where('id',$data['news'])->find()){
                $newnews=[
                    'title'=>$data['title'],
                    'auth'=>$data['auth'],
                    'content'=>$data['content'],
                    'class'=>$data['class']
                ];
                //判空
                if (Newsdetail::where('is_delete',0)->where('id',$data['news'])->update($newnews)){
                    LogExecution('修改新闻'.$data['title'].'成功');
                    return json(['status'=>'success','message' => '修改新闻成功']);
                }else{
                    LogExecution('修改新闻'.$data['title'].'插入异常');
                    return json(['status'=>'error','message' => '无修改']);
                }
            }else{
                LogExecution($data['news'].'新闻不存在');
                return json(['status'=>'error','message' => '新闻不存在']);
            }

        }

    }
}