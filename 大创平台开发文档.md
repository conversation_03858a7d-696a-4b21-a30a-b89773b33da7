# 大创平台开发文档

## 1. 项目概述

### 1.1 系统简介
大创平台是一个基于ThinkPHP 8.1框架开发的大学生创新创业训练计划项目管理系统，支持项目立项、中期报告、结题报告、延期申请、中期变更等全流程管理。

### 1.2 技术栈
- **后端框架**: ThinkPHP 8.1
- **前端框架**: Vue.js 2.x + Element UI
- **数据库**: MySQL 5.7+
- **服务器**: Apache/Nginx
- **开发语言**: PHP 8.0+

### 1.3 系统架构
```
cxcysys/
├── app/                    # 应用目录
│   ├── controller/         # 控制器
│   │   ├── dcmanage/      # 大创管理控制器
│   │   ├── basic/         # 基础功能控制器
│   │   └── ...
│   ├── model/             # 数据模型
│   ├── service/           # 服务层
│   └── view/              # 视图模板
├── config/                # 配置文件
├── public/                # 公共资源
├── static/                # 静态资源
└── route/                 # 路由配置
```

## 2. 数据库设计

### 2.1 核心数据表

#### 2.1.1 项目表 (dcproject)
```sql
CREATE TABLE `dcproject` (
  `uid` varchar(255) NOT NULL COMMENT '项目唯一标识',
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `proid` varchar(50) DEFAULT NULL COMMENT '项目编号',
  `time` date DEFAULT NULL COMMENT '立项时间',
  `type` int(11) DEFAULT NULL COMMENT '项目类型',
  `period` int(11) DEFAULT NULL COMMENT '项目周期',
  `level` int(11) DEFAULT NULL COMMENT '项目级别',
  `status` int(11) DEFAULT 1 COMMENT '项目状态',
  `introduction` text COMMENT '项目简介',
  `reason` text COMMENT '申请理由',
  `innovation` text COMMENT '项目特色及创新点',
  `schedule` text COMMENT '项目进度安排',
  `budget` decimal(10,2) DEFAULT NULL COMMENT '项目经费',
  `plan` text COMMENT '经费使用计划',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.2 项目成员表 (member)
```sql
CREATE TABLE `member` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `username` varchar(50) NOT NULL COMMENT '学号',
  `rank` int(11) DEFAULT 1 COMMENT '成员排名',
  `dc` tinyint(1) DEFAULT 1 COMMENT '大创项目标记',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.3 指导教师表 (teacher)
```sql
CREATE TABLE `teacher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `username` varchar(50) NOT NULL COMMENT '工号',
  `rank` int(11) DEFAULT 1 COMMENT '教师排名',
  `type` tinyint(1) DEFAULT 0 COMMENT '教师类型(0:校内,1:校外)',
  `dc` tinyint(1) DEFAULT 1 COMMENT '大创项目标记',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.4 审核记录表 (dccheck)
```sql
CREATE TABLE `dccheck` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `type` int(11) NOT NULL COMMENT '审核类型(1:教师,2:学院,3:学校)',
  `status` int(11) NOT NULL COMMENT '审核状态(1:立项,2:中期,3:结题,4:延期,5:变更)',
  `check` tinyint(1) DEFAULT 0 COMMENT '审核结果(0:通过,1:驳回)',
  `remark` text COMMENT '审核意见',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.5 项目进度表 (dcprogress)
```sql
CREATE TABLE `dcprogress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `action` varchar(100) NOT NULL COMMENT '操作名称',
  `remark` text COMMENT '操作备注',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.6 中期报告表 (dcinterm)
```sql
CREATE TABLE `dcinterm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `progress` text COMMENT '项目进展情况',
  `achievement` text COMMENT '阶段性成果',
  `problem` text COMMENT '存在的问题',
  `plan` text COMMENT '下一步计划',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.7 结题报告表 (dcconclude)
```sql
CREATE TABLE `dcconclude` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `results` text COMMENT '项目成果简介',
  `conclusion` text COMMENT '项目总结报告',
  `problem` text COMMENT '项目实施过程中存在的问题和建议',
  `excellent_project` tinyint(1) DEFAULT 0 COMMENT '是否申请优秀项目',
  `excellent_lunwen` tinyint(1) DEFAULT 0 COMMENT '是否申请优秀论文',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.8 延期申请表 (dcextension)
```sql
CREATE TABLE `dcextension` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `extension_time` int(11) NOT NULL COMMENT '延期时间(6:6个月,12:1年)',
  `reason` text COMMENT '延期理由',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.1.9 中期变更表 (dcmidchange)
```sql
CREATE TABLE `dcmidchange` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `change_items` text COMMENT '变更事项',
  `change_reason` text COMMENT '变更原因',
  `new_members` text COMMENT '新项目成员(JSON格式)',
  `new_teachers` text COMMENT '新指导教师(JSON格式)',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '删除标记',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2.2 状态流转说明

#### 2.2.1 项目状态定义
| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 等待指导教师立项审核 | 学生提交立项申请后 |
| 2 | 等待学院立项审核 | 教师审核通过后 |
| 3 | 等待学校立项审核 | 学院审核通过后 |
| 4 | 已立项待提交中期报告 | 学校审核通过后 |
| 5 | 等待教师中期审核 | 学生提交中期报告后 |
| 6 | 等待学院中期审核 | 教师中期审核通过后 |
| 7 | 等待学校中期审核 | 学院中期审核通过后 |
| 8 | 中期审核通过待提交结题报告 | 学校中期审核通过后 |
| 9 | 等待教师结题审核 | 学生提交结题报告后 |
| 10 | 等待学院结题审核 | 教师结题审核通过后 |
| 11 | 等待学校结题审核 | 学院结题审核通过后 |
| 12 | 教师立项审核驳回 | 教师驳回立项申请 |
| 13 | 学院立项审核驳回 | 学院驳回立项申请 |
| 14 | 学校立项审核驳回 | 学校驳回立项申请 |
| 15 | 教师中期审核驳回 | 教师驳回中期报告 |
| 16 | 学院中期审核驳回 | 学院驳回中期报告 |
| 17 | 学校中期审核驳回 | 学校驳回中期报告 |
| 18 | 教师结题审核驳回 | 教师驳回结题报告 |
| 19 | 学院结题审核驳回 | 学院驳回结题报告 |
| 20 | 学校结题审核驳回 | 学校驳回结题报告 |
| 21 | 项目结题完成 | 学校结题审核通过后 |
| 22 | 等待教师延期审核 | 学生申请延期后 |
| 23 | 等待学院延期审核 | 教师延期审核通过后 |
| 24 | 等待学校延期审核 | 学院延期审核通过后 |
| 25 | 延期审核通过 | 学校延期审核通过后 |
| 26 | 教师延期审核驳回 | 教师驳回延期申请 |
| 27 | 学院延期审核驳回 | 学院驳回延期申请 |
| 28 | 学校延期审核驳回 | 学校驳回延期申请 |
| 36 | 教师中期变更驳回 | 教师驳回中期变更 |
| 37 | 学院中期变更驳回 | 学院驳回中期变更 |
| 38 | 学校中期变更驳回 | 学校驳回中期变更 |
| 39 | 等待教师中期变更审核 | 学生提交中期变更后 |
| 40 | 等待学院中期变更审核 | 教师中期变更审核通过后 |
| 41 | 等待学校中期变更审核 | 学院中期变更审核通过后 |
| 42 | 中期变更审核通过 | 学校中期变更审核通过后 |

## 3. 功能模块开发说明

### 3.1 项目立项模块

#### 3.1.1 控制器: Addproject.php
**文件路径**: `app/controller/dcmanage/Addproject.php`

**主要方法**:
- `addProject($uid='')`: 项目立项/修改主方法
- `canEditProject($project)`: 检查项目是否可以修改

**权限控制逻辑**:
```php
private function canEditProject($project) {
    // 超级管理员可以修改任何状态的项目
    if (session('user.usermode') == 11) {
        return true;
    }
    
    // 学生只能修改特定状态的项目
    $allowedStatus = [1, 2, 3, 12, 13, 14, 15, 16, 17, 18, 19, 20];
    return in_array($project['status'], $allowedStatus);
}
```

**状态流转**:
1. 学生提交立项申请 → 状态变为1（等待指导教师立项审核）
2. 教师审核通过 → 状态变为2（等待学院立项审核）
3. 学院审核通过 → 状态变为3（等待学校立项审核）
4. 学校审核通过 → 状态变为4（已立项待提交中期报告）

#### 3.1.2 视图模板: addProject.html
**文件路径**: `app/view/dcmanage/addProject.html`

**主要功能**:
- 项目基本信息填写
- 项目成员选择（远程搜索）
- 指导教师选择（远程搜索）
- 项目文件上传
- 表单验证

**远程搜索实现**:
```javascript
// 远程搜索学生
remoteStudents(query) {
    if (query !== '') {
        this.loadingStudents = true;
        axios.post('bs-searchuser?type=1', {
            query: query
        })
        .then(response => {
            if (response.data.status === 'success') {
                this.studentOptions = response.data.message;
            }
        })
        .finally(() => {
            this.loadingStudents = false;
        });
    }
}
```

### 3.2 中期报告模块

#### 3.2.1 控制器: Intermproject.php
**文件路径**: `app/controller/dcmanage/Intermproject.php`

**主要方法**:
- `index($uid)`: 中期报告页面
- `add_interm($uid)`: 提交中期报告

**状态验证**:
```php
// 检查项目状态是否允许提交中期报告
if (!in_array($project['status'], [4, 25, 42])) {
    return json(['status'=>'error','message' => '当前不是可提交中期报告状态']);
}
```

#### 3.2.2 视图模板: intermProject.html
**文件路径**: `app/view/dcmanage/intermProject.html`

**主要功能**:
- 项目进展情况填写
- 阶段性成果记录
- 存在问题分析
- 下一步计划制定

### 3.3 结题报告模块

#### 3.3.1 控制器: Concludeproject.php
**文件路径**: `app/controller/dcmanage/Concludeproject.php`

**主要方法**:
- `index($uid)`: 结题报告页面
- `add_conclude($uid)`: 提交结题报告

**状态验证**:
```php
// 检查项目状态是否允许提交结题报告
if (!in_array($project['status'], [8, 25, 42])) {
    return json(['status'=>'error','message' => '当前不是可提交结题报告状态']);
}
```

#### 3.3.2 视图模板: concludeproject.html
**文件路径**: `app/view/dcmanage/concludeproject.html`

**主要功能**:
- 项目成果简介
- 项目总结报告
- 问题与建议
- 优秀项目/论文申请

### 3.4 延期结题模块

#### 3.4.1 控制器: Extension.php
**文件路径**: `app/controller/dcmanage/Extension.php`

**主要方法**:
- `index($uid)`: 延期申请页面
- `apply_extension($uid)`: 提交延期申请

**状态验证**:
```php
// 检查项目状态是否允许申请延期
if (!in_array($project['status'], [8, 28])) {
    return json(['status'=>'error','message' => '当前不是可申请延期状态']);
}
```

#### 3.4.2 视图模板: extension.html
**文件路径**: `app/view/dcmanage/extension.html`

**主要功能**:
- 延期时间选择（6个月/1年）
- 延期理由填写
- 状态流转控制

### 3.5 中期变更模块

#### 3.5.1 控制器: Midchange.php
**文件路径**: `app/controller/dcmanage/Midchange.php`

**主要方法**:
- `index($uid)`: 中期变更申请页面
- `apply_midchange($uid)`: 提交中期变更申请

**状态验证**:
```php
private function canApplyMidchange($status) {
    // 状态4：已立项待提交中期报告
    // 状态36：指导教师中期变更驳回
    // 状态37：学院中期变更驳回
    // 状态38：学校中期变更驳回
    $allowedStatus = [4, 36, 37, 38];
    return in_array($status, $allowedStatus);
}
```

#### 3.5.2 视图模板: midchange.html
**文件路径**: `app/view/dcmanage/midchange.html`

**主要功能**:
- 项目成员变更（远程搜索）
- 指导教师变更（远程搜索）
- 变更事项说明
- 变更原因分析

### 3.6 审核模块

#### 3.6.1 控制器: Check.php
**文件路径**: `app/controller/dcmanage/Check.php`

**主要方法**:
- `checkProject($uid)`: 项目审核主方法
- `processCheck($uid, $project, $check, $remark)`: 处理审核逻辑
- `canCheckCurrentStatus($status, $userMode)`: 验证当前状态是否可以被审核

**审核权限验证**:
```php
private function canCheckCurrentStatus($status, $userMode) {
    $checkableStatus = [
        2 => [1, 5, 9, 22, 39], // 教师可审核的状态
        3 => [2, 6, 10, 23, 40], // 学院可审核的状态
        4 => [3, 7, 11, 24, 41], // 学校可审核的状态
    ];
    return isset($checkableStatus[$userMode]) && in_array($status, $checkableStatus[$userMode]);
}
```

**状态流转计算**:
```php
private function calculateNewStatus($currentStatus, $userMode, $check) {
    if ($check) {
        // 驳回
        return $this->getRejectStatus($currentStatus, $userMode);
    } else {
        // 通过
        return $this->getApproveStatus($currentStatus, $userMode);
    }
}
```

## 4. 权限控制系统

### 4.1 用户角色定义
| 角色ID | 角色名称 | 权限说明 |
|--------|----------|----------|
| 1 | 学生 | 项目申请、修改、提交报告 |
| 2 | 教师 | 项目审核、指导 |
| 3 | 学院管理员 | 项目审核、管理 |
| 4 | 学校管理员 | 项目审核、管理 |
| 11 | 超级管理员 | 所有权限 |

### 4.2 权限验证方法
**文件路径**: `app/BaseController.php`

```php
/**
 * 检查用户权限
 */
protected function checkPermission($permission) {
    $userMode = session('user.usermode');
    $permissions = [
        'dc_view' => [1, 2, 3, 4, 11],
        'dc_check_teacher' => [2, 11],
        'dc_check_college' => [3, 11],
        'dc_check_school' => [4, 11],
    ];
    
    return isset($permissions[$permission]) && in_array($userMode, $permissions[$permission]);
}
```

### 4.3 前端权限控制
**文件路径**: `app/view/dcmanage/detail.html`

```javascript
computed: {
    // 学生操作权限
    isStudentOperation() {
        const usermode = parseInt(this.userinfo.usermode);
        return usermode === 1 || usermode === 11;
    },
    // 学生是否可以修改项目
    canStudentEditProject() {
        const allowedStatus = [1, 2, 3, 12, 13, 14, 15, 16, 17, 18, 19, 20];
        return allowedStatus.includes(this.projectStatus);
    }
}
```

## 5. 路由配置

### 5.1 大创平台路由
**文件路径**: `route/app.php`

```php
// 大创平台路由
Route::rule('dc-addproject', '/dcmanage/addProject/addProject','GET|POST');
Route::get('dc-lists', '/dcmanage/lists/index');
Route::get('dc-detail', '/dcmanage/detail/index');
Route::post('dc-check', '/dcmanage/check/checkProject');

// 中期报告
Route::get('dc-intermproject', '/dcmanage/intermproject/index');
Route::post('dc-addinterm', '/dcmanage/intermproject/add_interm');

// 结题报告
Route::get('dc-concludeproject', '/dcmanage/concludeproject/index');
Route::post('dc-addconclude', '/dcmanage/concludeproject/add_conclude');

// 延期申请
Route::get('dc-extension', '/dcmanage/extension/index');
Route::post('dc-applyextension', '/dcmanage/extension/apply_extension');

// 中期变更
Route::get('dc-midchange', '/dcmanage/midchange/index');
Route::post('dc-applymidchange', '/dcmanage/midchange/apply_midchange');
```

## 6. 前端组件开发

### 6.1 悬浮菜单组件
**功能**: 根据用户权限和项目状态动态显示操作菜单

**权限控制逻辑**:
```javascript
// 学生操作菜单
isStudentOperation() {
    return usermode === 1 || usermode === 11;
},
// 教师操作菜单
isTeacherOperation() {
    return usermode === 2 || usermode === 11;
},
// 学院操作菜单
isCollegeOperation() {
    return usermode === 3 || usermode === 11;
},
// 学校操作菜单
isSchoolOperation() {
    return usermode === 4 || usermode === 11;
}
```

### 6.2 远程搜索组件
**功能**: 支持按姓名或学号/工号模糊搜索用户

**实现方式**:
```javascript
// 远程搜索方法
remoteSearch(query, type) {
    if (query !== '') {
        this.loading = true;
        axios.post(`bs-searchuser?type=${type}`, {
            query: query
        })
        .then(response => {
            if (response.data.status === 'success') {
                this.options = response.data.message;
            }
        })
        .finally(() => {
            this.loading = false;
        });
    }
}
```

## 7. 数据库事务处理

### 7.1 项目修改事务
```php
try {
    Db::transaction(function () use ($project, $members, $teachers) {
        // 删除原有数据
        Member::where('is_delete', 0)
            ->where('uid', $project['uid'])
            ->update(['is_delete' => 1]);
        Teacher::where('is_delete', 0)
            ->where('uid', $project['uid'])
            ->update(['is_delete' => 1]);
        
        // 插入新数据
        Member::insertAll($members);
        Teacher::insertAll($teachers);
        
        // 更新项目信息
        Dcproject::where('uid', $project['uid'])
            ->update($project);
    });
    
    return ['status' => 'success', 'message' => '修改成功'];
} catch (\Exception $e) {
    return ['status' => 'error', 'message' => '修改失败'];
}
```

### 7.2 审核事务处理
```php
try {
    Db::transaction(function () use ($uid, $project, $result) {
        // 更新项目状态
        Dcproject::where('uid', $uid)
            ->update(['status' => $result['new_status']]);
        
        // 插入审核记录
        Dccheck::insert($result['checks']);
        
        // 插入进度记录
        Dcprogress::insert($result['progress']);
    });
    
    return ['status' => 'success', 'message' => '审核成功'];
} catch (\Exception $e) {
    return ['status' => 'error', 'message' => '审核失败'];
}
```

## 8. 错误处理机制

### 8.1 异常捕获
```php
try {
    // 业务逻辑
} catch (\Exception $e) {
    LogExecution('操作失败：' . $e->getMessage());
    return ['status' => 'error', 'message' => '操作失败，请重试'];
}
```

### 8.2 日志记录
```php
/**
 * 记录执行日志
 */
function LogExecution($message) {
    $log = [
        'time' => date('Y-m-d H:i:s'),
        'user' => session('user.name') ?? 'unknown',
        'message' => $message
    ];
    
    // 写入日志文件
    file_put_contents(
        runtime_path() . 'log/execution.log',
        json_encode($log) . "\n",
        FILE_APPEND
    );
}
```

## 9. 测试用例

### 9.1 项目立项测试
```php
// 测试项目立项
public function testProjectCreation() {
    $data = [
        'name' => '测试项目',
        'type' => 1,
        'period' => 1,
        'level' => 1,
        'introduction' => '项目简介',
        'reason' => '申请理由',
        'innovation' => '创新点',
        'schedule' => '进度安排',
        'budget' => 1000,
        'plan' => '经费计划'
    ];
    
    $response = $this->post('dc-addproject', ['data' => $data]);
    $this->assertEquals('success', $response['status']);
}
```

### 9.2 审核流程测试
```php
// 测试审核流程
public function testCheckProcess() {
    $checkData = [
        'check' => [
            'check' => 0, // 通过
            'remark' => '审核通过'
        ]
    ];
    
    $response = $this->post('dc-check?uid=test_uid', $checkData);
    $this->assertEquals('success', $response['status']);
}
```

## 10. 部署说明

### 10.1 环境要求
- PHP >= 8.0
- MySQL >= 5.7
- Apache/Nginx
- Composer

### 10.2 安装步骤
1. 克隆项目代码
2. 安装依赖：`composer install`
3. 配置数据库连接
4. 导入数据库结构
5. 配置Web服务器
6. 设置文件权限

### 10.3 配置文件
**数据库配置**: `config/database.php`
```php
'mysql' => [
    'hostname' => env('DB_HOST', '127.0.0.1'),
    'database' => env('DB_NAME', 'cxcysys'),
    'username' => env('DB_USER', 'cxcysys'),
    'password' => env('DB_PASS', 'cxcysys'),
    'hostport' => env('DB_PORT', '3306'),
    'charset' => env('DB_CHARSET', 'utf8mb4'),
]
```

## 11. 性能优化

### 11.1 数据库优化
- 为常用查询字段添加索引
- 使用软删除避免数据丢失
- 合理使用事务保证数据一致性

### 11.2 前端优化
- 使用Vue.js组件化开发
- 实现远程搜索减少数据传输
- 合理使用缓存机制

### 11.3 缓存策略
- 使用Redis缓存用户会话
- 缓存常用配置信息
- 实现查询结果缓存

## 12. 安全措施

### 12.1 权限验证
- 所有操作都需要验证用户权限
- 使用中间件进行权限控制
- 前后端双重验证

### 12.2 数据安全
- 使用参数化查询防止SQL注入
- 对用户输入进行验证和过滤
- 敏感数据加密存储

### 12.3 文件上传安全
- 限制文件类型和大小
- 使用安全的文件存储路径
- 验证文件内容

## 13. 维护指南

### 13.1 日志管理
- 定期清理日志文件
- 监控系统运行状态
- 记录重要操作日志

### 13.2 数据备份
- 定期备份数据库
- 备份重要配置文件
- 建立数据恢复机制

### 13.3 系统监控
- 监控服务器资源使用
- 监控数据库性能
- 设置告警机制

---

**文档版本**: v1.0  
**最后更新**: 2025-01-13  
**维护人员**: 开发团队 