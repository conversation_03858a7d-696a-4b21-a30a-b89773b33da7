<?php

namespace app\middleware;

use app\service\PermissionService;
use think\facade\Request;

class CheckPermission
{
    /**
     * 权限检查中间件
     * @param \think\Request $request
     * @param \Closure $next
     * @param string $permission 权限标识
     * @return mixed
     */
    public function handle($request, \Closure $next, $permission = '')
    {
        // 如果没有指定权限，直接通过
        if (empty($permission)) {
            return $next($request);
        }

        // 检查用户是否有指定权限
        if (!PermissionService::hasPermission($permission)) {
            LogExecution('权限不足：' . $permission);
            return json(['status' => 'error', 'message' => '权限不足']);
        }

        return $next($request);
    }
} 