<?php
//检查登录
namespace app\middleware;
use think\facade\Request;
use think\facade\Session;


class CheckLogin
{
    public function handle($request, \Closure $next)
    {
        // 定义登录页面的路径
        $loginPath = '/login';
//        echo $request->url();
        // 检查当前请求是否是登录页面
        if ($request->url() == $loginPath) {
            return $next($request);
        }
        if ($request->url() == '/index'||$request->url() == '/'||$request->url() == '/index/index') {
            return $next($request);
        }
        // 检查会话中是否有用户信息
        if (!session('user.username')) {
            // 用户未登录，重定向到登录页面
            return redirect($loginPath);
        }
        // 用户已登录，继续执行下一个中间件或请求
        return $next($request);
    }
}