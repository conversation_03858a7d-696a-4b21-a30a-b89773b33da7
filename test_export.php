<?php
require_once 'vendor/autoload.php';

// 初始化应用
$app = new \think\App();
$app->initialize();

use app\model\Department;
use app\model\Major;
use app\service\ExportService;

echo "=== 测试部门导出功能 ===\n";

// 获取部门数据
$departments = Department::where('is_delete', 0)->select();
echo "找到 " . count($departments) . " 个部门\n";

$result = [];
foreach ($departments as $dept) {
    $majors = Major::where('department_id', $dept->id)
        ->where('is_delete', 0)
        ->select();
    
    echo "部门: {$dept->name} (ID: {$dept->id}) - 专业数量: " . count($majors) . "\n";
    
    foreach ($majors as $major) {
        echo "  - 专业: {$major->name} (ID: {$major->id})\n";
    }
    
    $result[] = [
        'id' => $dept->id,
        'name' => $dept->name,
        'created_at' => $dept->created_at,
        'updated_at' => $dept->updated_at,
        'majors' => $majors
    ];
}

// 测试格式化
$exportService = new ExportService();
$formattedData = $exportService->formatDepartmentData($result);

echo "\n=== 格式化后的数据 ===\n";
foreach ($formattedData as $row) {
    echo "部门: {$row[0]}, 专业: '{$row[1]}', 创建时间: {$row[2]}\n";
}

// 调试软件学院的数据
echo "\n=== 调试软件学院数据 ===\n";
$softwareDept = null;
foreach ($result as $dept) {
    if ($dept['name'] === '软件学院') {
        $softwareDept = $dept;
        break;
    }
}

if ($softwareDept) {
    echo "软件学院 majors 类型: " . gettype($softwareDept['majors']) . "\n";
    echo "软件学院 majors 内容: " . print_r($softwareDept['majors'], true) . "\n";
    
    if (is_array($softwareDept['majors'])) {
        foreach ($softwareDept['majors'] as $major) {
            echo "专业对象类型: " . gettype($major) . "\n";
            echo "专业对象内容: " . print_r($major, true) . "\n";
            if (is_object($major)) {
                echo "专业名称: " . $major->name . "\n";
            } elseif (is_array($major)) {
                echo "专业名称: " . $major['name'] . "\n";
            }
        }
    }
}

echo "\n=== 测试完成 ===\n"; 