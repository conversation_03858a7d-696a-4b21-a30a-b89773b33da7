{include file="public_index/_header"}

<link rel="stylesheet" href="../../static/css/index/newsclass.css">
<div id="app">
  <p style="margin: 1.5rem 0 1.5rem 5%;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="/index">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/news-class?class=2">新闻管理</a>
</p>
  <template>
    <el-row class="news-container" :gutter="20">
      <!-- 左侧导航 -->
      <el-col 
        :xs="24" 
        :sm="24" 
        :md="6" 
        :lg="4" 
        :xl="4"
        class="category-col"
      >

      {foreach $classes as $class}
        <div 
          class="category-item"
          :class="{ 'active': currentCategory === {$class.id } }"
          @click="switchCategory({$class.id })"
        >
        {$class.name }
        </div>
        {/foreach}
      </el-col>
  
      <!-- 右侧内容 -->
      <el-col 
        :xs="24" 
        :sm="24" 
        :md="18" 
        :lg="20" 
        :xl="20"
        class="content-col"
        style="position: relative;"
      >
        <transition name="fade-slide" mode="out-in">
          <el-table
            :key="currentCategory"
            :data="paginatedNews"
            style="width: 100%"
            class="news-table"
            v-loading="loading"
          >
            <el-table-column
              prop="title"
              label="新闻标题"
              min-width="260"
            >
              <template slot-scope="{ row }">
                <el-link 
                  :underline="false" 
                  class="news-title"
                  @click="viewDetail(row)"
                >
                  {{ row.title }}
                </el-link>
              </template>
            </el-table-column>
  
            <el-table-column
              prop="time"
              label="发布时间"
              width="180"
              align="center"
            >
              <template slot-scope="{ row }">
                <div class="time-badge">
                  {{ formatTime(row.time) }}
                </div>
              </template>
            </el-table-column>
  
          </el-table>

        </transition>
        <el-pagination
        style="position: absolute;bottom: -10%;right: 0;"
        background
        layout="prev, pager, next, sizes, jumper"
        :current-page="currentPage"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        style="margin-top: 20px"
      />
      </el-col>
    </el-row>
  </template>
  

  </div>
    <script src="../../static/js/index/newsclass.js"></script>
    <script>
      app.newslist={:json_encode($news)};
    </script>
    {include file="public_index/_footer"}
