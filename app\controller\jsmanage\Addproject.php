<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Dcexpected;
use app\model\Dcperiod;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Dctype;
use app\model\File;
use app\model\Jscompetition;
use app\model\Jsprogress;
use app\model\Jsproject;
use app\model\Member;
use app\model\Teacher;
use app\validate\CheckAddDc;
use Exception;
use Ramsey\Uuid\Uuid;
use think\facade\Db;

class Addproject extends BaseController
{
    public function editProject($uid){
        $data=input('post.data');
        //根据uid查cuid
        $project=Jsproject::alias('p')
            ->join('jssetting s','s.cuid=p.cuid','LEFT')
            ->where('p.is_delete',0)
            ->where('p.uid',$uid)
            ->field('s.active,p.cuid,p.uid,p.name,p.introduction,p.time,class')
            ->find();
        $cuid=$project['cuid'];
        $competition = Jscompetition::alias('c')
            ->field('
                    c.cuid,s.active,c.name,s.years,
                    l.name as l_level,
                    s.*
                ')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->join('jssetting s','s.cuid=c.cuid','LEFT')
            ->where('c.is_delete', 0)
            ->where('c.cuid',$cuid)
            ->find();

            // 只有普通用户需要检查填报时间，超级管理员可以随时修改
            if (session('user.usermode') != 11) {
                if (!$competition['active']){
                    return json(['status'=>'error','message' => '当前不是'.$competition['name'].'填报时间']);
                }
            }
            $member=Member::alias('m')
                ->join('user u', 'u.username=m.username','LEFT')
                ->where('m.is_delete',0)
                ->where('u.status',0)
                ->where('m.uid',$uid)
                ->where('m.rank','!=',1)
                ->field('u.name as label,m.username as value')
                ->join('department d','d.id=u.college','LEFT')
                ->join('major mj','mj.id=u.major','LEFT')
                ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $teacher=Teacher::alias('t')
                ->join('user u', 'u.username=t.username','LEFT')
                ->where('t.is_delete',0)
                ->where('u.status',0)
                ->where('t.uid',$uid)
                ->where('t.type',0)
                ->field('t.username as value,u.name as label')
                ->join('department d','d.id=u.college','LEFT')
                ->join('major m','m.id=u.major','LEFT')
                ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $outstudent=Member::alias('m')
                ->where('m.is_delete',0)
                ->where('m.uid',$uid)
                ->where('m.type',1)
                ->field('m.name,m.unit,m.phone,m.email')
                ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $outteacher=Teacher::alias('t')
                ->where('t.is_delete',0)
                ->where('t.uid',$uid)
                ->where('t.type',1)
                ->field('t.name,t.unit,t.job,t.phone,t.email')
                ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $files=File::where('is_delete',0)->where('uid',$uid)->field('name,path as url')->select();
        $project['fileurls']=[];
            
            if (session('user.usermode') == 11) {
                LogExecution('超级管理员进入竞赛项目修改页');
            } else {
                LogExecution('进入竞赛项目修改页');
            }
            
            return view('jsmanage/addProject',[
                'competition'=>$competition,
                'project'=>$project,
                'members'=>$member,
                'teachers'=>$teacher,
                'outstudent'=>$outstudent,
                'outteacher'=>$outteacher,
                'files'=>$files
            ]);
    }
    public function addProject($cuid='')
    {
        $data=input('post.data');
        if ($cuid&&empty($data)){
            //新增页面
            $competition = Jscompetition::alias('c')
                ->field('
                    c.cuid,s.active,c.name,s.years,
                    l.name as l_level,
                    s.*
                ')
                ->join('jslevel l','l.id=c.level','LEFT')
                ->join('jssetting s','s.cuid=c.cuid','LEFT')
                ->where('c.is_delete', 0)
                ->where('c.cuid',$cuid)
                ->find();
            if (!$competition['active']){
                return json(['status'=>'error','message' => '当前不是'.$competition['name'].'填报时间']);
            }
//            进入立项页面
            LogExecution('进入竞赛项目立项页');
            return view('jsmanage/addProject',['competition'=>$competition]);

        }else{
            $type='';
            if($cuid === 'null'&&$data['uid']){
                $type='update';
                //修改项目
                $cuid=$data['cuid'];
                $competition = Jscompetition::alias('c')
                    ->field('
                    c.cuid,s.active,c.name,s.years,
                    l.name as l_level,
                    s.*
                ')
                    ->join('jslevel l','l.id=c.level','LEFT')
                    ->join('jssetting s','s.cuid=c.cuid','LEFT')
                    ->where('c.is_delete', 0)
                    ->where('c.cuid',$cuid)
                    ->find();
                // 检查是否为超级管理员
                if (session('user.usermode') == 11) {
                    LogExecution('超级管理员提交竞赛项目修改信息');
                } else {
                    LogExecution('用户提交竞赛项目修改信息');
                }
                
                $old_project=Jsproject::where('is_delete',0)->where('uid',$data['uid'])->find();
                if (!$old_project){
                    if (session('user.usermode') == 11) {
                        LogExecution('超级管理员竞赛项目不存在');
                        return json(['status'=>'error','message' => '超级管理员竞赛项目不存在']);
                    } else {
                        LogExecution('竞赛项目不存在');
                        return json(['status'=>'error','message' => '竞赛项目不存在']);
                    }
                }
                
                // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
                if (session('user.usermode') != 11) {
                    $current_status = $old_project['status'];
                    if ($current_status!=1&&$current_status!=9&&$current_status!=10&&$current_status!=11){
                        LogExecution('当前不是可修改立项信息状态');
                        return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
                    }
                } else {
                    LogExecution('超级管理员可以修改任意状态的竞赛项目');
                }

                $class='修改立项';
                $uid=$data['uid'];
            }elseif ($cuid&&!isset($data['uid'])){
                $type='add';

                //项目立项
                $competition = Jscompetition::alias('c')
                    ->field('
                    c.cuid,s.active,c.name,s.years,
                    l.name as l_level,
                    s.*
                ')
                    ->join('jslevel l','l.id=c.level','LEFT')
                    ->join('jssetting s','s.cuid=c.cuid','LEFT')
                    ->where('c.is_delete', 0)
                    ->where('c.cuid',$cuid)
                    ->find();
                
                if (session('user.usermode') == 11) {
                    LogExecution('超级管理员提交竞赛项目立项信息');
                } else {
                    LogExecution('用户提交竞赛项目立项信息');
                }
                $class='修改立项';
                $uid=Uuid::uuid4()->toString();
            }

            // 确保 $competition 变量已定义，如果没有则重新查询
            if (!isset($competition)) {
                $competition = Jscompetition::alias('c')
                    ->field('
                    c.cuid,s.active,c.name,s.years,
                    l.name as l_level,
                    s.*
                ')
                    ->join('jslevel l','l.id=c.level','LEFT')
                    ->join('jssetting s','s.cuid=c.cuid','LEFT')
                    ->where('c.is_delete', 0)
                    ->where('c.cuid',$cuid)
                    ->find();
            }

            if (!$competition) {
                LogExecution($cuid.'竞赛不存在');
                return json(['status'=>'error','message' => '竞赛不存在']);
            }

            if (!$competition['active']){
                return json(['status'=>'error','message' => '当前不是'.$competition['name'].'填报时间']);
            }
            
            // 所有用户都可以申请竞赛（竞赛不按学院分类）
            //判断数据校验以后写，这里先添加竞赛信息
            $project=[
                'uid'=>$uid,
                'cuid'=>$competition['cuid'],
                'name'=>$data['name'],
                'time'=>$data['time'],
                'introduction'=>$data['introduction'],
                'class'=>$data['class'],
                'status'=>1
            ];
            $time=$project['time'];
            $years=json_decode($competition['years'], true);
            // 从日期字符串中提取年份
            $yearFromDate = (int)date("Y", strtotime($time));
            // 检查年份是否在数组中
            if (!in_array($yearFromDate, $years)) {
                LogExecution($time.'您的立项时间不在可填报区间内');
                return json(['status'=>'error','message' => '您的立项时间不在可填报区间内']);
            }
            $files=[];
            foreach ($data['fileurls'] as $file) {
                $files[] = [
                    'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                    'path'=>$file['url'],
                    'name'=>$file['name'],
                ];
            }
            //学生和教师
            $members=[];
            //添加负责人
            $rank=1;
            if (session('user.usermode') == 11) {
                // 超级管理员修改时，保持原有的项目负责人
                if ($type == 'update') {
                    $old_member=Member::where('is_delete',0)->where('uid',$data['uid'])->where('rank',1)->where('dc',0)->find();
                    if ($old_member) {
                        $members[]=[
                            'uid'=>$data['uid'],
                            'username'=>$old_member['username'],
                            'rank'=>$rank,
                            'type'=>0,
                            'dc'=>0
                        ];
                    } else {
                        // 如果没有找到原有负责人，使用第一个成员作为负责人
                        if (!empty($data['members'])) {
                            $members[]=[
                                'uid'=>$data['uid'],
                                'username'=>$data['members'][0],
                                'rank'=>$rank,
                                'type'=>0,
                                'dc'=>0
                            ];
                        }
                    }
                } else {
                    // 新增项目时，使用第一个成员作为负责人
                    if (!empty($data['members'])) {
                        $members[]=[
                            'uid'=>$project['uid'],
                            'username'=>$data['members'][0],
                            'rank'=>$rank,
                            'type'=>0,
                            'dc'=>0
                        ];
                    }
                }
            } else {
                // 普通用户修改时，使用当前登录用户作为负责人
                $members[]=[
                    'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                    'username'=>session('user.username'),
                    'rank'=>$rank,
                    'type'=>0,
                    'dc'=>0
                ];
            }
            
            foreach ($data['members'] as $member){
                if (session('user.usermode') == 11) {
                    if ($type == 'update') {
                        // 超级管理员修改时，跳过负责人（已经在上面添加了）
                        $old_member=Member::where('is_delete',0)->where('uid',$data['uid'])->where('rank',1)->where('dc',0)->find();
                        if ($old_member && $member==$old_member['username']){
                            continue;
                        }
                    } else {
                        // 新增项目时，跳过第一个成员（已经在上面添加了）
                        if ($member==$data['members'][0]){
                            continue;
                        }
                    }
                } else {
                    // 普通用户修改时，跳过当前登录用户
                    if ($member==session('user.username')){
                        continue;
                    }
                }
                $rank++;
                $members[]=[
                    'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                    'username'=>$member,
                    'rank'=>$rank,
                    'type'=>0,
                    'dc'=>0
                ];
            }
            if (count($members)>$competition['member']){
                LogExecution($cuid.'项目校内成员数超出限制');
                return json(['status'=>'error','message' => '项目校内成员数超出限制']);
            }
            $teachers=[];
            $rank=0;
            //添加校内指导教师
            foreach ($data['teachers'] as $teacher){
                $rank++;
                $teachers[]=[
                    'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                    'username'=>$teacher,
                    'rank'=>$rank,
                    'type'=>0,
                    'dc'=>0
                ];
            }
            if (count($teachers)>$competition['teacher']){
                LogExecution($cuid.'项目校内指导教师数超出限制');
                return json(['status'=>'error','message' => '项目校内指导教师数超出限制']);
            }
            $outstus=[];
            $outteas=[];

            //检查校外教师和学生
            if ($competition['outstu']){
                $rank=0;

                foreach ($data['outstu'] as $outstu){
                    $rank++;
                    $outstus[]=[
                        'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                        'rank'=>$rank,
                        'type'=>1, // 校外学生类型为1
                        'dc'=>0,
                        'name'=>$outstu['name'],
                        'unit'=>$outstu['unit'],
                        'email'=>$outstu['email'],
                        'phone'=>$outstu['phone']
                    ];
                }
            }
            if ($competition['outtea']){
                $rank=0;

                foreach ($data['outtea'] as $outtea){
                    $rank++;
                    $outteas[]=[
                        'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                        'rank'=>$rank,
                        'type'=>1, // 校外教师类型为1
                        'dc'=>0,
                        'name'=>$outtea['name'],
                        'unit'=>$outtea['unit'],
                        'email'=>$outtea['email'],
                        'phone'=>$outtea['phone'],
                        'job'=>$outtea['job'],

                    ];
                }
            }
            if (count($outstus)>$competition['outstu_num']){
                LogExecution($cuid.'项目校外成员数超出限制');
                return json(['status'=>'error','message' => '项目校外成员数超出限制']);
            }
            if (count($outteas)>$competition['outtea_num']){
                LogExecution($cuid.'项目校外指导教师数超出限制');
                return json(['status'=>'error','message' => '项目校外指导教师数超出限制']);
            }

            //添加项目进度
            if (session('user.usermode') == 11) {
                if ($type == 'update') {
                    $progress=[
                        'uid'=>$data['uid'],
                        'action'=>'超级管理员修改竞赛项目',
                        'remark'=>'超级管理员'.session('user.name').'修改竞赛项目'
                    ];
                } else {
                    $progress=[
                        'uid'=>$project['uid'],
                        'action'=>'超级管理员竞赛项目立项',
                        'remark'=>'超级管理员'.session('user.name').'提交竞赛项目立项'
                    ];
                }
            } else {
                $progress=[
                    'uid'=>$type=='update' ? $data['uid'] : $project['uid'],
                    'action'=>$class.'申请',
                    'remark'=>'学生提交'.$class.'申请'
                ];
            }
            if($type=='update'){
                //修改
                //删除之前的文件
                $new_status=1;
                $old=Jsproject::where('is_delete',0)->where('uid',$data['uid'])->find();
                
                // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
                if (session('user.usermode') != 11) {
                    if ($old['status']!=1&&$old['status']!=9&&$old['status']!=10&&$old['status']!=11){
                        LogExecution($data['uid'] . '竞赛修改失败，项目当前不是可修改状态');
                        return ['status' => 'error', 'message' => '修改立项失败，项目当前不是可修改状态'];
                    }
                }
                
                if ($old['status']==9){
                    $new_status=1;
                }elseif ($old['status']==10){
                    $new_status=2;
                }elseif ($old['status']==11){
                    $new_status=3;
                }
                $project['status']=$new_status;
                $project['uid'] = $data['uid']; // 确保使用正确的UID

                // 开始事务
                Db::startTrans();
                try {
                    // 删除之前的成员记录
                    Member::where('is_delete', 0)->where('uid', $data['uid'])->update(['is_delete' => 1]);
                    Teacher::where('is_delete', 0)->where('uid', $data['uid'])->update(['is_delete' => 1]);

                    // 处理文件：只有当有新文件时才删除旧文件
                    $fileInsertResult = true; // 默认为成功
                    if (!empty($files)) {
                        // 有新文件时，删除旧文件并插入新文件
                        File::where('is_delete', 0)->where('uid', $data['uid'])->update(['is_delete' => 1]);
                        $fileInsertResult = File::insertAll($files);
                    }
                    // 如果没有新文件，保留原有文件，不进行任何操作
                    
                    // 进度记录插入
                    try {
                        if (!empty($progress)) {
                            $progressInsertResult = Jsprogress::insert($progress);
                        } else {
                            $progressInsertResult = true;
                            LogExecution('进度记录为空，跳过进度记录插入');
                        }
                    } catch (Exception $e) {
                        LogExecution('进度记录插入失败：' . $e->getMessage());
                        $progressInsertResult = false;
                    }
                    
                    // 成员插入
                    try {
                        if (!empty($members)) {
                            $memberInsertResult = Member::insertAll($members);
                        } else {
                            $memberInsertResult = true;
                            LogExecution('成员数组为空，跳过成员插入');
                        }
                    } catch (Exception $e) {
                        LogExecution('成员插入失败：' . $e->getMessage());
                        $memberInsertResult = false;
                    }
                    
                    // 教师插入
                    try {
                        if (!empty($teachers)) {
                            $teacherInsertResult = Teacher::insertAll($teachers);
                        } else {
                            $teacherInsertResult = true;
                            LogExecution('教师数组为空，跳过教师插入');
                        }
                    } catch (Exception $e) {
                        LogExecution('教师插入失败：' . $e->getMessage());
                        $teacherInsertResult = false;
                    }

                    // 更新项目信息
                    $updateData = [
                        'name' => $project['name'],
                        'time' => $project['time'],
                        'introduction' => $project['introduction'],
                        'class' => $project['class'],
                        'status' => $project['status']
                    ];
                    
                    // 检查是否有实际修改（在更新之前检查）
                    $hasProjectChanges = false;
                    $hasOtherChanges = false;
                    $oldProject = Jsproject::where('is_delete', 0)->where('uid', $data['uid'])->find();
                    if ($oldProject) {
                        foreach ($updateData as $key => $value) {
                            if ($oldProject[$key] != $value) {
                                $hasProjectChanges = true;
                                LogExecution('检测到项目信息变化：' . $key . ' 从 ' . $oldProject[$key] . ' 改为 ' . $value);
                                break;
                            }
                        }
                    }
                    
                    // 检查是否有其他变化（文件、成员、教师等）
                    if (!empty($files)) {
                        $hasOtherChanges = true;
                        LogExecution('检测到文件变化');
                    }
                    
                    if (!empty($members)) {
                        $hasOtherChanges = true;
                        LogExecution('检测到成员变化');
                    }
                    
                    if (!empty($teachers)) {
                        $hasOtherChanges = true;
                        LogExecution('检测到教师变化');
                    }
                    
                    if (!empty($outstus)) {
                        $hasOtherChanges = true;
                        LogExecution('检测到校外学生变化');
                    }
                    
                    if (!empty($outteas)) {
                        $hasOtherChanges = true;
                        LogExecution('检测到校外教师变化');
                    }
                    
                    $hasChanges = $hasProjectChanges || $hasOtherChanges;
                    
                    // 只有在项目信息有实际变化时才执行更新
                    if ($hasProjectChanges) {
                        $projectUpdateResult = Jsproject::where('is_delete', 0)->where('uid', $data['uid'])->update($updateData);
                        LogExecution('项目信息有变化，执行更新操作');
                    } else {
                        $projectUpdateResult = true;
                        LogExecution('项目信息没有实际修改，跳过更新操作');
                    }
                    
                    // 处理校外学生和教师
                    $outstusInsertResult = true;
                    $outteasInsertResult = true;
                    if (!empty($outstus)) {
                        try {
                            $outstusInsertResult = Member::insertAll($outstus);
                        } catch (Exception $e) {
                            LogExecution('校外学生插入失败：' . $e->getMessage());
                            $outstusInsertResult = false;
                        }
                    }
                    if (!empty($outteas)) {
                        try {
                            $outteasInsertResult = Teacher::insertAll($outteas);
                        } catch (Exception $e) {
                            LogExecution('校外教师插入失败：' . $e->getMessage());
                            $outteasInsertResult = false;
                        }
                    }

                    // 记录调试信息
                    LogExecution('调试信息 - 项目数据: ' . json_encode($project));
                    LogExecution('调试信息 - 成员数据: ' . json_encode($members));
                    LogExecution('调试信息 - 教师数据: ' . json_encode($teachers));
                    LogExecution('调试信息 - 校外学生数据: ' . json_encode($outstus));
                    LogExecution('调试信息 - 校外教师数据: ' . json_encode($outteas));
                    LogExecution('调试信息 - fileInsertResult: ' . ($fileInsertResult ? 'true' : 'false'));
                    LogExecution('调试信息 - progressInsertResult: ' . ($progressInsertResult ? 'true' : 'false'));
                    LogExecution('调试信息 - memberInsertResult: ' . ($memberInsertResult ? 'true' : 'false'));
                    LogExecution('调试信息 - teacherInsertResult: ' . ($teacherInsertResult ? 'true' : 'false'));
                    LogExecution('调试信息 - projectUpdateResult: ' . ($projectUpdateResult ? 'true' : 'false'));
                    LogExecution('调试信息 - outstusInsertResult: ' . ($outstusInsertResult ? 'true' : 'false'));
                    LogExecution('调试信息 - outteasInsertResult: ' . ($outteasInsertResult ? 'true' : 'false'));

                    // 检查所有操作是否成功
                    if ($fileInsertResult && $progressInsertResult && $memberInsertResult && $teacherInsertResult && $projectUpdateResult && $outstusInsertResult && $outteasInsertResult) {
                        // 提交事务
                        Db::commit();

                        // 记录成功日志并返回成功信息
                        $successMessage = 'js-projectdetail?uid=' . $data['uid'];
                        if (!$hasChanges) {
                            $successMessage = '项目信息未发生变化';
                            LogExecution('项目信息未发生变化，显示提示信息');
                        } else {
                            LogExecution('项目信息有变化，准备跳转到详情页');
                        }
                        
                        if (session('user.usermode') == 11) {
                            LogExecution($data['uid'] . '超级管理员修改竞赛项目成功');
                            return ['status' => 'success', 'message' => $successMessage];
                        } else {
                            LogExecution($data['uid'] . '修改竞赛立项成功');
                            return ['status' => 'success', 'message' => $successMessage];
                        }
                    } else {
                        // 回滚事务
                        Db::rollback();

                        // 记录具体哪个操作失败了
                        $failedOperations = [];
                        if (!$fileInsertResult) $failedOperations[] = '文件插入';
                        if (!$progressInsertResult) $failedOperations[] = '进度记录插入';
                        if (!$memberInsertResult) $failedOperations[] = '成员插入';
                        if (!$teacherInsertResult) $failedOperations[] = '教师插入';
                        if (!$projectUpdateResult) $failedOperations[] = '项目更新';
                        if (!$outstusInsertResult) $failedOperations[] = '校外学生插入';
                        if (!$outteasInsertResult) $failedOperations[] = '校外教师插入';
                        
                        $errorMsg = '修改立项失败，以下操作未完成：' . implode(', ', $failedOperations);
                        LogExecution($data['uid'] . ' - ' . $errorMsg);
                        throw new Exception($errorMsg);
                    }
                } catch (Exception $e) {
                    // 捕获异常，记录失败日志并返回错误信息
                    $errorMessage = '修改立项失败：' . $e->getMessage();
                    if (session('user.usermode') == 11) {
                        LogExecution($data['uid'] . '超级管理员修改竞赛项目失败：' . $e->getMessage());
                        return ['status' => 'error', 'message' => $errorMessage];
                    } else {
                        LogExecution($data['uid'] . '修改竞赛立项失败：' . $e->getMessage());
                        return ['status' => 'error', 'message' => $errorMessage];
                    }
                }




            }elseif ($type=='add'){
                try {
                    // 开启事务
                    Db::startTrans();

                    // 尝试执行每个插入操作
                    $projectInsertResult = Jsproject::insert($project); // 注意：这里使用了 insertGetId，如果您不需要获取ID，可以使用 insert
                    $fileInsertResult = File::insertAll($files);
                    $progressInsertResult = Jsprogress::insert($progress);
                    $memberInsertResult = Member::insertAll($members);
                    $teacherInsertResult = Teacher::insertAll($teachers);

                    // 检查前面的插入操作是否都成功
                    if ($projectInsertResult && $fileInsertResult && $progressInsertResult && $memberInsertResult && $teacherInsertResult) {
                        // 插入校外指导教师和学生
                        $outstusInsertResult = Member::insertAll($outstus);
                        $outteasInsertResult = Teacher::insertAll($outteas);

                        // 检查校外指导教师和学生的插入操作是否成功
                        if ($outstusInsertResult && $outteasInsertResult) {
                            // 如果所有操作成功，提交事务
                            Db::commit();

                            // 记录成功日志并返回成功信息
                            if (session('user.usermode') == 11) {
                                LogExecution($project['uid'] . '超级管理员竞赛立项成功');
                                return ['status' => 'success', 'message' => '超级管理员竞赛立项成功'];
                            } else {
                                LogExecution($project['uid'] . '竞赛立项成功');
                                return ['status' => 'success', 'message' => '竞赛立项成功'];
                            }
                        } else {
                            // 如果校外指导教师或学生的插入操作失败，回滚事务
                            Db::rollback();

                            // 抛出异常（这将在 catch 块中被捕获）
                            throw new Exception('立项失败：校外指导教师或学生信息插入失败');
                        }
                    } else {
                        // 如果前面的插入操作中有任何一个失败，回滚事务
                        Db::rollback();

                        // 抛出异常（这将在 catch 块中被捕获）
                        throw new Exception('立项失败：部分操作未完成');
                    }
                } catch (Exception $e) {
                    // 捕获异常，回滚事务（如果在 try 块中已经开始事务但尚未提交，则此回滚是多余的，但为了安全起见还是加上）
                    Db::rollback();

                    // 记录失败日志并返回错误信息
                    if (session('user.usermode') == 11) {
                        LogExecution($project['uid'] . '超级管理员竞赛立项失败，请检查环境及填写内容：' . $e->getMessage());
                        return ['status' => 'error', 'message' => '超级管理员竞赛立项失败，请检查环境及填写内容'];
                    } else {
                        LogExecution($project['uid'] . '竞赛立项失败，请检查环境及填写内容：' . $e->getMessage());
                        return ['status' => 'error', 'message' => '立项失败，请检查环境及填写内容'];
                    }
                }
            }
        }

        return 0;
        }


}