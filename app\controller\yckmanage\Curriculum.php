<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\User;
use app\model\Yckblacklist;
use app\model\Yckcontactlog;
use app\model\Yckcurriculum;
use app\model\Yckproject;
use app\model\Yckuser;

class Curriculum extends BaseController
{
    public function index($username){
        LogExecution(session('user.username').'进入英才库看'.$username);
        if (!Yckuser::where('username',$username)->where('is_delete',0)->where('status',2)->find()){
            LogExecution(session('user.username').'进入英才库看'.$username.'用户不是英才');
            return json(['status' => 'error', 'message' => $username.'用户不在英才库中']);
        }
        return view('yckmanage/curriculum',['username'=>$username]);
    }
    public function update (){
        LogExecution(session('user.username').'修改简历');
        if (!Yckuser::where('username',session('user.username'))->where('is_delete',0)->where('status',2)->find()){
            LogExecution(session('user.username').'修改简历但是不是英才');
            return json(['status' => 'error', 'message' => '您不在英才库中']);
        }
        $data=input('post.data');
        $curriculum=[
            'username'=>session('user.username'),
        ];
        $project=[];
        foreach ($data['show'] as $show){
            if ($show=='name'||$show=='email'||$show=='rate'){
                //必须有的直接跳过，数据库根本没存这些字段
                continue;
            }elseif ($show=='job'||$show=='intro'||$show=='phone'||$show=='qq'||$show=='wx'||$show=='dd'||$show=='qywx'||$show=='tags'||$show=='domains'){
                //简历表
                $curriculum[$show]=1;
            }else{
                //展示的项目表
                array_push($project,$show);
            }
        }
//        return $project;
        if (!count($project)){
            LogExecution(session('user.username').'请至少选择一项竞赛或大创项目');
            return json(['status' => 'error', 'message' => '请至少选择一项竞赛或大创项目']);
        }
        //先删除一波
        Yckproject::where('is_delete',0)->where('username',session('user.username'))->update(['show'=>0]);
        Yckcurriculum::where('is_delete',0)->where('username',session('user.username'))->update(['is_delete'=>1]);


        if (
            Yckproject::where('is_delete',0)->whereIn('uid', $project)->where('username',session('user.username'))->update(['show'=>1])
            &&
            Yckcurriculum::insert($curriculum)

        ){
            LogExecution(session('user.username').'修改简历成功');
            return json(['status' => 'success', 'message' => '修改简历成功']);
        }else{
            LogExecution(session('user.username').'修改简历无修改插入数据异常');
            return json(['status' => 'error', 'message' => '无修改']);
        }

    }
    public function seecontact($username){
        // 获取当天的日期
        $today = date('Y-m-d');
        // 获取当天的开始时间和结束时间
        $startOfDay = strtotime($today . ' 00:00:00'); // 当天00:00:00
        $endOfDay = strtotime($today . ' 23:59:59');   // 当天23:59:59
        //是否自己或者今日已经查过
        if (
            $username!=session('user.username')
            ||
            Yckcontactlog::where('is_delete',0)->where('username',session('user.username'))->where('target',$username)->whereBetween('created_at', [$startOfDay, $endOfDay])->find()
        ){

            //去查询今日是否有次数
            if (Yckcontactlog::where('is_delete',0)->where('username',session('user.username'))->where('target',$username)->whereBetween('created_at', [$startOfDay, $endOfDay])->count()==3){
                return json(['status' => 'error', 'message' => '您今日没有查看次数了']);
            }
            //查看是否在黑名单内
            if (Yckblacklist::where('is_delete',0)->where('target',session('user.username'))->where('username',$username)->whereBetween('created_at', [$startOfDay, $endOfDay])->find()){
                return json(['status' => 'error', 'message' => '联系方式查询失败']);
            }
            //只有其他人每日第一次查看才记录次数，防止刷票
             $log=Yckcontactlog::insert([
                 'username'=>session('user.username'),
                 'target'=>$username
             ]);
            if (!$log){
                return json(['status' => 'error', 'message' => '查询失败']);
            }
        }

        $query = Yckcurriculum::
            where('is_delete', 0)
            ->where('username', $username)
            ->find();

        $field=[];

        if ($query) {
            // 检查每个字段是否为 1，并相应地添加到 $field 数组中
            $ys = ['wx', 'qq', 'dd', 'qywx'];
            $us = ['email'];

            foreach ($ys as $y) {
                if ($query->$y == 1) {
                    array_push($field, "y.{$y}");
                }
            }
            foreach ($us as $u) {
                if ($query->$u == 1) {
                    array_push($field, "u.{$u}");
                }
            }
        }
//        return $field;


        $contact=Yckuser::alias('y')
            ->where('y.is_delete',0)
            ->where('u.status',0)
            ->where('y.username',$username)
            ->field($field)
            ->join('user u','y.username=u.username','LEFT')
            ->find();
        return json(['status' => 'success', 'message' => $contact]);
    }
}