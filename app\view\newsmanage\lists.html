{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/news/lists.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">新闻</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/news-lists">新闻管理</a>
</p>
<el-row :gutter="5">
  <el-col :span="3">
    <el-button plain type="primary" @click="goto('/news-edit')">发布新闻</el-button>
  </el-col>
  <el-col :span="3">
    <el-button plain type="primary" @click="exportNews">导出当前{{ totalData.length }}条新闻</el-button>
  </el-col>
  <el-col :span="3">
    <el-select v-model="search.classes" placeholder="请选择板块" style="width: 100%">
      <el-option value="all" label="全部板块"></el-option>

      {foreach $search.classes as $classes}
      <el-option value="{$classes.id}" label="{$classes.name}"></el-option>
      {/foreach}
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select
    style="width: 100%"
    v-model="search.texts"
    multiple
    filterable
    allow-create
    default-first-option
    placeholder="以回车分割(新闻标题，发布者)">
  </el-select>
  </el-col>
  <el-col :span="3">
    <el-button plain type="primary" @click="select">搜索</el-button>
  </el-col>
</el-row>
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
<el-table-column
prop="c_name"
label="板块"
min-width="120"
align="center"
></el-table-column>
          <el-table-column
            prop="title"
            label="标题"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="auth"
          label="发布者"
          min-width="180"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="created_at"
            label="发布时间"
            min-width="120"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="updated_at"
          label="修改时间"
          min-width="120"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="detail(scope.row)">详情</el-button>
              <el-button size="mini" @click="goto('/news-edit?news=' + scope.row.id)">修改</el-button>

            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
</div>
<script src="../../static/js/news/lists.js?v=1.1"></script>
</body>
</html>