{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/basic/users.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/bs-users">成员管理</a>
</p>

  <!-- 新增/修改用户对话框 -->
  <el-dialog
    :title="type === 'add' ? '新增用户' : '修改用户信息'"
    :visible.sync="visible"
    width="600px"
  >
    <el-form 
      :model="Form" 
      :rules="Form_rules" 
      ref="Form" 
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username" v-if="type === 'add'">
            <el-input v-model="Form.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="用户名" v-else>
            <el-input v-model="Form.username" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="Form.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="Form.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model="Form.email" placeholder="请输入电子邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户组" prop="usermode">
            <el-select v-model="Form.usermode" placeholder="请选择用户组" style="width: 100%">
              <el-option
                v-for="item in usermodes"
                :key="item.id"
                :label="item.group_name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门" prop="college">
            <el-select v-model="Form.college" placeholder="请选择部门" style="width: 100%" @change="handleDepartmentChange">
              <el-option
                v-for="item in departments"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专业" prop="major">
            <el-select v-model="Form.major" :placeholder="Form.college ? '请选择专业' : '请先选择部门'" style="width: 100%" :disabled="!Form.college">
              <el-option
                v-for="item in majors"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历/职称" prop="job">
            <el-input v-model="Form.job" placeholder="请输入学历或职称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年级" prop="grade">
            <el-select v-model="Form.grade" placeholder="请选择年级">
              <el-option
                v-for="year in gradeOptions"
                :key="year"
                :label="year"
                :value="year">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password" v-if="type === 'add'">
            <el-input v-model="Form.password" type="password" placeholder="请输入密码"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="password" v-else>
            <el-input v-model="Form.password" type="password" placeholder="留空则不修改密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
 
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">{{ type === 'add' ? '立即添加' : '确认修改' }}</el-button>
    </span>
  </el-dialog>

<el-row style="padding: 1rem;" :gutter="10">
<el-col :span="4">
<el-button plain type="primary" @click="adduser">新增用户</el-button>
</el-col>
<el-col :span="2">
<el-button plain type="success" @click="exportUsers">导出当前{{ totalData.length }}个用户</el-button>
</el-col>
<el-col :span="10">
<el-select
style="width: 100%"
v-model="search.texts"
multiple
filterable
allow-create
default-first-option
placeholder="以回车分割(用户名/姓名/联系方式/邮箱)">
</el-select>
</el-col>
<el-col :span="4">
<el-button plain type="primary" @click="select">搜索</el-button>
</el-col>
</el-row>
<el-table
:data="paginatedData"
style="width: 100%; margin-top: 20px"
:header-cell-style="{ background: '#f5f7fa', color: '#333' }"
height="600"
border
default-expand-all
>
<el-table-column
  prop="index"
  label="序号"
  min-width="60"
  align="center"
>
<template v-slot="scope">
<!-- 动态计算序号 -->
{{ (currentPage - 1) * pageSize + scope.$index + 1 }}
</template>
</el-table-column>
<el-table-column
  prop="username"
  label="用户名"
  min-width="120"
  align="center"
></el-table-column>
<el-table-column
  prop="name"
  label="姓名"
  min-width="120"
  align="center"
></el-table-column>
<el-table-column
prop="m_name"
label="用户组"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="phone"
label="电话"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="email"
label="邮箱"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="department"
label="部门"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="major"
label="专业"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="job"
label="学历/职称"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="grade"
label="年级"
min-width="120"
align="center"
>
</el-table-column>
<el-table-column
prop="status"
label="状态"
min-width="120"
align="center"
>
<template slot-scope="scope" >
  <el-tag 
  :type="scope.row.status == 0 ? 'success' : scope.row.status == 1 ? 'info' : 'danger'">
  {{ 
    scope.row.status == 0 ? '正常' :
    scope.row.status == 1 ? '停用' :
      '未知' 
  }}
</el-tag>
      </template>
</el-table-column>

<el-table-column
fixed="right"
  prop="management"
  label="管理"
  min-width="250"
  align="center"
  style="overflow: auto;"
>
<template slot-scope="scope">
<el-button size="mini" @click="manage(scope.row)">修改</el-button>

<el-popconfirm
      title="确定删除该用户吗"
      @confirm="handleDelete(scope.row)"
>
<el-button slot="reference" size="mini" plain type="danger">删除</el-button>
</el-popconfirm>
<el-popconfirm
      title="确定重置该用户密码吗"
      @confirm="handleResetPassword(scope.row)"
>
<el-button slot="reference" size="mini" plain type="warning">重置密码</el-button>
</el-popconfirm>

</template>
</el-table-column>
</el-table>
<el-pagination
@current-change="handlePageChange"
@size-change="handleSizeChange"
:current-page="currentPage"
:page-sizes="[10, 20, 50, 100]"
:page-size="pageSize"
layout="total, sizes, prev, pager, next, jumper"
:total="totalData.length"
style="text-align: right; margin-top: 20px;"
>
</el-pagination>

</div>
<script src="../../static/js/basic/users.js"></script>
</body>
</html>