<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dclevel;
use app\model\Dcproject;
use app\model\Dcstatus;
use app\model\Department;

class Lists extends BaseController
{
    public function index()
    {
        LogExecution('进入大创');
        //返回搜索要用的信息
        $search=[
          'status'=>Dcstatus::where('is_delete',0)->select(),
            'level'=>Dclevel::where('is_delete',0)->select(),
            'department'=>Department::where('is_delete',0)->select(),
        ];
        return view('dcmanage/lists',['search'=>$search]);
    }
    public function select_dc(){
        $search=input('post.search');
        $where=[];
        
        // 根据用户权限过滤数据
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        // 院级管理员只能查看本学院的项目
        if ($userMode == 3) {
            $where[]=['m.rank','=',1];
            $where[]=['mu.college','=',$userCollege];
        }
        // 校级管理员可以查看所有项目，不需要额外过滤
        
        // 教师只能看自己指导的项目
        if ($userMode == 2) {
            $where[] = ['t.username', '=', session('user.username')];
        }
        
        //level,status,department,user,texts
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[]=['p.uid|p.proid|p.name','like','%'.$text.'%'];
            }
        }
        if ($search['status']!='all'){
            $where[]=['p.status','=',$search['status']];
        }
        if ($search['level']!='all'){
            $where[]=['p.level','=',$search['level']];
        }
        if ($search['user']!=''){
            $where[]=['m.username|t.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[]=['m.rank','=',1];
            $where[]=['mu.college','=',$search['department']];
        }
        $data = Dcproject::alias('p')
            ->field('
        p.uid,p.proid,p.name,
        l.name as l_level,s.name as s_status,
        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames
    ')
            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
            ->join('user mu', 'mu.username = m.username', 'LEFT')
            ->join('user tu', 'tu.username = t.username', 'LEFT')
            ->join('dclevel l','l.id=p.level','LEFT')
            ->join('dcstatus s','s.id=p.status','LEFT')
            ->where('p.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
            ->where($where)
            ->where('mu.status', 0)
            ->where('tu.status', 0)
            ->group('p.uid')
            ->select();
        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($data),
            'data'=>$data
        ]]);
    }
    
    /**
     * 导出大创项目数据
     */
    public function export_projects()
    {
        $search = input('post.search');
        $where = [];
        
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[] = ['p.uid|p.proid|p.name','like','%'.$text.'%'];
            }
        }
        if ($search['status']!='all'){
            $where[] = ['p.status','=',$search['status']];
        }
        if ($search['level']!='all'){
            $where[] = ['p.level','=',$search['level']];
        }
        if ($search['user']!=''){
            $where[] = ['m.username|t.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[] = ['m.rank','=',1];
            $where[] = ['mu.college','=',$search['department']];
        }
        
        $data = Dcproject::alias('p')
            ->field('
        p.uid,p.proid,p.name,
        l.name as l_level,s.name as s_status,
        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames
    ')
            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
            ->join('user mu', 'mu.username = m.username', 'LEFT')
            ->join('user tu', 'tu.username = t.username', 'LEFT')
            ->join('dclevel l','l.id=p.level','LEFT')
            ->join('dcstatus s','s.id=p.status','LEFT')
            ->where('p.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
            ->where($where)
            ->where('mu.status', 0)
            ->where('tu.status', 0)
            ->group('p.uid')
            ->select();
            
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '申请编号',
            '项目编号',
            '项目名称',
            '项目级别',
            '当前状态',
            '负责人',
            '指导教师'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatDcProjectData($data);
        
        // 生成文件名
        $filename = $exportService->generateFilename('大创项目列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
}