<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcextension;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Member;
use think\facade\Db;

class Extension extends BaseController
{
    /**
     * 延期结题申请页面
     */
    public function index($uid){
        if (!$uid){
            LogExecution('延期结题申请失败，uid不存在，请重新登录');
            return ['status'=>'error','message'=>'延期结题申请失败，请重新登录'];
        }
        
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('延期结题申请失败，项目不存在');
            return ['status'=>'error','message'=>'延期结题申请失败，项目不存在'];
        }

        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }

        // 检查项目状态是否允许申请延期结题
        if (!$this->canApplyExtension($project['status'])) {
            LogExecution(session('user.name').'当前不是可申请延期结题状态');
            return json(['status'=>'error','message' => '当前不是可申请延期结题状态']);
        }

        // 检查是否已有延期申请
        $extension = Dcextension::where('is_delete',0)->where('uid',$uid)->find();
        
        return view('dcmanage/extension', [
            'project' => $project,
            'extension' => $extension
        ]);
    }

    /**
     * 提交延期结题申请
     */
    public function apply_extension($uid){
        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }
        
        $data = input('post.data');
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('延期结题申请失败，项目不存在');
            return json(['status'=>'error','message' => '延期结题申请失败，项目不存在']);
        }

        // 检查项目状态是否允许申请延期结题
        if (!$this->canApplyExtension($project['status'])) {
            LogExecution(session('user.name').'当前不是可申请延期结题状态');
            return json(['status'=>'error','message' => '当前不是可申请延期结题状态']);
        }

        // 验证数据
        if (!$data['extension_time'] || !$data['reason']) {
            LogExecution(session('user.name').'延期结题申请数据不完整');
            return json(['status'=>'error','message' => '请填写完整的延期申请信息']);
        }

        $new_status = $this->calculateExtensionStatus($project['status']);

        // 整理数据
        $extension = [
            'uid' => $uid,
            'extension_time' => intval($data['extension_time']),
            'reason' => $data['reason']
        ];

        if ($project['status'] == 8) {
            // 新申请
            return $this->submitNewExtension($extension, $uid, $new_status);
        } else {
            // 修改申请
            return $this->updateExtension($extension, $uid, $new_status);
        }
    }

    /**
     * 检查是否可以申请延期结题
     */
    private function canApplyExtension($status) {
        // 状态8：待提交结题报告
        // 状态22：待指导教师延期结题审核
        // 状态29：指导教师延期结题驳回
        // 状态30：学院延期结题驳回
        // 状态31：学校延期结题驳回
        $allowedStatus = [8, 22, 29, 30, 31];
        return in_array($status, $allowedStatus);
    }

    /**
     * 计算延期申请提交后的新状态
     */
    private function calculateExtensionStatus($currentStatus) {
        $statusMap = [
            8 => 22,   // 新申请 -> 等待教师审核
            29 => 22,  // 教师驳回后重新申请 -> 等待教师审核
            30 => 23,  // 学院驳回后重新申请 -> 等待学院审核
            31 => 24,  // 学校驳回后重新申请 -> 等待学校审核
        ];
        return $statusMap[$currentStatus] ?? $currentStatus;
    }

    /**
     * 提交新的延期申请
     */
    private function submitNewExtension($extension, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '延期结题申请',
            'remark' => '学生提交延期结题申请'
        ];
        
        try {
            Db::transaction(function () use ($extension, $uid, $new_status, $progress) {
                // 插入延期申请
                $extensionResult = Dcextension::insert($extension);
                // 更新项目状态
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                // 插入进度
                $progressResult = Dcprogress::insert($progress);
                // 只要无异常即可视为成功，不再要求projectResult为真
                if (!$extensionResult || !$progressResult) {
                    throw new \Exception('延期结题申请提交失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'延期结题申请提交成功');
            return ['status' => 'success', 'message' => '延期结题申请提交成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '延期结题申请提交失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '延期结题申请提交失败，数据表插入异常'];
        }
    }

    /**
     * 更新延期申请
     */
    private function updateExtension($extension, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '修改延期结题申请',
            'remark' => '学生修改延期结题申请'
        ];
        
        try {
            Db::transaction(function () use ($extension, $uid, $new_status, $progress) {
                // 检查是否已有未删除的延期申请
                $exist = Dcextension::where('is_delete',0)->where('uid',$uid)->find();
                if ($exist) {
                    // 更新原有数据
                    $extensionResult = Dcextension::where('is_delete',0)->where('uid',$uid)->update($extension);
                } else {
                    // 插入新数据
                    $extensionResult = Dcextension::insert($extension);
                }
                $progressResult = Dcprogress::insert($progress);
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                // 只要无异常即可视为成功，不再要求projectResult为真
                if (!$extensionResult || !$progressResult) {
                    throw new \Exception('延期结题申请修改失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'延期结题申请修改成功');
            return ['status' => 'success', 'message' => '延期结题申请修改成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '延期结题申请修改失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '延期结题申请修改失败，数据表插入异常'];
        }
    }
} 