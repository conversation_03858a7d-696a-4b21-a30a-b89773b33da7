1.建表语句
-- 用户表
CREATE TABLE `user` (
`id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
`name` VARCHAR(50) NOT NULL COMMENT '姓名',
`student_id` VARCHAR(50) NOT NULL UNIQUE COMMENT '学号',
`gender` ENUM('male', 'female', 'other') NOT NULL COMMENT '性别',
`college` VARCHAR(100) NOT NULL COMMENT '学院',
`major` VARCHAR(100) NOT NULL COMMENT '专业',
`email` VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
`phone` VARCHAR(20) NOT NULL COMMENT '电话号',
`password` VARCHAR(255) NOT NULL COMMENT '密码',
`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
`remarks` TEXT COMMENT '备注',
INDEX (`email`),
INDEX (`phone`)
) COMMENT='用户表';

-- 用户组表
CREATE TABLE `user_group` (
`id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户组ID',
`group_name` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户组名',
`permissions` TEXT NOT NULL COMMENT '权限列表（JSON格式）'
) COMMENT='用户组表';

-- 用户-用户组关联表
CREATE TABLE `user_user_group` (
`user_id` INT NOT NULL,
`user_group_id` INT NOT NULL,
PRIMARY KEY (`user_id`, `user_group_id`),
FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
FOREIGN KEY (`user_group_id`) REFERENCES `user_group`(`id`) ON DELETE CASCADE
) COMMENT='用户-用户组关联表';