<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcexpenditure;
use app\model\Dcinterm;
use app\model\Dcintermachievement;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Member;
use think\facade\Db;

class Intermproject extends BaseController
{
    /**
     * 中期报告页面
     */
    public function index($uid){
        if (!$uid){
            LogExecution('中期填报失败，uid不存在，请重新登录');
            return ['status'=>'error','message'=>'中期填报失败，请重新登录'];
        }
        
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('中期填报失败，项目不存在');
            return ['status'=>'error','message'=>'中期填报失败，项目不存在'];
        }

        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }

        // 检查项目状态是否允许提交中期报告
        if (!$this->canSubmitIntermReport($project['status'])) {
            LogExecution(session('user.name').'当前不是可填报状态');
            return json(['status'=>'error','message' => '当前不是可填报状态']);
        }

        if ($project['status'] == 4){
            // 第一次填报
            return view('dcmanage/intermproject',['budget'=>$project['budget']]);
        } else {
            // 被驳回需要显示之前信息
            $interm = Dcinterm::where('is_delete',0)->where('uid',$uid)->find();
            $intermachievement = Dcintermachievement::where('is_delete',0)->where('uid',$uid)->select();
            $expenditure = Dcexpenditure::where('is_delete',0)->where('uid',$uid)->where('type',1)->select();

            // 组装数据
            $data = [
                'progress' => $interm ? $interm['progress'] : '',
                'statement' => $interm ? $interm['statement'] : '',
                'outcomes' => $intermachievement ?: [],
                'expenses' => $expenditure ?: [],
            ];
            return view('dcmanage/intermproject',['budget'=>$project['budget'],'data'=>$data]);
        }
    }

    /**
     * 提交中期报告
     */
    public function add_interm($uid){
        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }
        
        $data = input('post.data');
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('中期报告提交失败，项目不存在');
            return json(['status'=>'error','message' => '中期报告提交失败，项目不存在']);
        }

        // 检查项目状态是否允许提交中期报告
        if (!$this->canSubmitIntermReport($project['status'])) {
            LogExecution(session('user.name').'当前不是可填报状态');
            return json(['status'=>'error','message' => '当前不是可填报状态']);
        }

        $new_status = $this->calculateIntermStatus($project['status']);
        
        // 整理数据
        $interm = [
            'uid' => $uid,
            'progress' => $data['progress'],
            'statement' => $data['statement']
        ];
        
        $expenditure = [];
        foreach ($data['expenses'] as $expenses){
            $expenditure[] = [
                'uid' => $uid,
                'type' => 1,
                'detail' => $expenses['detail'],
                'amount' => $expenses['amount']
            ];
        }
        
        $achievement = [];
        foreach ($data['outcomes'] as $outcomes){
            $achievement[] = [
                'uid' => $uid,
                'name' => $outcomes['name'],
                'date' => $outcomes['date'],
                'form' => $outcomes['form'],
                'remark' => $outcomes['remark'],
            ];
        }

        if ($project['status'] == 4) {
            // 新填报
            return $this->submitNewIntermReport($interm, $achievement, $expenditure, $uid, $new_status);
        } else {
            // 修改填报
            return $this->updateIntermReport($interm, $achievement, $expenditure, $uid, $new_status);
        }
    }

    /**
     * 检查是否可以提交中期报告
     */
    private function canSubmitIntermReport($status) {
        // 状态4：已立项待提交中期报告
        // 状态5：等待指导教师中期审核
        // 状态6：等待学院中期审核
        // 状态7：等待学校中期审核
        // 状态15：指导教师中期审核驳回
        // 状态16：学院中期审核驳回  
        // 状态17：学校中期审核驳回
        // 状态33：等待指导教师中期变更审核
        // 状态36：教师中期变更驳回
        // 状态37：学院中期变更驳回
        // 状态38：学校中期变更驳回
        $allowedStatus = [4, 5, 6, 7, 15, 16, 17, 33, 36, 37, 38];
        return in_array($status, $allowedStatus);
    }

    /**
     * 计算中期报告提交后的新状态
     */
    private function calculateIntermStatus($currentStatus) {
        $statusMap = [
            4 => 5,   // 新提交 -> 等待教师审核
            5 => 5,   // 重新提交 -> 等待教师审核
            15 => 5,  // 教师驳回后重新提交 -> 等待教师审核
            16 => 6,  // 学院驳回后重新提交 -> 等待学院审核
            17 => 7,  // 学校驳回后重新提交 -> 等待学校审核
            36 => 5,  // 教师中期变更驳回后提交中期报告 -> 等待教师审核
            37 => 6,  // 学院中期变更驳回后提交中期报告 -> 等待学院审核
            38 => 7,  // 学校中期变更驳回后提交中期报告 -> 等待学校审核
            4 => 5,  // 中期变更审核通过后提交中期报告 -> 等待教师审核
        ];
        return $statusMap[$currentStatus] ?? $currentStatus;
    }

    /**
     * 提交新的中期报告
     */
    private function submitNewIntermReport($interm, $achievement, $expenditure, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '中期申请',
            'remark' => '学生提交中期报告'
            ];
        
            try {
                Db::transaction(function () use ($interm, $achievement, $expenditure, $uid, $new_status, $progress) {
                    // 插入中期报告
                    $intermResult = Dcinterm::insert($interm);
                    // 插入中期成果
                    $achievementResult = empty($achievement) ? true : Dcintermachievement::insertAll($achievement);
                    // 插入支出
                    $expenditureResult = empty($expenditure) ? true : Dcexpenditure::insertAll($expenditure);
                    // 更新项目状态
                    $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                    // 插入进度
                    $progressResult = Dcprogress::insert($progress);
                
                    if ($intermResult === false || $achievementResult === false || $expenditureResult === false || $projectResult === false || $progressResult === false) {
                        throw new \Exception('中期报告提交失败，已回滚事务');
                    }
                });

                LogExecution(session('user.name').'中期报告提交成功');
                return ['status' => 'success', 'message' => '中期报告提交成功'];

            } catch (\Exception $e) {
                LogExecution(session('user.name') . '中期报告提交失败：' . $e->getMessage());
                return ['status' => 'error', 'message' => '中期报告提交失败，数据表插入异常'];
            }
    }

    /**
     * 更新中期报告
     */
    private function updateIntermReport($interm, $achievement, $expenditure, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '修改中期申请',
            'remark' => '学生修改中期报告'
            ];
        
            try {
                Db::transaction(function () use ($interm, $achievement, $expenditure, $uid, $new_status, $progress) {
                // 软删除原有数据
                Dcinterm::where('is_delete',0)->where('uid',$uid)->update(['is_delete'=>1]);
                Dcintermachievement::where('is_delete',0)->where('uid',$uid)->update(['is_delete'=>1]);
                Dcexpenditure::where('is_delete',0)->where('uid',$uid)->where('type',1)->update(['is_delete'=>1]);
                
                // 插入新数据
                $intermResult = Dcinterm::insert($interm);
                $achievementResult = empty($achievement) ? true : Dcintermachievement::insertAll($achievement);
                $expenditureResult = empty($expenditure) ? true : Dcexpenditure::insertAll($expenditure);
                $progressResult = Dcprogress::insert($progress);
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                
                if ($intermResult === false || $achievementResult === false || $expenditureResult === false || $progressResult === false || $projectResult === false) {
                        throw new \Exception('中期报告修改失败，已回滚事务');
                    }
                });

                LogExecution(session('user.name').'中期报告修改成功');
                return ['status' => 'success', 'message' => '中期报告修改成功'];

            } catch (\Exception $e) {
                LogExecution(session('user.name') . '中期报告修改失败：' . $e->getMessage());
                return ['status' => 'error', 'message' => '中期报告修改失败，数据表插入异常'];
            }
        }
}