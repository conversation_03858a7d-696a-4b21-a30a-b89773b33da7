<?php

namespace app\service;

use think\facade\Session;

class PermissionService
{
    /**
     * 获取当前用户信息
     */
    public static function getCurrentUser()
    {
        return Session::get('user');
    }

    /**
     * 获取当前用户组ID
     */
    public static function getCurrentUserMode()
    {
        $user = self::getCurrentUser();
        return $user ? $user['usermode'] : null;
    }

    /**
     * 检查用户是否有指定权限
     */
    public static function hasPermission($permission)
    {
        $userMode = self::getCurrentUserMode();
        
        // 超级管理员拥有所有权限
        if ($userMode == 11) {
            return true;
        }
        
        // 根据用户组检查权限
        switch ($permission) {
            // 大创项目权限
            case 'dc_view':
                return in_array($userMode, [1, 2, 3, 4, 11]);
            case 'dc_apply':
                return in_array($userMode, [1, 11]);
            case 'dc_edit':
                return in_array($userMode, [11]);
            case 'dc_check_teacher':
                return in_array($userMode, [2, 11]);
            case 'dc_check_college':
                return in_array($userMode, [3, 11]);
            case 'dc_check_school':
                return in_array($userMode, [4, 11]);
            case 'dc_manage':
                return in_array($userMode, [3, 4, 11]);

            // 竞赛项目权限
            case 'js_view':
                return in_array($userMode, [1, 2, 5, 6, 11]);
            case 'js_apply':
                return in_array($userMode, [1, 11]);
            case 'js_edit':
                return in_array($userMode, [11]);
            case 'js_check_teacher':
                return in_array($userMode, [2, 11]);
            case 'js_check_college':
                return in_array($userMode, [5, 11]);
            case 'js_check_school':
                return in_array($userMode, [6, 11]);
            case 'js_manage':
                return in_array($userMode, [5, 6, 11]);
            case 'js_list':
                return in_array($userMode, [1, 2, 5, 6, 11]);

            // 英才库权限
            case 'yck_view':
                return in_array($userMode, [1, 2, 7, 11]);
            case 'yck_apply':
                return in_array($userMode, [1, 2, 11]);
            case 'yck_check':
                return in_array($userMode, [7, 11]);

            // 新闻管理权限
            case 'news_view':
                return in_array($userMode, [8, 9, 10, 11, 12, 13]);
            case 'news_edit':
                return in_array($userMode, [8, 9, 10, 11, 12, 13]);
            case 'news_manage':
                return in_array($userMode, [11]);

            // 用户管理权限
            case 'user_manage':
                return in_array($userMode, [3, 4, 5, 6, 7, 11]);
            case 'user_reset_password':
                return in_array($userMode, [3, 4, 5, 6, 7, 11]);

            // 系统管理权限
            case 'system_manage':
                return in_array($userMode, [11]);

            default:
                return false;
        }
    }

    /**
     * 检查用户是否可以操作指定学院的数据
     */
    public static function canOperateCollege($targetCollege)
    {
        $userMode = self::getCurrentUserMode();
        $user = self::getCurrentUser();

        // 超级管理员可以操作所有学院
        if ($userMode == 11) {
            return true;
        }

        // 校级管理员可以操作所有学院
        if (in_array($userMode, [4, 6, 7])) {
            return true;
        }

        // 院级管理员只能操作本学院
        if (in_array($userMode, [3, 5])) {
            return $user['college'] == $targetCollege;
        }

        return false;
    }

    /**
     * 检查用户是否可以操作指定新闻板块
     */
    public static function canOperateNewsClass($newsClassId)
    {
        $userMode = self::getCurrentUserMode();
        
        // 超级管理员可以操作所有新闻板块
        if ($userMode == 11) {
            return true;
        }

        // 新闻管理员只能操作指定的新闻板块
        if (in_array($userMode, [8, 9, 10, 12, 13])) {
            // 这里需要根据具体的新闻板块权限逻辑来实现
            // 可能需要查询数据库来获取用户对应的新闻板块权限
            return true; // 临时返回true，需要根据实际逻辑调整
        }

        return false;
    }

    /**
     * 检查用户是否可以操作指定项目
     */
    public static function canOperateProject($projectUid, $projectType = 'dc')
    {
        $userMode = self::getCurrentUserMode();
        $user = self::getCurrentUser();

        // 超级管理员可以操作所有项目
        if ($userMode == 11) {
            return true;
        }

        // 根据项目类型检查权限
        if ($projectType == 'dc') {
            // 大创项目权限检查
            if (in_array($userMode, [1, 2, 3, 4])) {
                return true;
            }
        } elseif ($projectType == 'js') {
            // 竞赛项目权限检查
            if (in_array($userMode, [1, 2, 5, 6])) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查用户是否为项目负责人
     */
    public static function isProjectLeader($projectUid, $projectType = 'dc')
    {
        $user = self::getCurrentUser();
        if (!$user) {
            return false;
        }

        // 超级管理员视为所有项目的负责人
        if ($user['usermode'] == 11) {
            return true;
        }

        // 查询项目成员表，检查是否为负责人（rank=1）
        $member = \app\model\Member::where('uid', $projectUid)
            ->where('username', $user['username'])
            ->where('rank', 1)
            ->where('is_delete', 0)
            ->find();

        return !empty($member);
    }

    /**
     * 检查用户是否为项目指导教师
     */
    public static function isProjectTeacher($projectUid, $projectType = 'dc')
    {
        $user = self::getCurrentUser();
        if (!$user || $user['usermode'] != 2) {
            return false;
        }

        // 查询项目教师表，检查是否为指导教师
        $teacher = \app\model\Teacher::where('uid', $projectUid)
            ->where('username', $user['username'])
            ->where('is_delete', 0)
            ->find();

        return !empty($teacher);
    }

    /**
     * 检查用户是否可以查看项目
     */
    public static function canViewProject($projectUid, $projectType = 'dc')
    {
        $userMode = self::getCurrentUserMode();
        $user = self::getCurrentUser();

        // 超级管理员可以查看所有项目
        if ($userMode == 11) {
            return true;
        }

        // 学生和教师可以查看所有项目
        if (in_array($userMode, [1, 2])) {
            return true;
        }

        // 管理员只能查看本学院的项目
        if (in_array($userMode, [3, 4, 5, 6, 7])) {
            // 简化查询，暂时返回true，避免复杂查询导致问题
            return true;
        }

        return false;
    }

    /**
     * 检查用户是否可以编辑项目
     */
    public static function canEditProject($projectUid, $projectType = 'dc')
    {
        // 超级管理员可以编辑所有项目
        if (self::getCurrentUserMode() == 11) {
            return true;
        }

        // 只有项目负责人可以编辑项目
        return self::isProjectLeader($projectUid, $projectType);
    }

    /**
     * 检查用户是否可以审核项目
     */
    public static function canCheckProject($projectUid, $projectType = 'dc')
    {
        $userMode = self::getCurrentUserMode();
        $user = self::getCurrentUser();

        // 超级管理员可以审核所有项目
        if ($userMode == 11) {
            return true;
        }

        // 教师只能审核自己指导的项目
        if ($userMode == 2) {
            return self::isProjectTeacher($projectUid, $projectType);
        }

        // 管理员审核权限
        if ($projectType == 'dc') {
            if (in_array($userMode, [3, 4])) {
                // 院级管理员只能审核本学院项目
                if ($userMode == 3) {
                    $project = \app\model\Dcproject::alias('p')
                        ->join('member m', 'm.uid = p.uid')
                        ->join('user u', 'u.username = m.username')
                        ->where('p.uid', $projectUid)
                        ->where('m.rank', 1)
                        ->where('m.is_delete', 0)
                        ->where('u.status', 0)
                        ->field('u.college')
                        ->find();
                    
                    return $project && $project['college'] == $user['college'];
                }
                // 校级管理员可以审核所有项目
                return true;
            }
        } elseif ($projectType == 'js') {
            if (in_array($userMode, [5, 6])) {
                // 院级管理员只能审核本学院项目
                if ($userMode == 5) {
                    $project = \app\model\Jsproject::alias('p')
                        ->join('member m', 'm.uid = p.uid')
                        ->join('user u', 'u.username = m.username')
                        ->where('p.uid', $projectUid)
                        ->where('m.rank', 1)
                        ->where('m.is_delete', 0)
                        ->where('u.status', 0)
                        ->field('u.college')
                        ->find();
                    
                    return $project && $project['college'] == $user['college'];
                }
                // 校级管理员可以审核所有项目
                return true;
            }
        }

        return false;
    }

    /**
     * 获取用户可操作的菜单权限
     */
    public static function getMenuPermissions()
    {
        $userMode = self::getCurrentUserMode();
        
        $permissions = [
            'dc' => false,      // 大创平台
            'js' => false,      // 竞赛平台
            'yck' => false,     // 英才库
            'news' => false,    // 新闻管理
            'manage' => false,  // 系统管理
        ];

        // 根据用户组设置权限
        switch ($userMode) {
            case 1: // 学生
                $permissions['dc'] = true;
                $permissions['js'] = true;
                $permissions['yck'] = true;
                $permissions['news'] = true;
                break;
            case 2: // 教师
                $permissions['dc'] = true;
                $permissions['js'] = true;
                $permissions['yck'] = true;
                // $permissions['news'] = true; // 教师不再拥有新闻板块权限
                break;
            case 3: // 大创院级管理员
                $permissions['dc'] = true;
                // $permissions['manage'] = true; // 院级管理员不再有部门管理
                break;
            case 4: // 大创校级管理员
                $permissions['dc'] = true;
                // $permissions['manage'] = true; // 校级管理员不再有部门管理
                break;
            case 5: // 竞赛院级管理员
                $permissions['js'] = true;
                // $permissions['manage'] = true; // 院级管理员不再有部门管理
                break;
            case 6: // 竞赛校级管理员
                $permissions['js'] = true;
                // $permissions['manage'] = true; // 校级管理员不再有部门管理
                break;
            case 7: // 英才库校级管理员
                $permissions['yck'] = true;
                // $permissions['manage'] = true; // 校级管理员不再有部门管理
                break;
            case 8:
            case 9:
            case 10:
            case 12:
            case 13: // 新闻管理员
                $permissions['news'] = true;
                break;
            case 11: // 超级管理员
                $permissions['dc'] = true;
                $permissions['js'] = true;
                $permissions['yck'] = true;
                $permissions['news'] = true;
                $permissions['manage'] = true;
                break;
        }

        return $permissions;
    }

    /**
     * 获取悬浮菜单的操作权限
     */
    public static function getFloatingMenuPermissions($projectType = 'dc')
    {
        $userMode = self::getCurrentUserMode();
        
        $permissions = [
            'student_operations' => false,
            'teacher_operations' => false,
            'college_operations' => false,
            'school_operations' => false,
            'admin_operations' => false,
        ];

        if ($projectType == 'dc') {
            // 大创项目悬浮菜单权限
            switch ($userMode) {
                case 1: // 学生
                    $permissions['student_operations'] = true;
                    break;
                case 2: // 教师
                    $permissions['teacher_operations'] = true;
                    break;
                case 3: // 大创院级管理员
                    $permissions['college_operations'] = true;
                    break;
                case 4: // 大创校级管理员
                    $permissions['school_operations'] = true;
                    break;
                case 11: // 超级管理员
                    $permissions['student_operations'] = true;
                    $permissions['teacher_operations'] = true;
                    $permissions['college_operations'] = true;
                    $permissions['school_operations'] = true;
                    $permissions['admin_operations'] = true;
                    break;
            }
        } elseif ($projectType == 'js') {
            // 竞赛项目悬浮菜单权限
            switch ($userMode) {
                case 1: // 学生
                    $permissions['student_operations'] = true;
                    break;
                case 2: // 教师
                    $permissions['teacher_operations'] = true;
                    break;
                case 5: // 竞赛院级管理员
                    $permissions['college_operations'] = true;
                    break;
                case 6: // 竞赛校级管理员
                    $permissions['school_operations'] = true;
                    break;
                case 11: // 超级管理员
                    $permissions['student_operations'] = true;
                    $permissions['teacher_operations'] = true;
                    $permissions['college_operations'] = true;
                    $permissions['school_operations'] = true;
                    $permissions['admin_operations'] = true;
                    break;
            }
        }

        return $permissions;
    }
} 