{include file="public/_header"}
  <script>
    window.userinfo = {
      username: '{:session(\'user.username\')}',
      usermode: '{:session(\'user.usermode\')}'
    };
  </script>
  <link rel="stylesheet" href="../../static/css/js/projectdetail.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">竞赛平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-project">项目管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-projectdetail?uid={$project.uid}">{$project.name}</a>
</p>

  <!-- 上传获奖结果 -->
  <el-dialog
  title="获奖结果上传"
  :visible.sync="awardVisible"
  width="40%"
  >
  <el-form ref="awardform" :rules="award_rules" label-position="left" :model="awardform" label-width="6rem">
    <el-form-item label="获奖级别" prop="award">
      <el-select
      v-model="awardform.award"
      placeholder="获奖级别"
      >
      {foreach $award_level as $level}
      <el-option  label="{$level['level'].$level['type']}" value="{$level['id']}"></el-option>
      {/foreach}
    </el-select>
    </el-form-item>
    <el-form-item label="证书上传" prop="certificate">

    <el-upload
    class="avatar-uploader"
    drag
    action="bs-addfile?class=js&type=certificate"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-remove="removeFile"
    :limit="1" 
    >
    <img v-if="awardform.certificate" :src="`/static/files/upload/${awardform.certificate}`" class="avatar">
    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
  </el-upload>
</el-form-item>

    <el-form-item label="备注" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入备注"
      v-model="awardform.remark">
     </el-input>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="awardVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_award">确 定</el-button>
  </span>
</el-dialog>
  <!-- 审核框 -->
  <el-dialog
  :title="checktitle"
  :visible.sync="checkVisible"
  width="30%"
  >
  <el-form ref="checkform" :rules="check_rules" label-position="left" :model="checkform" label-width="6rem">
    <el-form-item label="审核结果" prop="check">
      <el-radio v-model="checkform.check" label="0">通过</el-radio>
      <el-radio v-model="checkform.check" label="1">驳回</el-radio>
    </el-form-item>
    <el-form-item label="审核意见" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入审核意见"
      v-model="checkform.remark">
     </el-input>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="checkVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_check">确 定</el-button>
  </span>
</el-dialog>


 <!-- 悬浮按钮 -->
 <el-button
 class="floating-button"
 type="primary"
 icon="el-icon-more"
 circle
 @click="toggleMenu"
 v-if="hasAnyOperation"
></el-button>
<!-- 悬浮菜单 -->
<div v-if="isMenuVisible" class="floating-menu" style="max-height: 60vh; overflow-y: auto; overflow-x: hidden;">
 <el-menu>
   <!-- 学生操作 - 只有学生或超级管理员可见 -->
   <el-menu-item-group v-if="isStudentOperation">
   <template slot="title">学生操作</template>
   <el-menu-item index="1" @click="handleMenuClick(1)">修改项目</el-menu-item>
   <el-menu-item index="2" @click="handleMenuClick(2)">提交获奖证书</el-menu-item>
 </el-menu-item-group>
 
 <!-- 教师操作 - 只有教师或超级管理员可见 -->
 <el-menu-item-group v-if="isTeacherOperation">
   <template slot="title">教师操作</template>
   <el-menu-item index="3" @click="handleMenuClick(3)">项目报名审核</el-menu-item>
   <el-menu-item index="4" @click="handleMenuClick(4)">项目证书审核</el-menu-item>
 </el-menu-item-group>
 
 <!-- 学院操作 - 只有竞赛院级管理员或超级管理员可见 -->
 <el-menu-item-group v-if="isCollegeOperation">
   <template slot="title">学院操作</template>
   <el-menu-item index="5" @click="handleMenuClick(5)">项目报名审核</el-menu-item>
   <el-menu-item index="6" @click="handleMenuClick(6)">项目证书审核</el-menu-item>
 </el-menu-item-group>
 
 <!-- 学校操作 - 只有竞赛校级管理员或超级管理员可见 -->
 <el-menu-item-group v-if="isSchoolOperation">
   <template slot="title">学校操作</template>
   <el-menu-item index="7" @click="handleMenuClick(7)">项目报名审核</el-menu-item>
   <el-menu-item index="8" @click="handleMenuClick(8)">项目证书审核</el-menu-item>
 </el-menu-item-group>
 
 <!-- 超级管理员操作 - 只有超级管理员可见 -->
 <el-menu-item-group v-if="isSuperAdmin">
   <template slot="title">超级管理员操作</template>
   <el-menu-item index="9" @click="handleMenuClick(9)">切换项目状态</el-menu-item>
   <el-menu-item index="10" @click="handleMenuClick(10)">修改项目</el-menu-item>
 </el-menu-item-group>
 </el-menu>
</div>

<el-row>
  <el-col :xs="24" :sm="20">
    <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="项目概况" :column="6" border>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目名称
        </template>
        {$project.name}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目状态
        </template>
        {$project.status}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          所属竞赛
        </template>
        {$project.c_name}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          竞赛级别
        </template>
        {$project.c_level}
      </el-descriptions-item>

      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          赛道/类别
        </template>
        {$project.class}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          所属单位
        </template>
        {$member[0]['college']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          项目负责人
        </template>
        {$member[0]['name']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          学号
        </template>
        {$member[0]['username']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          联系电话
        </template>
        {$member[0]['phone']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          电子邮箱
        </template>
        {$member[0]['email']}
      </el-descriptions-item>
      <el-descriptions-item span="6">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目简介
        </template>
        {$project.introduction}
      </el-descriptions-item>
    </el-descriptions>
    <el-table
    :data="{$member}"
    style="width: 100%">
    <el-table-column label="团队成员信息" >
      <el-table-column
      prop="username"
      label="学号"
      min-width="100">
      </el-table-column>
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="college"
      label="学院"
      min-width="120">
      </el-table-column>
      <el-table-column
      prop="major"
      label="专业"
      min-width="120">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    
    {if count($outstudent) > 0}
    <el-table
    :data="{$outstudent}"
    style="width: 100%">
    <el-table-column label="校外成员信息" >
    
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    {/if}


    <el-table
    :data="{$teacher}"
    style="width: 100%">
    <el-table-column label="指导教师信息" >
    
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="job"
      label="技术职称"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    {if count($outteacher) > 0}
    <el-table
    :data="{$outteacher}"
    style="width: 100%">
    <el-table-column label="校外指导教师信息" >
    
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="job"
      label="职务/职称"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>



    {/if}
    <el-table
    :data="{$files}"
    style="width: 100%">
    <el-table-column
    label="序号"
    type="index"
    min-width="50">
    
    </el-table-column>
    <el-table-column
      prop="name"
      label="项目相关材料"
      min-width="200"
    >
      <template v-slot="scope">
        <a style="text-decoration: none; color: #000;" :href="`/static/files/upload/${scope.row.path}`">{{ scope.row.name }}</a>
      </template>
    </el-table-column>
    </el-table>
    {if isset($checks['status1']['teacher'][0]['created_at']) || isset($checks['status1']['college'][0]['created_at']) || isset($checks['status1']['school'][0]['created_at'])}
    <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="审核意见" direction="vertical"   border>
      {if count($checks['status1']['teacher']) neq 0}
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          教师立项意见
        </template>
        {foreach $checks['status1']['teacher'] as $check}
        <p>
        {if !$check['check']}
        <span style="color: #1fd11f97;">通过</span>
        {else}
        <span style="color: #ff4400;">驳回</span>
        {/if}
        <span style="color: #00000097;">{$check['created_at']}</span>
        <span>{$check['remark']}</span>
      </p>
        {/foreach}
      </el-descriptions-item>
    {/if}
    {if  count($checks['status1']['college']) neq 0}
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        学院立项意见
      </template>
      {foreach $checks['status1']['college'] as $check}
      <p>
        {if !$check['check']}
        <span style="color: #1fd11f97;">通过</span>
        {else}
        <span style="color: #ff4400;">驳回</span>
        {/if}
        <span style="color: #00000097;">{$check['created_at']}</span>
        <span>{$check['remark']}</span>
      </p>
      {/foreach}
    </el-descriptions-item>
  {/if}
  {if count($checks['status1']['school']) neq 0}
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        学校立项意见
      </template>
      {foreach $checks['status1']['school'] as $check}
      <p>
        {if !$check['check']}
        <span style="color: #1fd11f97;">通过</span>
        {else}
        <span style="color: #ff4400;">驳回</span>
        {/if}
        <span style="color: #00000097;">{$check['created_at']}</span>
        <span>{$check['remark']}</span>
      </p>
      {/foreach}
    </el-descriptions-item>
  {/if}
    </el-descriptions>
    {/if}

    {if $project['award']}
    <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="获奖信息"   :column="1"  border>
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          获奖类型
        </template>
        {$project['award']}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          证书图片
        </template>
        <el-image
        src="/static/files/upload/{$project['certificate']}"
      ></el-image>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          备注
        </template>
        {$project['award_remark']}
      </el-descriptions-item>
  </el-descriptions>

  <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="审核意见" direction="vertical"   border>
    {if count($checks['status1']['teacher']) neq 0}
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        教师获奖信息意见
      </template>
      {foreach $checks['status2']['teacher'] as $check}
      <p>
      {if !$check['check']}
      <span style="color: #1fd11f97;">通过</span>
      {else}
      <span style="color: #ff4400;">驳回</span>
      {/if}
      <span style="color: #00000097;">{$check['created_at']}</span>
      <span>{$check['remark']}</span>
    </p>
      {/foreach}
    </el-descriptions-item>
  {/if}
  {if  count($checks['status2']['college']) neq 0}
  <el-descriptions-item>
    <template slot="label" >
      <i class="el-icon-user"></i>
      学院获奖信息意见
    </template>
    {foreach $checks['status2']['college'] as $check}
    <p>
      {if !$check['check']}
      <span style="color: #1fd11f97;">通过</span>
      {else}
      <span style="color: #ff4400;">驳回</span>
      {/if}
      <span style="color: #00000097;">{$check['created_at']}</span>
      <span>{$check['remark']}</span>
    </p>
    {/foreach}
  </el-descriptions-item>
{/if}
{if count($checks['status2']['school']) neq 0}
  <el-descriptions-item>
    <template slot="label" >
      <i class="el-icon-user"></i>
      学校获奖信息意见
    </template>
    {foreach $checks['status2']['school'] as $check}
    <p>
      {if !$check['check']}
      <span style="color: #1fd11f97;">通过</span>
      {else}
      <span style="color: #ff4400;">驳回</span>
      {/if}
      <span style="color: #00000097;">{$check['created_at']}</span>
      <span>{$check['remark']}</span>
    </p>
    {/foreach}
  </el-descriptions-item>
{/if}
  </el-descriptions>

    {/if}


  </el-col>
  <el-col :xs="24" :sm="4">
    <el-timeline :reverse="false">
      {foreach $progesses as $progess}
      <el-timeline-item
      timestamp="{$progess.created_at}">
      {$progess.action}
      <br>
      {$progess.remark}
    </el-timeline-item>
    {/foreach}
    </el-timeline>
  </el-col>

</el-row>




</div>
<script src="../../static/js/js/projectdetail.js?v=1.1"></script>
</body>
</html>