{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/js/addproject.css">
<style>
  body{
    overflow-x: hidden;
    /* padding: 0 3rem; */
  }
</style>
<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">竞赛平台</a>
    >
    {if !isset($project)}
    <a style="text-decoration: none;color: #00000097;" href="js-competition">竞赛管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-addProject?cuid={$competition['cuid']}">竞赛填报:{$competition['name']}</a>
    {else}
    <a style="text-decoration: none;color: #00000097;" href="js-project">项目管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-projectdetail?uid={$project.uid}">{$project.name}</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-editproject?uid={$project['uid']}">修改竞赛项目:{$project['name']}</a>
    {/if}
  </p>

  <el-row :gutter="40" class="container">
    <h2>{$competition['name']}</h2>
  <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="10rem">
    <el-col  :sm="24" :md="12" >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名称"></el-input>
        </el-form-item>
        <el-form-item label="赛道/类别" prop="class">
          <el-input v-model="form.class" placeholder="请输入赛道/类别等信息"></el-input>
        </el-form-item>
        <el-form-item label="立项时间" prop="time">
            <el-date-picker
            style="width: 100%;"
            v-model="form.time"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目成员" prop="members">
          <el-select
          style="width: 100%;"
          v-model="form.members"
          multiple
          :multiple-limit='{$competition['member']+1}'
          filterable
          remote
          reserve-keyword
          placeholder="顺序选择,无需选择队长"
          :remote-method="remotestudents"
          :loading="loading">
          <el-option
            v-for="item in options_s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            >
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      {if $competition['outstu']}
      <el-form-item label="校外成员" prop="outstu">
        <el-button plain type="primary" @click="add('outstu')" style="margin-top: 20px;">添加校外成员</el-button>  
        <div v-for="(outstu, index) in form.outstu" :key="index" class="expense-row">
          <el-input style="width: 20;" v-model="outstu.name" placeholder="姓名" ></el-input>
          <el-input style="width: 20;" v-model="outstu.unit" placeholder="单位" ></el-input>
          <el-input style="width: 20;" v-model="outstu.phone" placeholder="手机号" ></el-input>
          <el-input style="width: 20;" v-model="outstu.email" placeholder="电子邮件" ></el-input>
          <el-button style="width: 20;" plain type="danger" @click="remove(index, 'outstu')">删除</el-button>
        </div>
      </el-form-item>
      {/if}

      <el-form-item label="指导教师" prop="teachers">
        <el-select
        style="width: 100%;"
        v-model="form.teachers"
        multiple
        :multiple-limit='{$competition['teacher']}'
        filterable
        remote
        reserve-keyword
        placeholder="顺序选择"
        :remote-method="remoteteachers"
        :loading="loading">
        <el-option
          v-for="item in options_t"
          :key="item.value"
          :label="item.label"
          :value="item.value">
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
        </el-option>
      </el-select>
    </el-form-item>
    {if $competition['outtea']}
    <el-form-item label="校外指导教师" prop="outtea">
      <el-button plain type="primary" @click="add('outtea')" style="margin-top: 20px;">添加校外指导教师</el-button>  
      <div v-for="(outtea, index) in form.outtea" :key="index" class="expense-row">
        <el-input v-model="outtea.name" placeholder="姓名" ></el-input>
        <el-input v-model="outtea.unit" placeholder="单位" ></el-input>
        <el-input v-model="outtea.job" placeholder="职务/职称" ></el-input>
        <el-input v-model="outtea.phone" placeholder="手机号" ></el-input>
        <el-input v-model="outtea.email" placeholder="电子邮件" ></el-input>
        <el-button plain type="danger" @click="remove(index, 'outtea')">删除</el-button>
      </div>
    </el-form-item>
    {/if}
    
    <el-form-item label="项目简介" prop="introduction">
      <el-input
      type="textarea"
      maxlength="500"
      show-word-limit
      :autosize="{ minRows: 5, maxRows: 20}"
      placeholder="请输入项目简介"
      v-model="form.introduction">
     </el-input>
    </el-form-item>
  </el-col>

    <el-col  :sm="24" :md="12" >
    <el-form-item label="项目材料" prop="fileurls">
      <el-upload
      class="upload-demo"
      drag
      action="bs-addfile?class=dc&type=conclude"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-remove="removeFile"
      :show-file-list="false"
      >
      <i  class="el-icon-upload"></i>
      <div  class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            {if isset($project)}
      <div class="el-upload__tip" slot="tip">修改上传将覆盖之前上传的全部文件</div>
      {/if}
      <div class="el-upload__tip" slot="tip">只能上传PDF，PPT, Word, Excel及压缩包文件，且不超过20MB</div>

    </el-upload>
    <div style="display: flex;" v-if="form.fileurls.length > 0" class="el-upload-list__item" v-for="file in form.fileurls" :key="file.uid">
      <span class="el-upload-list__item-name">{{ file.name }}</span>
      <el-button size="mini" type="danger" plain @click="removeFile(file)">删除</el-button>
    </div>
    </el-form-item>

  </el-col>

    <el-col  :sm="24"  style="text-align: center;">
  <el-button type="primary " plain @click="onSubmit">{{ submitButtonText }}</el-button>
    </el-col>

  </el-form>
  
  </el-row>





</div>
<script src="../../static/js/js/addproject.js?v=1.3"></script>

<script>
  {if isset($project)}
  app.update_data(
    {:json_encode($project)},
    {:json_encode($members)},
    {:json_encode($teachers)},
  );

  {/if}



</script>
</body>
</html>