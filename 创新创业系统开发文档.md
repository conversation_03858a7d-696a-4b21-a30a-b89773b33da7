# 创新创业系统开发文档

## 1. 系统概述

### 1.1 项目简介
创新创业系统（CXCYSYS）是一个基于ThinkPHP 8.0框架开发的综合性创新创业管理平台，主要用于管理大学生创新创业项目、竞赛项目、英才库和新闻资讯等功能。系统采用B/S架构，支持多用户角色管理，提供完整的项目生命周期管理。

### 1.2 技术栈
- **后端框架**: ThinkPHP 8.0
- **数据库**: MySQL
- **前端技术**: Vue.js 2.x + Element UI
- **图表库**: ECharts
- **Excel处理**: PhpSpreadsheet
- **UUID生成**: Ramsey/Uuid
- **PHP版本**: >= 8.0.0

### 1.3 系统架构
```
cxcysys/
├── app/                    # 应用核心目录
│   ├── controller/        # 控制器层
│   ├── model/            # 模型层
│   ├── service/          # 服务层
│   ├── middleware/       # 中间件
│   ├── validate/         # 验证器
│   └── view/             # 视图层
├── config/               # 配置文件
├── public/               # 公共资源
├── route/                # 路由配置
├── static/               # 静态资源
└── runtime/              # 运行时文件
```

## 2. 数据库设计

### 2.1 核心数据表

#### 2.1.1 用户相关表
- **user**: 用户基础信息表
- **usermode**: 用户组/角色表
- **department**: 学院/部门表
- **major**: 专业表

#### 2.1.2 大创项目相关表
- **dcproject**: 大创项目主表
- **dcstatus**: 项目状态表
- **dclevel**: 项目级别表
- **dctype**: 项目类型表
- **dcperiod**: 项目周期表
- **member**: 项目成员表
- **teacher**: 指导教师表
- **dcprogress**: 项目进度表
- **dcinterm**: 中期报告表
- **dcintermachievement**: 中期成果表
- **dcconclude**: 结题报告表
- **dcachievement**: 项目成果表
- **dcexpenditure**: 项目支出表
- **dcextension**: 延期申请表
- **dcmidchange**: 中期变更表

#### 2.1.3 竞赛项目相关表
- **jsproject**: 竞赛项目主表
- **jsstatus**: 竞赛状态表
- **jscompetition**: 竞赛信息表
- **jsaward**: 获奖等级表
- **jslevel**: 竞赛级别表
- **jscheck**: 竞赛审核表

#### 2.1.4 英才库相关表
- **yckuser**: 英才用户表
- **yckstatus**: 英才状态表
- **yckjob**: 英才职位表
- **ycktag**: 英才标签表
- **yckdomain**: 英才领域表
- **yckcontactlog**: 联系记录表
- **yckblacklist**: 黑名单表

#### 2.1.5 新闻资讯相关表
- **newsclass**: 新闻分类表
- **newsdetail**: 新闻详情表
- **carousel**: 轮播图表

#### 2.1.6 系统功能表
- **file**: 文件管理表

### 2.2 关键字段说明

#### 2.2.1 项目状态流转
大创项目状态码定义：
- 1: 待提交
- 2: 等待指导教师审核
- 3: 等待学院审核
- 4: 已立项待提交中期报告
- 5: 等待指导教师中期审核
- 6: 等待学院中期审核
- 7: 等待学校中期审核
- 8: 待提交结题报告
- 9: 等待指导教师结题审核
- 10: 等待学院结题审核
- 11: 等待学校结题审核
- 12: 已结题
- 13: 已终止
- 15-20: 各种驳回状态
- 22-32: 延期相关状态
- 33-38: 中期变更相关状态

## 3. 用户权限系统

### 3.1 用户角色定义
- **1**: 学生
- **2**: 教师
- **3**: 大创院级管理员
- **4**: 大创校级管理员
- **5**: 竞赛院级管理员
- **6**: 竞赛校级管理员
- **7**: 英才库校级管理员
- **8-10, 12-13**: 新闻管理员
- **11**: 超级管理员

### 3.2 权限控制实现

#### 3.2.1 权限服务类 (PermissionService)
```php
class PermissionService
{
    // 检查用户是否有指定权限
    public static function hasPermission($permission)
    
    // 检查用户是否可以操作指定学院的数据
    public static function canOperateCollege($targetCollege)
    
    // 检查用户是否为项目负责人
    public static function isProjectLeader($projectUid, $projectType = 'dc')
    
    // 检查用户是否为项目指导教师
    public static function isProjectTeacher($projectUid, $projectType = 'dc')
}
```

#### 3.2.2 中间件权限控制
- **CheckLogin**: 登录状态检查
- **CheckPermission**: 权限验证中间件

### 3.3 权限矩阵

| 功能模块 | 学生 | 教师 | 院级管理员 | 校级管理员 | 超级管理员 |
|---------|------|------|-----------|-----------|-----------|
| 大创项目申请 | ✓ | - | - | - | ✓ |
| 大创项目审核 | - | ✓(指导项目) | ✓(本院) | ✓(全校) | ✓ |
| 竞赛项目申请 | ✓ | - | - | - | ✓ |
| 竞赛项目审核 | - | ✓(指导项目) | ✓(本院) | ✓(全校) | ✓ |
| 英才库申请 | ✓ | ✓ | - | ✓ | ✓ |
| 新闻管理 | - | - | - | - | ✓ |
| 用户管理 | - | - | ✓(本院) | ✓(全校) | ✓ |

## 4. 核心功能模块

### 4.1 大创项目管理模块

#### 4.1.1 项目申请 (Addproject.php)
- **功能**: 学生提交大创项目申请
- **主要方法**:
  - `index()`: 显示申请页面
  - `addProject()`: 提交项目申请
- **数据验证**: 项目名称、成员信息、指导教师等必填项验证
- **状态流转**: 提交后状态变为"等待指导教师审核"

#### 4.1.2 项目审核 (Check.php)
- **功能**: 多级审核流程管理
- **审核流程**: 指导教师 → 学院 → 学校
- **主要方法**:
  - `checkProject()`: 审核项目
  - `updateProjectDataAfterMidchange()`: 中期变更后更新项目数据
- **审核结果**: 通过/驳回，状态自动流转

#### 4.1.3 中期报告管理 (Intermproject.php)
- **功能**: 项目中期报告提交和审核
- **主要方法**:
  - `index()`: 中期报告页面
  - `add_interm()`: 提交中期报告
- **支持功能**: 项目进度、中期成果、经费支出记录

#### 4.1.4 结题报告管理 (Concludeproject.php)
- **功能**: 项目结题报告提交和审核
- **主要方法**:
  - `index()`: 结题报告页面
  - `add_conclude()`: 提交结题报告
- **支持功能**: 项目成果、结题总结、经费决算

#### 4.1.5 延期申请管理 (Extension.php)
- **功能**: 项目延期申请处理
- **主要方法**:
  - `index()`: 延期申请页面
  - `apply_extension()`: 提交延期申请
- **审核流程**: 指导教师 → 学院 → 学校

#### 4.1.6 中期变更管理 (Midchange.php)
- **功能**: 项目中期变更申请
- **主要方法**:
  - `index()`: 中期变更页面
  - `apply_midchange()`: 提交变更申请
- **变更内容**: 项目成员、指导教师变更

### 4.2 竞赛项目管理模块

#### 4.2.1 竞赛管理 (Competition.php)
- **功能**: 竞赛信息管理
- **主要方法**:
  - `select_competition()`: 查询竞赛列表
  - `addCompetition()`: 添加竞赛
  - `updateCompetition()`: 更新竞赛信息

#### 4.2.2 项目申请 (Addproject.php)
- **功能**: 竞赛项目申请
- **主要方法**:
  - `addProject()`: 提交竞赛项目
  - `editProject()`: 编辑项目信息

#### 4.2.3 项目管理 (Project.php)
- **功能**: 竞赛项目列表和导出
- **主要方法**:
  - `select_project()`: 查询项目列表
  - `export_projects()`: 导出项目数据

#### 4.2.4 证书管理 (Certificate.php)
- **功能**: 获奖证书上传和管理
- **主要方法**:
  - `addcertificate()`: 上传证书

### 4.3 英才库管理模块

#### 4.3.1 申请管理 (Apply.php)
- **功能**: 英才库申请
- **主要方法**:
  - `apply()`: 提交申请
- **申请类型**: 英才学生、精英教师

#### 4.3.2 申请列表 (Applylist.php)
- **功能**: 申请列表管理
- **主要方法**:
  - `select_apply()`: 查询申请列表

#### 4.3.3 英才列表 (Lists.php)
- **功能**: 英才库列表管理
- **主要方法**:
  - `select_lists()`: 查询英才列表
  - `update_lists()`: 更新排序
  - `export_lists()`: 导出数据
  - `get_homepage_talents()`: 获取首页展示数据

#### 4.3.4 课程管理 (Curriculum.php)
- **功能**: 英才课程管理
- **主要方法**:
  - `update()`: 更新课程信息
  - `seecontact()`: 查看联系方式

### 4.4 新闻资讯管理模块

#### 4.4.1 新闻分类 (Classes.php)
- **功能**: 新闻分类管理
- **主要方法**:
  - `classlist()`: 分类列表
  - `edit()`: 编辑分类

#### 4.4.2 新闻列表 (Lists.php)
- **功能**: 新闻列表管理
- **主要方法**:
  - `newslist()`: 查询新闻列表
  - `export_news()`: 导出新闻数据

#### 4.4.3 新闻编辑 (Edit.php)
- **功能**: 新闻内容编辑
- **主要方法**:
  - `index()`: 编辑页面
  - `add()`: 添加新闻
  - `update()`: 更新新闻

#### 4.4.4 轮播管理 (CarouselManage.php)
- **功能**: 首页轮播图管理
- **主要方法**:
  - `getCarouselList()`: 获取轮播列表
  - `addCarousel()`: 添加轮播
  - `editCarousel()`: 编辑轮播
  - `deleteCarousel()`: 删除轮播
  - `updateStatus()`: 更新状态
  - `updateSort()`: 更新排序
  - `getHomepageCarousel()`: 获取首页轮播数据

### 4.5 基础管理模块

#### 4.5.1 用户管理 (Users.php)
- **功能**: 用户信息管理
- **主要方法**:
  - `select_users()`: 查询用户列表
  - `manage()`: 管理用户
  - `delete()`: 删除用户
  - `reset_password()`: 重置密码
  - `export_users()`: 导出用户数据

#### 4.5.2 部门管理 (DepartmentController.php)
- **功能**: 学院专业管理
- **主要方法**:
  - `get_departments()`: 获取部门列表
  - `add_department()`: 添加部门
  - `update_department()`: 更新部门
  - `delete_department()`: 删除部门
  - `add_major()`: 添加专业
  - `update_major()`: 更新专业
  - `delete_major()`: 删除专业

#### 4.5.3 文件管理 (Files.php)
- **功能**: 文件上传和管理
- **主要方法**:
  - `addfile()`: 上传文件
  - `uploadCarousel()`: 上传轮播图

#### 4.5.4 搜索功能 (Search.php)
- **功能**: 全局搜索
- **主要方法**:
  - `index()`: 搜索首页
  - `user()`: 搜索用户
  - `dc()`: 搜索大创项目
  - `js()`: 搜索竞赛项目
  - `curriculum()`: 搜索课程
  - `news()`: 搜索新闻

## 5. 服务层设计

### 5.1 导出服务 (ExportService.php)
```php
class ExportService
{
    // 生成Excel文件
    public function generateExcel($data, $headers, $filename)
    
    // 格式化用户数据
    public function formatUserData($data)
    
    // 格式化大创项目数据
    public function formatDcProjectData($data)
    
    // 格式化竞赛项目数据
    public function formatJsProjectData($data)
    
    // 格式化英才列表数据
    public function formatYckListData($data)
    
    // 格式化新闻数据
    public function formatNewsData($data)
}
```

### 5.2 仪表盘服务 (DashboardService.php)
- **功能**: 管理员仪表盘数据统计
- **主要方法**:
  - `getStatistics()`: 获取统计数据
  - `getCharts()`: 获取图表数据
  - `getRecentActivities()`: 获取最新动态
  - `getTodoList()`: 获取待办事项

### 5.3 权限服务 (PermissionService.php)
- **功能**: 权限验证和管理
- **主要方法**:
  - `hasPermission()`: 检查权限
  - `canOperateCollege()`: 检查学院操作权限
  - `getMenuPermissions()`: 获取菜单权限
  - `getFloatingMenuPermissions()`: 获取悬浮菜单权限

## 6. 中间件设计

### 6.1 登录检查中间件 (CheckLogin.php)
```php
class CheckLogin
{
    public function handle($request, \Closure $next)
    {
        // 检查登录状态
        // 未登录重定向到登录页面
    }
}
```

### 6.2 权限检查中间件 (CheckPermission.php)
```php
class CheckPermission
{
    public function handle($request, \Closure $next, $permission = '')
    {
        // 检查用户是否有指定权限
        // 权限不足返回错误信息
    }
}
```

## 7. 路由配置

### 7.1 路由文件结构 (route/app.php)
- **认证路由**: 登录、登出
- **大创项目路由**: 项目申请、审核、中期、结题等
- **竞赛项目路由**: 竞赛管理、项目申请等
- **英才库路由**: 申请、列表、课程等
- **新闻管理路由**: 分类、列表、编辑、轮播等
- **基础管理路由**: 用户、部门、文件、搜索等

### 7.2 路由规则示例
```php
// 大创项目路由
Route::rule('dc-addproject', '/dcmanage/addProject/addProject','GET|POST');
Route::post('dc-search', '/dcmanage/lists/select_dc');
Route::post('dc-check', '/dcmanage/check/checkProject');

// 竞赛项目路由
Route::get('js-project', '/jsmanage/project/index');
Route::post('js-searchproject', '/jsmanage/project/select_project');

// 英才库路由
Route::rule('yck-apply', '/yckmanage/apply/apply','GET|POST');
Route::get('yck-lists', '/yckmanage/lists/index');
```

## 8. 前端设计

### 8.1 技术栈
- **Vue.js 2.x**: 前端框架
- **Element UI**: UI组件库
- **ECharts**: 图表库
- **Axios**: HTTP客户端

### 8.2 页面结构
- **管理后台**: 基于Element UI的管理界面
- **前台展示**: 新闻展示、英才库展示
- **响应式设计**: 支持多设备访问

### 8.3 主要页面
- **仪表盘**: 数据统计和图表展示
- **项目列表**: 表格形式展示项目信息
- **申请表单**: 项目申请、英才申请等
- **审核界面**: 项目审核、状态管理
- **新闻管理**: 新闻编辑、轮播管理等

## 9. 系统配置

### 9.1 应用配置 (config/app.php)
```php
return [
    'app_namespace' => '',
    'with_route' => true,
    'default_app' => 'index',
    'default_timezone' => 'Asia/Shanghai',
    'show_error_msg' => false,
];
```

### 9.2 数据库配置 (config/database.php)
- **数据库类型**: MySQL
- **字符集**: utf8mb4
- **时区**: Asia/Shanghai
- **连接池**: 支持连接池配置

### 9.3 依赖配置 (composer.json)
```json
{
    "require": {
        "php": ">=8.0.0",
        "topthink/framework": "^8.0",
        "topthink/think-orm": "^3.0|^4.0",
        "ramsey/uuid": "^4.7",
        "phpoffice/phpspreadsheet": "^2.1"
    }
}
```

## 10. 安全机制

### 10.1 身份认证
- **Session管理**: 基于ThinkPHP的Session机制
- **登录验证**: 用户名密码验证
- **权限控制**: 基于角色的访问控制

### 10.2 数据安全
- **SQL注入防护**: 使用ThinkPHP的查询构造器
- **XSS防护**: 输入数据过滤和输出转义
- **CSRF防护**: 表单令牌验证

### 10.3 文件安全
- **文件上传**: 类型和大小限制
- **路径安全**: 防止目录遍历攻击
- **权限控制**: 文件访问权限管理

## 11. 日志系统

### 11.1 操作日志
- **用户操作**: 记录用户的关键操作
- **系统日志**: 记录系统运行状态
- **错误日志**: 记录异常和错误信息

### 11.2 日志函数
```php
function LogExecution($message)
{
    // 记录执行日志
    // 包含时间戳、用户信息、操作内容
}
```

## 12. 部署说明

### 12.1 环境要求
- **PHP**: >= 8.0.0
- **MySQL**: >= 5.7
- **Web服务器**: Apache/Nginx
- **扩展**: PDO、MySQL、JSON、Fileinfo等

### 12.2 安装步骤
1. **克隆项目**: 下载项目代码
2. **安装依赖**: `composer install`
3. **配置数据库**: 修改数据库配置文件
4. **导入数据**: 执行SQL文件创建数据表
5. **配置Web服务器**: 设置虚拟主机
6. **权限设置**: 设置目录读写权限

### 12.3 配置说明
- **URL重写**: 启用Apache mod_rewrite或Nginx rewrite
- **文件权限**: runtime目录需要写权限
- **上传目录**: public/uploads目录需要写权限

## 13. 开发规范

### 13.1 代码规范
- **PSR-4**: 自动加载规范
- **命名规范**: 类名大驼峰，方法名小驼峰
- **注释规范**: 类和方法必须有注释

### 13.2 数据库规范
- **表名**: 小写字母，下划线分隔
- **字段名**: 小写字母，下划线分隔
- **主键**: 统一使用id或uid
- **软删除**: 使用is_delete字段

### 13.3 文件组织
- **控制器**: 按功能模块组织
- **模型**: 一个表对应一个模型
- **视图**: 按控制器组织目录结构
- **静态资源**: CSS、JS、图片分类存放

## 14. 测试说明

### 14.1 功能测试
- **用户登录**: 测试不同角色的登录功能
- **项目申请**: 测试项目申请流程
- **审核流程**: 测试多级审核功能
- **数据导出**: 测试Excel导出功能

### 14.2 性能测试
- **并发访问**: 测试系统并发处理能力
- **数据查询**: 测试大数据量查询性能
- **文件上传**: 测试文件上传功能

### 14.3 安全测试
- **权限验证**: 测试权限控制机制
- **SQL注入**: 测试SQL注入防护
- **XSS攻击**: 测试XSS防护机制

## 15. 维护说明

### 15.1 日常维护
- **日志清理**: 定期清理系统日志
- **数据备份**: 定期备份数据库
- **性能监控**: 监控系统运行状态

### 15.2 故障处理
- **错误排查**: 查看错误日志定位问题
- **数据恢复**: 从备份恢复数据
- **系统重启**: 必要时重启Web服务

### 15.3 升级说明
- **版本兼容**: 确保新版本兼容性
- **数据迁移**: 执行数据库迁移脚本
- **功能测试**: 升级后进行全面测试

## 16. 扩展开发

### 16.1 新功能开发
- **模块化设计**: 按功能模块组织代码
- **接口设计**: 定义清晰的API接口
- **文档维护**: 及时更新开发文档

### 16.2 第三方集成
- **API接口**: 提供RESTful API
- **数据导入**: 支持Excel、CSV数据导入
- **消息通知**: 集成邮件、短信通知

### 16.3 性能优化
- **缓存机制**: 使用Redis缓存热点数据
- **数据库优化**: 优化SQL查询和索引
- **前端优化**: 压缩静态资源，使用CDN

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护人员**: 开发团队 