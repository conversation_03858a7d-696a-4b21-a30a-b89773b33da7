{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/news/classes.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">新闻</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/news-classes">板块管理</a>
</p>


    <!-- 对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="新闻板块"
      width="30%"
      
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="100px"
        label-position="right"
      >
        <!-- 板块名输入 -->
        <el-form-item label="板块名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入板块名称" />
        </el-form-item>
 
        <!-- 排位输入 -->
        <el-form-item label="显示排位" prop="rank">
          <el-input
            v-model.number="form.rank"
            type="number"
            placeholder="请输入数字排位"
            :min="1"
            :max="100"
          />
        </el-form-item>
      </el-form>
 
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">确认</el-button>
      </template>
    </el-dialog>

<el-row>
  <el-button plain type="primary" @click="handleedit">新增新闻板块</el-button>

</el-row>
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
          <el-table-column
            prop="name"
            label="板块名称"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="rank"
          label="排位"
          min-width="180"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="update(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
      
</div>
<script src="../../static/js/news/classes.js"></script>
</body>
</html>