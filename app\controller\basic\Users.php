<?php

namespace app\controller\basic;

use app\BaseController;
use app\model\User;
use app\model\Usermode;
use app\model\Department;
use app\model\Major;
use think\facade\Db;

class Users extends BaseController
{
    public function index(){
        return view('basic/users');
    }
    
    public function select_users(){
        // 检查用户是否有权限管理用户
        if (!$this->checkPermission('user_manage')) {
            LogExecution(session('user.username') . '尝试访问用户管理');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $search = input('post.search');
        $where = [];
        $where[] = ['u.status', '=', 0]; // 只查询正常状态的用户
        
        // 根据用户权限过滤数据
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        // 院级管理员只能查看本学院的用户
        if (in_array($userMode, [3, 5])) {
            $where[] = ['u.college', '=', $userCollege];
        }
        
        if (!empty($search['texts'])) {
            foreach ($search['texts'] as $text) {
                $where[] = ['u.username|u.name|u.phone|u.email', 'like', '%'.$text.'%'];
            }
        }
        
        // 使用查询构造器进行多表查询
        $data = User::alias('u')
            ->join('usermode md', 'md.id=u.usermode', 'LEFT')
            ->join('department d', 'd.id=u.college AND d.is_delete=0', 'LEFT')
            ->join('major m', 'm.id=u.major AND m.is_delete=0', 'LEFT')
            ->where($where)
            ->field('
                u.username,u.name,u.email,u.phone,u.job,u.grade,u.status,u.usermode,
                u.college,u.major,
                md.group_name as m_name,
                d.name as department,
                m.name as major
            ')
            ->select();
            
        return json(['status' => 'success', 'message' => [
            'total' => count($data),
            'data' => $data
        ]]);
    }
    
    public function get_usermodes(){
        $usermodes = Usermode::where('is_delete', 0)->select();
        return json(['status' => 'success', 'message' => $usermodes]);
    }
    
    public function get_departments(){
        $departments = Department::where('is_delete', 0)->select();
        return json(['status' => 'success', 'message' => $departments]);
    }
    
    public function get_majors(){
        $department_id = input('post.department_id');
        
        $query = Major::where('is_delete', 0);
        
        // 如果提供了部门ID，则筛选该部门下的专业
        if ($department_id) {
            $query->where('department_id', $department_id);
        }
        
        $majors = $query->select();
        return json(['status' => 'success', 'message' => $majors]);
    }
    
    public function delete(){
        $username = input('post.username');
        
        // 检查用户是否存在
        $user = User::where('username', $username)->where('status', 0)->find();
        if (!$user) {
            return json(['status' => 'error', 'message' => '用户不存在']);
        }
        
        // 软删除用户（设置状态为禁用）
        if (User::where('username', $username)->update(['status' => 1])) {
            return json(['status' => 'success', 'message' => '删除成功']);
        } else {
            return json(['status' => 'error', 'message' => '删除失败']);
        }
    }
    
    public function manage($type){
        $data = input('post.data');
        
        // 添加调试日志
        trace('接收到的数据: ' . json_encode($data), 'debug');
        
        if ($type == 'add'){
            // 检查用户名是否已存在
            if (User::where('username', $data['username'])->find()) {
                return json(['status' => 'error', 'message' => '用户名已存在']);
            }
            
            $add = [
                'username' => $data['username'],
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'job' => $data['job'],
                'grade' => $data['grade'],
                'usermode' => $data['usermode'],
                'college' => $data['college'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'status' => 0
            ];
            
            // 只有当major不为空时才添加
            if (!empty($data['major'])) {
                $add['major'] = $data['major'];
            }
            
            if (User::insert($add)) {
                return json(['status' => 'success', 'message' => '新建成功']);
            } else {
                return json(['status' => 'error', 'message' => '新建失败']);
            }
            
        } elseif ($type == 'update'){
            $update = [
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'job' => $data['job'],
                'grade' => $data['grade'],
                'usermode' => $data['usermode'],
                'college' => $data['college']
            ];
            
            // 只有当major不为空时才更新
            if (!empty($data['major'])) {
                $update['major'] = $data['major'];
            }
            
            // 如果提供了新密码，则更新密码
            if (!empty($data['password'])) {
                $update['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            if (User::where('username', $data['username'])->update($update)) {
                return json(['status' => 'success', 'message' => '修改成功']);
            } else {
                return json(['status' => 'error', 'message' => '修改失败']);
            }
        }
    }
    
    public function reset_password(){
        $username = input('post.username');
        $new_password = '123456'; // 默认密码
        
        if (User::where('username', $username)->update(['password' => password_hash($new_password, PASSWORD_DEFAULT)])) {
            return json(['status' => 'success', 'message' => '密码重置成功，新密码为：'.$new_password]);
        } else {
            return json(['status' => 'error', 'message' => '密码重置失败']);
        }
    }
    
    /**
     * 导出用户数据
     */
    public function export_users()
    {
        $search = input('post.search');
        $where = [];
        $where[] = ['u.status', '=', 0]; // 只查询正常状态的用户
        
        if (!empty($search['texts'])) {
            foreach ($search['texts'] as $text) {
                $where[] = ['u.username|u.name|u.phone|u.email', 'like', '%'.$text.'%'];
            }
        }
        
        // 使用查询构造器进行多表查询
        $data = User::alias('u')
            ->join('usermode md', 'md.id=u.usermode', 'LEFT')
            ->join('department d', 'd.id=u.college AND d.is_delete=0', 'LEFT')
            ->join('major m', 'm.id=u.major AND m.is_delete=0', 'LEFT')
            ->where($where)
            ->field('
                u.username,u.name,u.email,u.phone,u.job,u.grade,u.status,u.usermode,
                u.college,u.major,
                md.group_name as m_name,
                d.name as department,
                m.name as major
            ')
            ->select();
            
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '用户名',
            '姓名', 
            '联系电话',
            '电子邮箱',
            '用户组',
            '部门',
            '专业',
            '学历/职称',
            '年级',
            '状态'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatUserData($data);
        
        // 生成文件名
        $filename = $exportService->generateFilename('成员管理列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
}