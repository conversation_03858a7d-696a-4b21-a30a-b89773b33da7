<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcmidchange;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Member;
use app\model\Teacher;
use app\model\User;
use think\facade\Db;

class Midchange extends BaseController
{
    /**
     * 中期变更申请页面
     */
    public function index($uid){
        if (!$uid){
            LogExecution('中期变更申请失败，uid不存在，请重新登录');
            return ['status'=>'error','message'=>'中期变更申请失败，请重新登录'];
        }
        
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('中期变更申请失败，项目不存在');
            return ['status'=>'error','message'=>'中期变更申请失败，项目不存在'];
        }

        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }

        // 检查项目状态是否允许申请中期变更
        if (!$this->canApplyMidchange($project['status'])) {
            LogExecution(session('user.name').'当前不是可申请中期变更状态');
            return json(['status'=>'error','message' => '当前不是可申请中期变更状态']);
        }

        // 获取当前项目成员和教师信息
        $current_members = Member::alias('m')
            ->join('user u', 'u.username=m.username','LEFT')
            ->where('m.is_delete',0)
            ->where('u.status',0)
            ->where('m.uid',$uid)
            ->field('u.name as label,m.username as value,m.rank')
            ->order('m.rank', 'asc')
            ->select();
        // 保证负责人在第一位
        $current_members = $current_members->toArray();
        usort($current_members, function($a, $b) {
            return $a['rank'] - $b['rank'];
        });
        // 新成员选择不包含负责人
        $all_students = User::where('status',0)
            ->where('usermode',1)
            ->where('username', '<>', session('user.username'))
            ->field('username as value,name as label')
            ->select();

        $current_teachers = Teacher::alias('t')
            ->join('user u', 'u.username=t.username','LEFT')
            ->where('t.is_delete',0)
            ->where('u.status',0)
            ->where('t.uid',$uid)
            ->where('t.type',0)
            ->field('t.username as value,u.name as label')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->order('t.rank', 'asc')
            ->select();

        // 获取所有可选的学生和教师
        $all_teachers = User::where('status',0)
            ->where('usermode',2)
            ->field('username as value,name as label')
            ->select();

        // 检查是否已有中期变更申请
        $midchange = Dcmidchange::where('uid',$uid)->find();

        // 新增：初始化form的已选项
        $init_new_members = [];
        $init_new_teachers = [];
        if ($midchange && $midchange['new_members']) {
            $members_arr = json_decode($midchange['new_members'], true) ?: [];
            // 只保留value和label都非空的项
            $init_new_members = array_values(array_filter($members_arr, function($m){
                return !empty($m['value']) && !empty($m['label']);
            }));
        }
        if ($midchange && $midchange['new_teachers']) {
            $teachers_arr = json_decode($midchange['new_teachers'], true) ?: [];
            $init_new_teachers = array_values(array_filter($teachers_arr, function($t){
                return !empty($t['value']) && !empty($t['label']);
            }));
        }

        return view('dcmanage/midchange', [
            'project' => $project,
            'current_members' => $current_members,
            'current_teachers' => $current_teachers,
            'all_students' => $all_students,
            'all_teachers' => $all_teachers,
            'midchange' => $midchange,
            'init_new_members' => $init_new_members,
            'init_new_teachers' => $init_new_teachers
        ]);
    }

    /**
     * 提交中期变更申请
     */
    public function apply_midchange($uid){
        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }
        
        $data = input('post.data');
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('中期变更申请失败，项目不存在');
            return json(['status'=>'error','message' => '中期变更申请失败，项目不存在']);
        }

        // 检查项目状态是否允许申请中期变更
        if (!$this->canApplyMidchange($project['status'])) {
            LogExecution(session('user.name').'当前不是可申请中期变更状态');
            return json(['status'=>'error','message' => '当前不是可申请中期变更状态']);
        }

        // 验证数据
        if (!$data['change_items'] || !$data['change_reason']) {
            LogExecution(session('user.name').'中期变更申请数据不完整');
            return json(['status'=>'error','message' => '请填写完整的变更申请信息']);
        }

        // 验证字数限制
        if (mb_strlen($data['change_items']) > 500) {
            return json(['status'=>'error','message' => '变更事项不能超过500字']);
        }
        if (mb_strlen($data['change_reason']) > 500) {
            return json(['status'=>'error','message' => '变更原因不能超过500字']);
        }

        // 验证成员和教师数量
        $newMembers = $data['new_members'] ?? [];
        $newTeachers = $data['new_teachers'] ?? [];

        // 自动排除负责人（当前用户）
        $filteredMembers = array_filter($newMembers, function($m) {
            return $m['value'] !== session('user.username');
        });
        if (count($filteredMembers) > 4) {
            return json(['status'=>'error','message' => '项目成员最多选择4名（不含队长）']);
        }
        if (count($newTeachers) > 2) {
            return json(['status'=>'error','message' => '指导教师最多选择2名']);
        }

        $new_status = $this->calculateMidchangeStatus($project['status']);

        // 保存原成员和教师快照，补充name/username
        $oldMembersRaw = Member::alias('m')
            ->join('user u', 'u.username=m.username','LEFT')
            ->where('m.is_delete',0)
            ->where('u.status',0)
            ->where('m.uid',$uid)
            ->field('m.username,u.name')
            ->order('m.rank', 'asc')
            ->select();
        $oldMembers = [];
        foreach ($oldMembersRaw as $m) {
            $oldMembers[] = [
                'name' => $m['name'],
                'username' => $m['username']
            ];
        }
        $oldTeachersRaw = Teacher::alias('t')
            ->join('user u', 'u.username=t.username','LEFT')
            ->where('t.is_delete',0)
            ->where('t.uid',$uid)
            ->where('t.type',0)
            ->field('t.username,u.name')
            ->order('t.rank', 'asc')
            ->select();
        $oldTeachers = [];
        foreach ($oldTeachersRaw as $t) {
            $oldTeachers[] = [
                'name' => $t['name'],
                'username' => $t['username']
            ];
        }

        // 整理数据
        $midchange = [
            'uid' => $uid,
            'change_items' => $data['change_items'],
            'change_reason' => $data['change_reason'],
            'new_members' => json_encode(array_values($filteredMembers)),
            'new_teachers' => json_encode($data['new_teachers'] ?? []),
            'old_members' => json_encode($oldMembers),
            'old_teachers' => json_encode($oldTeachers)
        ];

        // 检查是否已存在中期变更申请
        $existingMidchange = Dcmidchange::where('uid', $uid)->find();
        
        if ($existingMidchange) {
            // 已存在变更申请，更新数据
            $result = $this->updateMidchange($midchange, $uid, $new_status);
        } else {
            // 新申请
            $result = $this->submitNewMidchange($midchange, $uid, $new_status);
        }
        // 立即应用新成员和教师
        \app\controller\dcmanage\Check::updateProjectDataAfterMidchange($uid);
        return $result;
    }

    /**
     * 检查是否可以申请中期变更
     */
    private function canApplyMidchange($status) {
        // 状态4：已立项待提交中期报告
        // 状态36：指导教师中期变更驳回
        // 状态37：学院中期变更驳回
        // 状态38：学校中期变更驳回
        $allowedStatus = [4, 36, 37, 38];
        return in_array($status, $allowedStatus);
    }

    /**
     * 计算中期变更申请提交后的新状态
     */
    private function calculateMidchangeStatus($currentStatus) {
        $statusMap = [
            4 => 33,   // 新申请 -> 等待教师审核
            36 => 33,  // 教师驳回后重新申请 -> 等待教师审核
            37 => 34,  // 学院驳回后重新申请 -> 等待学院审核
            38 => 35,  // 学校驳回后重新申请 -> 等待学校审核
        ];
        return $statusMap[$currentStatus] ?? $currentStatus;
    }

    /**
     * 提交新的中期变更申请
     */
    private function submitNewMidchange($midchange, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '中期变更申请',
            'remark' => '学生提交中期变更申请'
        ];
        
        try {
            Db::transaction(function () use ($midchange, $uid, $new_status, $progress) {
                // 插入中期变更申请
                $midchangeResult = Dcmidchange::insert($midchange);
                // 更新项目状态
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                // 插入进度
                $progressResult = Dcprogress::insert($progress);
                
                if (!$midchangeResult || !$projectResult || !$progressResult) {
                    throw new \Exception('中期变更申请提交失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'中期变更申请提交成功');
            return ['status' => 'success', 'message' => '中期变更申请提交成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '中期变更申请提交失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '中期变更申请提交失败，数据表插入异常'];
        }
    }

    /**
     * 更新中期变更申请
     */
    private function updateMidchange($midchange, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '修改中期变更申请',
            'remark' => '学生修改中期变更申请'
        ];
        
        try {
            Db::transaction(function () use ($midchange, $uid, $new_status, $progress) {
                // 更新现有数据而不是删除后重新插入
                $midchangeResult = Dcmidchange::where('uid', $uid)->update([
                    'change_items' => $midchange['change_items'],
                    'change_reason' => $midchange['change_reason'],
                    'new_members' => $midchange['new_members'],
                    'new_teachers' => $midchange['new_teachers']
                ]);
                
                $progressResult = Dcprogress::insert($progress);
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                
                if (!$midchangeResult || !$progressResult || !$projectResult) {
                    throw new \Exception('中期变更申请修改失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'中期变更申请修改成功');
            return ['status' => 'success', 'message' => '中期变更申请修改成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '中期变更申请修改失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '中期变更申请修改失败，数据表插入异常'];
        }
    }
} 