<?php
// 测试轮播数据
require_once 'vendor/autoload.php';

use think\facade\Db;

// 配置数据库连接
$config = [
    'type' => 'mysql',
    'hostname' => '127.0.0.1',
    'database' => 'cxcysys',
    'username' => 'cxcysys',
    'password' => 'cxcysys',
    'hostport' => '3306',
    'charset' => 'utf8mb4',
];

// 连接数据库
Db::setConfig($config);

try {
    // 查询轮播表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'carousel'");
    echo "轮播表存在: " . (count($tables) > 0 ? '是' : '否') . "\n";
    
    if (count($tables) > 0) {
        // 查询所有轮播数据
        $carousels = Db::table('carousel')->select();
        echo "轮播数据总数: " . count($carousels) . "\n";
        
        foreach ($carousels as $carousel) {
            echo "ID: {$carousel['id']}, 标题: {$carousel['title']}, 状态: {$carousel['status']}, 删除标记: {$carousel['is_delete']}\n";
        }
        
        // 查询启用的轮播
        $activeCarousels = Db::table('carousel')
            ->where('status', 1)
            ->where('is_delete', 0)
            ->select();
        echo "启用的轮播数量: " . count($activeCarousels) . "\n";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
} 