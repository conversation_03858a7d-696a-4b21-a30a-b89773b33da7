<?php
// 测试 dcmidchange 表结构
require_once 'vendor/autoload.php';

// 初始化 ThinkPHP
$app = new think\App();
$app->initialize();

use think\facade\Db;

try {
    // 检查表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'dcmidchange'");
    echo "dcmidchange 表存在: " . (count($tables) > 0 ? '是' : '否') . "\n";
    
    if (count($tables) > 0) {
        // 获取表结构
        $columns = Db::query("DESCRIBE dcmidchange");
        echo "表结构:\n";
        foreach ($columns as $column) {
            echo "- {$column['Field']}: {$column['Type']}\n";
        }
    } else {
        echo "dcmidchange 表不存在，需要创建\n";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
} 