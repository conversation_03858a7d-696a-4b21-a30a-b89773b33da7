<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Dcproject;
use app\model\Dctype;
use app\model\Department;
use app\model\Jsaward;
use app\model\Jscompetition;
use app\model\Jslevel;
use app\model\Jsstatus;
use app\model\Jssetting;
use Ramsey\Uuid\Uuid;
use think\facade\Db;
use Exception;

class Competition extends BaseController
{
    public function index()
    {
        LogExecution('进入竞赛管理');
        
        // 检查用户是否有权限管理竞赛
        if (!$this->checkPermission('js_manage')) {
            LogExecution(session('user.username') . '尝试访问竞赛管理');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $l_level = Jslevel::
        where('is_delete',0)
            ->field('id as value,name as label')
            ->select();
        $search=[
            'level'=>Jslevel::where('is_delete',0)->field('id, name')->select(),
        ];
        return view('jsmanage/competition',['l_level'=>$l_level,'search'=>$search]);
    }
    
    public function select_competition(){
        // 检查用户是否有权限查看竞赛
        if (!$this->checkPermission('js_manage')) {
            LogExecution(session('user.username') . '尝试查看竞赛列表');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $search=input('post.search');
        $where=[];
        
        // 竞赛管理不按学院过滤，所有管理员都可以查看所有竞赛
        
        if ($search['level']!='all'){
            $where[]=['c.level','=',$search['level']];
        }
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[]=['c.cuid|c.url|c.name','like','%'.$text.'%'];
            }
        }
        
        $data = Jscompetition::alias('c')
            ->field('
        c.cuid,c.name,c.url,c.remark,c.level,
        s.active,s.years,s.member,s.teacher,s.outstu,s.outstu_num,s.outtea,s.outtea_num,
        l.name as l_level
    ')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->join('jssetting s','s.cuid=c.cuid','LEFT')
            ->where($where)
            ->where('c.is_delete', 0)
            ->order('s.active','desc')
            ->order('c.updated_at')
            ->select();
        LogExecution('查看竞赛列表');

        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($data),
            'data'=>$data
        ]]);
    }
    
    public function addCompetition()
    {
        // 检查用户是否有权限添加竞赛
        if (!$this->checkPermission('js_manage')) {
            LogExecution(session('user.username') . '尝试添加竞赛');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $data = input('post.');
        
        // 验证必填字段
        if (empty($data['name'])) {
            return json(['status' => 'error', 'message' => '竞赛名称不能为空']);
        }
        if (empty($data['level'])) {
            return json(['status' => 'error', 'message' => '请选择竞赛级别']);
        }
        if (empty($data['url'])) {
            return json(['status' => 'error', 'message' => '官网链接不能为空']);
        }
        if ($data['member'] <= 0) {
            return json(['status' => 'error', 'message' => '成员数量必须大于0']);
        }
        if ($data['teacher'] <= 0) {
            return json(['status' => 'error', 'message' => '指导教师数量必须大于0']);
        }
        
        // 检查竞赛名称是否已存在
        $existingCompetition = Jscompetition::where('name', $data['name'])
            ->where('is_delete', 0)
            ->find();
        if ($existingCompetition) {
            return json(['status' => 'error', 'message' => '竞赛名称已存在']);
        }
        
        // 生成竞赛ID
        $cuid = Uuid::uuid4()->toString();
        
        // 竞赛不按学院分类，所有管理员都可以添加竞赛
        
        // 开始事务
        Db::startTrans();
        try {
            // 插入竞赛基本信息
            $competitionData = [
                'cuid' => $cuid,
                'name' => $data['name'],
                'level' => $data['level'],
                'url' => $data['url'],
                'remark' => $data['remark'] ?? '',
                'is_delete' => 0
            ];
            
            $competitionResult = Jscompetition::insert($competitionData);
            
            // 插入竞赛设置信息
            $settingData = [
                'cuid' => $cuid,
                'active' => $data['active'] ?? 0,
                'years' => $data['years'] ?? '[]',
                'member' => $data['member'],
                'teacher' => $data['teacher'],
                'outstu' => $data['outstu'] ?? 0,
                'outstu_num' => $data['outstu_num'] ?? 0,
                'outtea' => $data['outtea'] ?? 0,
                'outtea_num' => $data['outtea_num'] ?? 0
            ];
            
            $settingResult = Jssetting::insert($settingData);
            
            // 检查插入结果
            if ($competitionResult && $settingResult) {
                // 提交事务
                Db::commit();
                
                LogExecution('添加竞赛成功：' . $data['name']);
                return json(['status' => 'success', 'message' => '竞赛添加成功']);
            } else {
                // 回滚事务
                Db::rollback();
                LogExecution('添加竞赛失败：数据库插入失败');
                return json(['status' => 'error', 'message' => '添加竞赛失败，请检查数据']);
            }
            
        } catch (Exception $e) {
            // 捕获异常，回滚事务
            Db::rollback();
            LogExecution('添加竞赛失败：' . $e->getMessage());
            return json(['status' => 'error', 'message' => '添加竞赛失败：' . $e->getMessage()]);
        }
    }
    
    public function updateCompetition()
    {
        // 检查用户是否有权限修改竞赛
        if (!$this->checkPermission('js_manage')) {
            LogExecution(session('user.username') . '尝试修改竞赛');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $data = input('post.');
        
        // 验证必填字段
        if (empty($data['cuid'])) {
            return json(['status' => 'error', 'message' => '竞赛ID不能为空']);
        }
        if (empty($data['name'])) {
            return json(['status' => 'error', 'message' => '竞赛名称不能为空']);
        }
        if (empty($data['l_level'])) {
            return json(['status' => 'error', 'message' => '请选择竞赛级别']);
        }
        if (empty($data['url'])) {
            return json(['status' => 'error', 'message' => '官网链接不能为空']);
        }
        if ($data['member'] <= 0) {
            return json(['status' => 'error', 'message' => '成员数量必须大于0']);
        }
        if ($data['teacher'] <= 0) {
            return json(['status' => 'error', 'message' => '指导教师数量必须大于0']);
        }
        
        // 检查竞赛是否存在
        $existingCompetition = Jscompetition::where('cuid', $data['cuid'])
            ->where('is_delete', 0)
            ->find();
        if (!$existingCompetition) {
            return json(['status' => 'error', 'message' => '竞赛不存在']);
        }
        
        // 所有管理员都可以修改竞赛
        
        // 检查竞赛名称是否已被其他竞赛使用
        $nameExists = Jscompetition::where('name', $data['name'])
            ->where('cuid', '<>', $data['cuid'])
            ->where('is_delete', 0)
            ->find();
        if ($nameExists) {
            return json(['status' => 'error', 'message' => '竞赛名称已存在']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新竞赛基本信息
            $competitionData = [
                'name' => $data['name'],
                'level' => $data['l_level'],
                'url' => $data['url'],
                'remark' => $data['remark'] ?? ''
            ];
            
            // 竞赛不按学院分类
            
            $competitionResult = Jscompetition::where('cuid', $data['cuid'])->update($competitionData);
            
            // 更新竞赛设置信息
            $settingData = [
                'active' => $data['active'] ?? 0,
                'years' => $data['years'] ?? '[]',
                'member' => $data['member'],
                'teacher' => $data['teacher'],
                'outstu' => $data['outstu'] ?? 0,
                'outstu_num' => $data['outstu_num'] ?? 0,
                'outtea' => $data['outtea'] ?? 0,
                'outtea_num' => $data['outtea_num'] ?? 0
            ];
            
            $settingResult = Jssetting::where('cuid', $data['cuid'])->update($settingData);
            
            // 检查更新结果
            if ($competitionResult !== false && $settingResult !== false) {
                // 提交事务
                Db::commit();
                
                LogExecution('修改竞赛成功：' . $data['name']);
                return json(['status' => 'success', 'message' => '竞赛修改成功']);
            } else {
                // 回滚事务
                Db::rollback();
                LogExecution('修改竞赛失败：数据库更新失败');
                return json(['status' => 'error', 'message' => '修改竞赛失败，请检查数据']);
            }
            
        } catch (Exception $e) {
            // 捕获异常，回滚事务
            Db::rollback();
            LogExecution('修改竞赛失败：' . $e->getMessage());
            return json(['status' => 'error', 'message' => '修改竞赛失败：' . $e->getMessage()]);
        }
    }
    
    public function deleteCompetition()
    {
        // 检查用户是否有权限删除竞赛
        if (!$this->checkPermission('js_manage')) {
            LogExecution(session('user.username') . '尝试删除竞赛');
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        $cuid = input('post.cuid');
        
        if (empty($cuid)) {
            return json(['status' => 'error', 'message' => '竞赛ID不能为空']);
        }
        
        // 检查竞赛是否存在
        $existingCompetition = Jscompetition::where('cuid', $cuid)
            ->where('is_delete', 0)
            ->find();
        if (!$existingCompetition) {
            return json(['status' => 'error', 'message' => '竞赛不存在']);
        }
        
        // 所有管理员都可以删除竞赛
        
        // 检查是否有关联的项目
        $hasProjects = Jsproject::where('cuid', $cuid)
            ->where('is_delete', 0)
            ->find();
        if ($hasProjects) {
            return json(['status' => 'error', 'message' => '该竞赛下有关联的项目，无法删除']);
        }
        
        // 软删除竞赛
        $result = Jscompetition::where('cuid', $cuid)->update(['is_delete' => 1]);
        
        if ($result) {
            LogExecution('删除竞赛成功：' . $existingCompetition['name']);
            return json(['status' => 'success', 'message' => '竞赛删除成功']);
        } else {
            LogExecution('删除竞赛失败：' . $existingCompetition['name']);
            return json(['status' => 'error', 'message' => '删除竞赛失败']);
        }
    }
    
    /**
     * 检查权限的辅助方法
     */
    protected function checkPermission($permission)
    {
        $userMode = session('user.usermode');
        
        // 超级管理员拥有所有权限
        if ($userMode == 11) {
            return true;
        }
        
        // 根据权限类型检查
        switch ($permission) {
            case 'js_manage':
                return in_array($userMode, [5, 6, 11]);
            case 'js_view':
                return in_array($userMode, [1, 2, 5, 6, 11]);
            default:
                return false;
        }
    }
}