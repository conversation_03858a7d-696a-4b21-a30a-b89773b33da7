<?php

namespace app\validate;

use think\Validate;

class CheckAddDc  extends Validate
{
    //校验新建大创项目
    protected $rule = [
        'name'          => 'require',
        'time'          => 'require',
        'members'       => 'require',
        'teachers'      => 'require',
        'type'          => 'require',
        'period'        => 'require',
        'introduction'  => 'require',
        'reason'        => 'require',
        'innovation'    => 'require',
        'schedule'      => 'require',
        'budget'        => 'require',
        'plan'          => 'require',
        'fileurl'       => 'require',
        'expected'      => 'require',
        'shangyehua'      => 'require',
    ];
    
    // 修改项目时的验证规则
    protected $updateRule = [
        'name'          => 'require',
        'time'          => 'require',
        'members'       => 'require',
        'teachers'      => 'require',
        'type'          => 'require',
        'period'        => 'require',
        'introduction'  => 'require',
        'reason'        => 'require',
        'innovation'    => 'require',
        'schedule'      => 'require',
        'budget'        => 'require',
        'plan'          => 'require',
        'expected'      => 'require',
        'shangyehua'      => 'require',
    ];

    // 定义错误信息
    protected $message = [
        'name.require'          => '请输入项目名称',
        'time.require'          => '请输入立项时间',
        'members.require'       => '请输入项目成员',
        'teachers.require'      => '请输入指导教师',
        'type.require'          => '请输入项目类型',
        'period.require'        => '请输入项目周期',
        'introduction.require'  => '请输入项目简介',
        'reason.require'        => '请输入申请理由',
        'innovation.require'    => '请输入项目特色及创新点',
        'schedule.require'      => '请输入项目进度安排',
        'budget.require'        => '请输入项目经费',
        'plan.require'          => '请输入经费使用计划',
        'fileurl.require'       => '请上传项目方案',
        'expected.require'      => '请输入项目预期成果',
        'shangyehua.require'      => '请选择是否商业化',

    ];
}