{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/dashboard.css">
<script src="../../static/js/echarts.min.js"></script>

<div id="app">
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-info">
        <h2>欢迎回来，{$user.usermode_text}：{$user.name}</h2>
        <p class="current-time">{{ currentTime }}</p>
      </div>
      <div class="quick-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(stat, index) in statistics" :key="index">
          <div class="stat-card" :style="{ borderLeftColor: stat.color }">
            <div class="stat-icon" :style="{ color: stat.color }">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}{{ stat.unit }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>{{ charts.mainChart ? charts.mainChart.title : '数据统计' }}</h3>
            </div>
            <div id="mainChart" class="chart-container"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>{{ getSecondaryChartTitle() }}</h3>
            </div>
            <div id="secondaryChart" class="chart-container"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 最新动态和待办事项 -->
    <div class="activities-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="activity-card">
            <div class="card-header">
              <h3>最新动态</h3>
            </div>
            <div class="activity-list">
              <div v-for="(activity, index) in recentActivities" :key="index" class="activity-item">
                <div class="activity-icon">
                  <i class="el-icon-document"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.name }}</div>
                  <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
                </div>
                <div class="activity-type">{{ activity.type }}</div>
              </div>
              <div v-if="recentActivities.length === 0" class="empty-state">
                <i class="el-icon-info"></i>
                <p>暂无最新动态</p>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="todo-card">
            <div class="card-header">
              <h3>待办事项</h3>
            </div>
            <div class="todo-list">
              <div v-for="(todo, index) in todoList" :key="index" class="todo-item">
                <div class="todo-icon">
                  <i class="el-icon-warning" :style="{ color: todo.type === 'warning' ? '#E6A23C' : '#409EFF' }"></i>
                </div>
                <div class="todo-content">
                  <div class="todo-title">{{ todo.title }}</div>
                  <div class="todo-time">{{ formatTime(todo.time) }}</div>
                </div>
              </div>
              <div v-if="todoList.length === 0" class="empty-state">
                <i class="el-icon-check"></i>
                <p>暂无待办事项</p>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</div>

<script>
new Vue({
  el: '#app',
  data() {
    return {
      currentTime: '',
      statistics: [],
      charts: {
        mainChart: null,
        barChart: null,
        pieChart: null,
        lineChart: null
      },
      recentActivities: [],
      todoList: [],
      mainChart: null,
      secondaryChart: null,
      refreshTimer: null
    }
  },
  created() {
    this.loadDashboardData();
    this.startTimeUpdate();
    this.startAutoRefresh();
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },
  methods: {
    // 加载仪表盘数据
    loadDashboardData() {
      axios.post('/admin/getDashboardData')
        .then(response => {
          if (response.data.status === 'success') {
            const data = response.data.message;
            this.statistics = data.statistics;
            this.charts = data.charts;
            this.recentActivities = data.recentActivities;
            this.todoList = data.todoList;
            
            // 渲染图表
            this.$nextTick(() => {
              this.renderCharts();
            });
          } else {
            this.$message.error('获取数据失败');
          }
        })
        .catch(error => {
          console.error('获取仪表盘数据错误:', error);
          this.$message.error('获取数据失败');
        });
    },
    
    // 渲染图表
    renderCharts() {
      // 渲染主图表
      if (this.charts.mainChart && this.charts.mainChart.data) {
        this.renderMainChart();
      }
      
      // 渲染次要图表
      if ((this.charts.barChart && this.charts.barChart.data) || 
          (this.charts.pieChart && this.charts.pieChart.data) || 
          (this.charts.lineChart && this.charts.lineChart.data)) {
        this.renderSecondaryChart();
      }
    },
    
    // 渲染主图表
    renderMainChart() {
      const chartDom = document.getElementById('mainChart');
      if (!chartDom) return;
      
      this.mainChart = echarts.init(chartDom);
      
      const option = this.getChartOption(this.charts.mainChart);
      this.mainChart.setOption(option);
    },
    
    // 渲染次要图表
    renderSecondaryChart() {
      const chartDom = document.getElementById('secondaryChart');
      if (!chartDom) return;
      
      this.secondaryChart = echarts.init(chartDom);
      
      // 按优先级获取图表数据
      let chartData = null;
      if (this.charts.barChart) {
        chartData = this.charts.barChart;
      } else if (this.charts.pieChart) {
        chartData = this.charts.pieChart;
      } else if (this.charts.lineChart) {
        chartData = this.charts.lineChart;
      }
      
      if (chartData) {
        const option = this.getChartOption(chartData);
        this.secondaryChart.setOption(option);
      }
    },
    
    // 获取图表配置
    getChartOption(chartData) {
      if (chartData.type === 'pie') {
        return {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [{
            name: chartData.title,
            type: 'pie',
            radius: '50%',
            data: chartData.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
      } else if (chartData.type === 'bar') {
        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: chartData.data.map(item => item[0])
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: chartData.title,
            type: 'bar',
            data: chartData.data.map(item => item[1]),
            itemStyle: {
              color: '#409EFF'
            }
          }]
        };
      } else if (chartData.type === 'line') {
        return {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: chartData.data.map(item => item[0])
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: chartData.title,
            type: 'line',
            data: chartData.data.map(item => item[1]),
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }]
        };
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour12: false });
    },
    
    // 开始时间更新
    startTimeUpdate() {
      this.updateCurrentTime();
      setInterval(() => {
        this.updateCurrentTime();
      }, 1000);
    },
    
    // 更新当前时间
    updateCurrentTime() {
      this.currentTime = new Date().toLocaleString('zh-CN');
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadDashboardData();
      }, 300000); // 5分钟刷新一次
    },
    
    // 手动刷新数据
    refreshData() {
      this.loadDashboardData();
      this.$message.success('数据已刷新');
    },
    

    
    // 获取次要图表标题
    getSecondaryChartTitle() {
      if (this.charts.barChart && this.charts.barChart.title) {
        return this.charts.barChart.title;
      } else if (this.charts.pieChart && this.charts.pieChart.title) {
        return this.charts.pieChart.title;
      } else if (this.charts.lineChart && this.charts.lineChart.title) {
        return this.charts.lineChart.title;
      }
      return '数据趋势';
    }
  }
});
</script>

{include file="public/_footer"} 