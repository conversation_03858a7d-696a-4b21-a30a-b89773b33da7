<?php

namespace app\service;

use app\model\Dcproject;
use app\model\Jsproject;
use app\model\Newsdetail;
use app\model\Newsclass;
use app\model\User;
use app\model\Yckuser;
use app\model\Usermode;
use app\model\Member;
use app\model\Teacher;
use app\model\Dcstatus;
use app\model\Jsstatus;
use app\model\Department;
use app\model\Carousel;
use think\facade\Db;

class DashboardService
{
    /**
     * 获取用户仪表盘数据
     */
    public static function getDashboardData($userMode, $username = '', $college = '')
    {
        $data = [
            'userInfo' => self::getUserInfo($userMode, $username),
            'statistics' => self::getStatistics($userMode, $username, $college),
            'charts' => self::getCharts($userMode, $username, $college),
            'recentActivities' => self::getRecentActivities($userMode, $username),
            'todoList' => self::getTodoList($userMode, $username, $college)
        ];
        
        return $data;
    }
    
    /**
     * 获取用户信息
     */
    private static function getUserInfo($userMode, $username)
    {
        $userModeText = Usermode::where('id', $userMode)->value('group_name') ?: '未知用户组';
        
        return [
            'userMode' => $userMode,
            'userModeText' => $userModeText,
            'username' => $username,
            'currentTime' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 获取统计数据
     */
    private static function getStatistics($userMode, $username, $college)
    {
        $stats = [];
        
        // 超级管理员 - 全局统计
        if ($userMode == 11) {
            $stats = [
                [
                    'title' => '大创项目',
                    'value' => Dcproject::where('is_delete', 0)->count(),
                    'icon' => 'el-icon-document-add',
                    'color' => '#409EFF',
                    'unit' => '个'
                ],
                [
                    'title' => '竞赛项目',
                    'value' => Jsproject::where('is_delete', 0)->count(),
                    'icon' => 'el-icon-trophy',
                    'color' => '#67C23A',
                    'unit' => '个'
                ],
                [
                    'title' => '英才库',
                    'value' => Yckuser::where('is_delete', 0)->where('status', 2)->count(),
                    'icon' => 'el-icon-star-on',
                    'color' => '#E6A23C',
                    'unit' => '人'
                ],
                [
                    'title' => '新闻总数',
                    'value' => Newsdetail::where('is_delete', 0)->count(),
                    'icon' => 'el-icon-s-comment',
                    'color' => '#F56C6C',
                    'unit' => '篇'
                ]
            ];
        }
        // 大创管理员
        elseif (in_array($userMode, [3, 4])) {
            $where = [];
            if ($userMode == 3) {
                $where[] = ['m.rank', '=', 1];
                $where[] = ['mu.college', '=', $college];
            }
            
            $totalProjects = Dcproject::alias('p')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->join('user mu', 'mu.username = m.username', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where($where)
                ->group('p.uid')
                ->count();
                
            $pendingProjects = Dcproject::alias('p')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->join('user mu', 'mu.username = m.username', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('p.status', 1) // 待审核状态
                ->where('m.is_delete', 0)
                ->where($where)
                ->group('p.uid')
                ->count();
                
            $stats = [
                [
                    'title' => '项目总数',
                    'value' => $totalProjects,
                    'icon' => 'el-icon-document-add',
                    'color' => '#409EFF',
                    'unit' => '个'
                ],
                [
                    'title' => '待审核',
                    'value' => $pendingProjects,
                    'icon' => 'el-icon-time',
                    'color' => '#E6A23C',
                    'unit' => '个'
                ],
                [
                    'title' => '已立项',
                    'value' => Dcproject::alias('p')
                        ->join('member m', 'm.uid = p.uid', 'LEFT')
                        ->join('user mu', 'mu.username = m.username', 'LEFT')
                        ->where('p.is_delete', 0)
                        ->where('p.status', 2)
                        ->where('m.is_delete', 0)
                        ->where($where)
                        ->group('p.uid')
                        ->count(),
                    'icon' => 'el-icon-check',
                    'color' => '#67C23A',
                    'unit' => '个'
                ],
                [
                    'title' => '已结题',
                    'value' => Dcproject::alias('p')
                        ->join('member m', 'm.uid = p.uid', 'LEFT')
                        ->join('user mu', 'mu.username = m.username', 'LEFT')
                        ->where('p.is_delete', 0)
                        ->where('p.status', 4)
                        ->where('m.is_delete', 0)
                        ->where($where)
                        ->group('p.uid')
                        ->count(),
                    'icon' => 'el-icon-finished',
                    'color' => '#909399',
                    'unit' => '个'
                ]
            ];
        }
        // 竞赛管理员
        elseif (in_array($userMode, [5, 6])) {
            $where = [];
            if ($userMode == 5) {
                $where[] = ['m.rank', '=', 1];
                $where[] = ['mu.college', '=', $college];
            }
            
            $totalProjects = Jsproject::alias('p')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->join('user mu', 'mu.username = m.username', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where($where)
                ->group('p.uid')
                ->count();
                
            $stats = [
                [
                    'title' => '竞赛项目',
                    'value' => $totalProjects,
                    'icon' => 'el-icon-trophy',
                    'color' => '#409EFF',
                    'unit' => '个'
                ],
                [
                    'title' => '待审核',
                    'value' => Jsproject::alias('p')
                        ->join('member m', 'm.uid = p.uid', 'LEFT')
                        ->join('user mu', 'mu.username = m.username', 'LEFT')
                        ->where('p.is_delete', 0)
                        ->where('p.status', 1)
                        ->where('m.is_delete', 0)
                        ->where($where)
                        ->group('p.uid')
                        ->count(),
                    'icon' => 'el-icon-time',
                    'color' => '#E6A23C',
                    'unit' => '个'
                ],
                [
                    'title' => '已获奖',
                    'value' => Jsproject::alias('p')
                        ->join('member m', 'm.uid = p.uid', 'LEFT')
                        ->join('user mu', 'mu.username = m.username', 'LEFT')
                        ->where('p.is_delete', 0)
                        ->where('p.status', 3)
                        ->where('m.is_delete', 0)
                        ->where($where)
                        ->group('p.uid')
                        ->count(),
                    'icon' => 'el-icon-medal',
                    'color' => '#F56C6C',
                    'unit' => '个'
                ],
                [
                    'title' => '参与学院',
                    'value' => Jsproject::alias('p')
                        ->join('member m', 'm.uid = p.uid', 'LEFT')
                        ->join('user mu', 'mu.username = m.username', 'LEFT')
                        ->where('p.is_delete', 0)
                        ->where('m.is_delete', 0)
                        ->where($where)
                        ->group('mu.college')
                        ->count(),
                    'icon' => 'el-icon-office-building',
                    'color' => '#67C23A',
                    'unit' => '个'
                ]
            ];
        }
        // 英才库管理员
        elseif ($userMode == 7) {
            $stats = [
                [
                    'title' => '英才学生',
                    'value' => Yckuser::where('is_delete', 0)->where('status', 2)->where('type', '英才学生')->count(),
                    'icon' => 'el-icon-user',
                    'color' => '#409EFF',
                    'unit' => '人'
                ],
                [
                    'title' => '精英教师',
                    'value' => Yckuser::where('is_delete', 0)->where('status', 2)->where('type', '精英教师')->count(),
                    'icon' => 'el-icon-user-solid',
                    'color' => '#67C23A',
                    'unit' => '人'
                ],
                [
                    'title' => '待审核',
                    'value' => Yckuser::where('is_delete', 0)->where('status', 1)->count(),
                    'icon' => 'el-icon-time',
                    'color' => '#E6A23C',
                    'unit' => '人'
                ],
                [
                    'title' => '平均评分',
                    'value' => round(Yckuser::where('is_delete', 0)->where('status', 2)->avg('mark'), 1),
                    'icon' => 'el-icon-star-on',
                    'color' => '#F56C6C',
                    'unit' => '分'
                ]
            ];
        }
        // 新闻管理员
        elseif (in_array($userMode, [8, 9, 10, 12, 13, 14])) {
            // 获取用户管理的新闻板块
            $newsClassId = self::getUserNewsClassId($userMode);
            
            $stats = [
                [
                    'title' => '管理新闻',
                    'value' => Newsdetail::where('is_delete', 0)->where('class', $newsClassId)->count(),
                    'icon' => 'el-icon-s-comment',
                    'color' => '#409EFF',
                    'unit' => '篇'
                ],
                [
                    'title' => '本月发布',
                    'value' => Newsdetail::where('is_delete', 0)
                        ->where('class', $newsClassId)
                        ->whereTime('created_at', 'month')
                        ->count(),
                    'icon' => 'el-icon-date',
                    'color' => '#67C23A',
                    'unit' => '篇'
                ],
                [
                    'title' => '轮播图',
                    'value' => Carousel::where('is_delete', 0)->where('status', 1)->count(),
                    'icon' => 'el-icon-picture',
                    'color' => '#E6A23C',
                    'unit' => '张'
                ],
                [
                    'title' => '板块总数',
                    'value' => Newsclass::where('is_delete', 0)->count(),
                    'icon' => 'el-icon-s-management',
                    'color' => '#F56C6C',
                    'unit' => '个'
                ]
            ];
        }
        // 学生
        elseif ($userMode == 1) {
            // 统计自己参与的大创、竞赛、是否在英才库
            $dcCount = Dcproject::alias('p')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->group('p.uid')
                ->count();
            $jsCount = Jsproject::alias('p')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->group('p.uid')
                ->count();
            $inYck = Yckuser::where('is_delete', 0)->where('username', $username)->where('status', 2)->count() > 0 ? '是' : '否';

            $stats = [
                [
                    'title' => '参与大创项目',
                    'value' => $dcCount,
                    'icon' => 'el-icon-document-add',
                    'color' => '#409EFF',
                    'unit' => '个'
                ],
                [
                    'title' => '参与竞赛项目',
                    'value' => $jsCount,
                    'icon' => 'el-icon-trophy',
                    'color' => '#67C23A',
                    'unit' => '个'
                ],
                [
                    'title' => '是否在英才库',
                    'value' => $inYck,
                    'icon' => 'el-icon-star-on',
                    'color' => '#E6A23C',
                    'unit' => ''
                ]
            ];
        }
        // 教师
        elseif ($userMode == 2) {
            // 统计自己指导的大创、竞赛、是否在英才库
            $dcCount = Dcproject::alias('p')
                ->join('teacher t', 't.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('t.is_delete', 0)
                ->where('t.username', $username)
                ->group('p.uid')
                ->count();
            $jsCount = Jsproject::alias('p')
                ->join('teacher t', 't.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('t.is_delete', 0)
                ->where('t.username', $username)
                ->group('p.uid')
                ->count();
            $inYck = Yckuser::where('is_delete', 0)->where('username', $username)->where('status', 2)->count() > 0 ? '是' : '否';

            $stats = [
                [
                    'title' => '指导大创项目',
                    'value' => $dcCount,
                    'icon' => 'el-icon-document-add',
                    'color' => '#409EFF',
                    'unit' => '个'
                ],
                [
                    'title' => '指导竞赛项目',
                    'value' => $jsCount,
                    'icon' => 'el-icon-trophy',
                    'color' => '#67C23A',
                    'unit' => '个'
                ],
                [
                    'title' => '是否在英才库',
                    'value' => $inYck,
                    'icon' => 'el-icon-star-on',
                    'color' => '#E6A23C',
                    'unit' => ''
                ]
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取图表数据
     */
    private static function getCharts($userMode, $username, $college)
    {
        $charts = [];
        
        // 超级管理员 - 全局对比图表
        if ($userMode == 11) {
            $charts['mainChart'] = [
                'type' => 'bar',
                'title' => '各模块数据对比',
                'data' => [
                    ['大创项目', Dcproject::where('is_delete', 0)->count()],
                    ['竞赛项目', Jsproject::where('is_delete', 0)->count()],
                    ['英才库', Yckuser::where('is_delete', 0)->where('status', 2)->count()],
                    ['新闻总数', Newsdetail::where('is_delete', 0)->count()]
                ]
            ];
            
            // 用户组分布饼图
            $userGroups = Db::table('usermode')
                ->alias('um')
                ->field('um.group_name, COUNT(u.id) as count')
                ->join('user u', 'u.usermode = um.id', 'LEFT')
                ->where('u.status', 0)
                ->group('um.id')
                ->select();
                
            $pieData = [];
            foreach ($userGroups as $group) {
                $pieData[] = [
                    'name' => $group['group_name'],
                    'value' => $group['count']
                ];
            }
            
            $charts['pieChart'] = [
                'type' => 'pie',
                'title' => '用户组分布',
                'data' => $pieData
            ];
        }
        // 大创管理员
        elseif (in_array($userMode, [3, 4])) {
            // 图表部分
            if ($userMode == 3) {
                // 学院用户组分布
                $studentCount = \app\model\User::where('college', $college)->where('usermode', 1)->where('status', 0)->count();
                $teacherCount = \app\model\User::where('college', $college)->where('usermode', 2)->where('status', 0)->count();
                $adminCount = \app\model\User::where('college', $college)->where('usermode', 3)->where('status', 0)->count();
                $charts = [
                    'mainChart' => [
                        'type' => 'pie',
                        'title' => '本学院用户组分布',
                        'data' => [
                            ['name' => '学生', 'value' => $studentCount],
                            ['name' => '教师', 'value' => $teacherCount],
                            ['name' => '学院管理员', 'value' => $adminCount],
                        ]
                    ]
                ];
            } else {
                // 校级管理员保持原有项目分布图表
                $where = [];
                if ($userMode == 4) {
                    $where[] = ['m.rank', '=', 1];
                    // 校级管理员不加学院限制，统计全校
                }
                
                // 项目状态分布
                $statusData = Dcstatus::alias('s')
                    ->field('s.name, COUNT(DISTINCT p.uid) as count')
                    ->join('dcproject p', 'p.status = s.id', 'LEFT')
                    ->join('member m', 'm.uid = p.uid', 'LEFT')
                    ->join('user mu', 'mu.username = m.username', 'LEFT')
                    ->where('p.is_delete', 0)
                    ->where('m.is_delete', 0)
                    ->where($where)
                    ->group('s.id')
                    ->select();
                    
                $pieData = [];
                foreach ($statusData as $status) {
                    $pieData[] = [
                        'name' => $status['name'],
                        'value' => $status['count']
                    ];
                }
                
                $charts['mainChart'] = [
                    'type' => 'pie',
                    'title' => '项目状态分布',
                    'data' => $pieData
                ];
                
                // 学院分布
                $collegeData = Dcproject::alias('p')
                    ->field('d.name, COUNT(p.uid) as count')
                    ->join('member m', 'm.uid = p.uid', 'LEFT')
                    ->join('user mu', 'mu.username = m.username', 'LEFT')
                    ->join('department d', 'd.id = mu.college', 'LEFT')
                    ->where('p.is_delete', 0)
                    ->where('m.is_delete', 0)
                    ->where('m.rank', 1)
                    ->group('mu.college')
                    ->select();
                    
                $barData = [];
                foreach ($collegeData as $college) {
                    $barData[] = [$college['name'], $college['count']];
                }
                
                $charts['barChart'] = [
                    'type' => 'bar',
                    'title' => '学院项目分布',
                    'data' => $barData
                ];
            }
        }
        // 竞赛管理员
        elseif (in_array($userMode, [5, 6])) {
            // 图表部分
            if ($userMode == 5) {
                // 学院用户组分布
                $studentCount = \app\model\User::where('college', $college)->where('usermode', 1)->where('status', 0)->count();
                $teacherCount = \app\model\User::where('college', $college)->where('usermode', 2)->where('status', 0)->count();
                $adminCount = \app\model\User::where('college', $college)->where('usermode', 5)->where('status', 0)->count();
                $charts = [
                    'mainChart' => [
                        'type' => 'pie',
                        'title' => '本学院用户组分布',
                        'data' => [
                            ['name' => '学生', 'value' => $studentCount],
                            ['name' => '教师', 'value' => $teacherCount],
                            ['name' => '竞赛学院管理员', 'value' => $adminCount],
                        ]
                    ]
                ];
            } else {
                // 校级管理员保持原有项目分布图表
                $where = [];
                if ($userMode == 6) {
                    $where[] = ['m.rank', '=', 1];
                    $where[] = ['mu.college', '=', $college];
                }
                
                // 竞赛状态分布
                $statusData = Jsstatus::alias('s')
                    ->field('s.name, COUNT(p.uid) as count')
                    ->join('jsproject p', 'p.status = s.id', 'LEFT')
                    ->join('member m', 'm.uid = p.uid', 'LEFT')
                    ->join('user mu', 'mu.username = m.username', 'LEFT')
                    ->where('p.is_delete', 0)
                    ->where('m.is_delete', 0)
                    ->where($where)
                    ->group('s.id')
                    ->select();
                    
                $pieData = [];
                foreach ($statusData as $status) {
                    $pieData[] = [
                        'name' => $status['name'],
                        'value' => $status['count']
                    ];
                }
                
                $charts['mainChart'] = [
                    'type' => 'pie',
                    'title' => '竞赛状态分布',
                    'data' => $pieData
                ];
                
                // 学院竞赛参与度
                $collegeData = Jsproject::alias('p')
                    ->field('d.name, COUNT(p.uid) as count')
                    ->join('member m', 'm.uid = p.uid', 'LEFT')
                    ->join('user mu', 'mu.username = m.username', 'LEFT')
                    ->join('department d', 'd.id = mu.college', 'LEFT')
                    ->where('p.is_delete', 0)
                    ->where('m.is_delete', 0)
                    ->where('m.rank', 1)
                    ->group('mu.college')
                    ->select();
                    
                $barData = [];
                foreach ($collegeData as $college) {
                    $barData[] = [$college['name'], $college['count']];
                }
                
                $charts['barChart'] = [
                    'type' => 'bar',
                    'title' => '学院竞赛参与度',
                    'data' => $barData
                ];
            }
        }
        // 英才库管理员
        elseif ($userMode == 7) {
            // 英才类型分布
            $typeData = Yckuser::where('is_delete', 0)
                ->where('status', 2)
                ->field('type, COUNT(*) as count')
                ->group('type')
                ->select();
                
            $charts['mainChart'] = [
                'type' => 'pie',
                'title' => '英才类型分布',
                'data' => $typeData->map(function($item) {
                    return ['name' => $item['type'], 'value' => $item['count']];
                })->toArray()
            ];
            
            // 评分分布
            $scoreData = Yckuser::where('is_delete', 0)
                ->where('status', 2)
                ->field('ROUND(mark) as score, COUNT(*) as count')
                ->group('ROUND(mark)')
                ->order('score', 'asc')
                ->select();
                
            $charts['barChart'] = [
                'type' => 'bar',
                'title' => '评分分布',
                'data' => $scoreData->map(function($item) {
                    return [$item['score'] . '分', $item['count']];
                })->toArray()
            ];
        }
        // 新闻管理员
        elseif (in_array($userMode, [8, 9, 10, 12, 13, 14])) {
            $newsClassId = self::getUserNewsClassId($userMode);
            
            // 新闻板块分布
            $classData = Newsclass::alias('c')
                ->field('c.name, COUNT(n.id) as count')
                ->join('newsdetail n', 'n.class = c.id', 'LEFT')
                ->where('c.is_delete', 0)
                ->where('n.is_delete', 0)
                ->group('c.id')
                ->select();
                
            $charts['mainChart'] = [
                'type' => 'pie',
                'title' => '新闻板块分布',
                'data' => $classData->map(function($item) {
                    return ['name' => $item['name'], 'value' => $item['count']];
                })->toArray()
            ];
            
            // 月度发布趋势
            $trendData = Newsdetail::where('is_delete', 0)
                ->where('class', $newsClassId)
                ->field('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as count')
                ->group('month')
                ->order('month', 'desc')
                ->limit(6)
                ->select();
                
            $charts['lineChart'] = [
                'type' => 'line',
                'title' => '月度发布趋势',
                'data' => $trendData->map(function($item) {
                    return [$item['month'], $item['count']];
                })->reverse()->toArray()
            ];
        }
        // 学生
        if ($userMode == 1) {
            // 大创项目状态分布
            $dcStatusData = Dcproject::alias('p')
                ->field('s.name, COUNT(DISTINCT p.uid) as count')
                ->join('dcstatus s', 's.id = p.status', 'LEFT')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->group('s.id')
                ->select();
            $dcPieData = [];
            foreach ($dcStatusData as $status) {
                $dcPieData[] = [
                    'name' => $status['name'],
                    'value' => $status['count']
                ];
            }
            $charts['mainChart'] = [
                'type' => 'pie',
                'title' => '参与大创项目状态分布',
                'data' => $dcPieData
            ];
            // 竞赛项目状态分布
            $jsStatusData = Jsproject::alias('p')
                ->field('s.name, COUNT(DISTINCT p.uid) as count')
                ->join('jsstatus s', 's.id = p.status', 'LEFT')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->group('s.id')
                ->select();
            $jsPieData = [];
            foreach ($jsStatusData as $status) {
                $jsPieData[] = [
                    'name' => $status['name'],
                    'value' => $status['count']
                ];
            }
            $charts['barChart'] = [
                'type' => 'pie',
                'title' => '参与竞赛项目状态分布',
                'data' => $jsPieData
            ];
        }
        // 教师
        elseif ($userMode == 2) {
            // 大创项目状态分布
            $dcStatusData = Dcproject::alias('p')
                ->field('s.name, COUNT(DISTINCT p.uid) as count')
                ->join('dcstatus s', 's.id = p.status', 'LEFT')
                ->join('teacher t', 't.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('t.is_delete', 0)
                ->where('t.username', $username)
                ->group('s.id')
                ->select();
            $dcPieData = [];
            foreach ($dcStatusData as $status) {
                $dcPieData[] = [
                    'name' => $status['name'],
                    'value' => $status['count']
                ];
            }
            $charts['mainChart'] = [
                'type' => 'pie',
                'title' => '指导大创项目状态分布',
                'data' => $dcPieData
            ];
            // 竞赛项目状态分布
            $jsStatusData = Jsproject::alias('p')
                ->field('s.name, COUNT(DISTINCT p.uid) as count')
                ->join('jsstatus s', 's.id = p.status', 'LEFT')
                ->join('teacher t', 't.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('t.is_delete', 0)
                ->where('t.username', $username)
                ->group('s.id')
                ->select();
            $jsPieData = [];
            foreach ($jsStatusData as $status) {
                $jsPieData[] = [
                    'name' => $status['name'],
                    'value' => $status['count']
                ];
            }
            $charts['barChart'] = [
                'type' => 'pie',
                'title' => '指导竞赛项目状态分布',
                'data' => $jsPieData
            ];
        }
        
        return $charts;
    }
    
    /**
     * 获取最近活动
     */
    private static function getRecentActivities($userMode, $username)
    {
        $activities = [];
        
        // 根据用户组获取相关活动
        if (in_array($userMode, [3, 4])) {
            // 大创项目活动
            $activities = Dcproject::alias('p')
                ->field('p.name, p.created_at, "大创项目" as type')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->order('p.created_at', 'desc')
                ->limit(5)
                ->select();
        } elseif (in_array($userMode, [5, 6])) {
            // 竞赛项目活动
            $activities = Jsproject::alias('p')
                ->field('p.name, p.created_at, "竞赛项目" as type')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('m.username', $username)
                ->order('p.created_at', 'desc')
                ->limit(5)
                ->select();
        } elseif ($userMode == 7) {
            // 英才库活动
            $activities = Yckuser::where('is_delete', 0)
                ->field('name, created_at, type')
                ->order('created_at', 'desc')
                ->limit(5)
                ->select();
        } elseif (in_array($userMode, [8, 9, 10, 12, 13, 14])) {
            // 新闻活动
            $newsClassId = self::getUserNewsClassId($userMode);
            $activities = Newsdetail::where('is_delete', 0)
                ->where('class', $newsClassId)
                ->field('title as name, created_at, "新闻" as type')
                ->order('created_at', 'desc')
                ->limit(5)
                ->select();
        }
        
        return $activities;
    }
    
    /**
     * 获取待办事项
     */
    private static function getTodoList($userMode, $username, $college)
    {
        $todos = [];
        
        if (in_array($userMode, [3, 4])) {
            // 大创待审核项目
            $where = [];
            if ($userMode == 3) {
                $where[] = ['m.rank', '=', 1];
                $where[] = ['mu.college', '=', $college];
            }
            
            $pendingProjects = Dcproject::alias('p')
                ->field('p.name, p.created_at')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->join('user mu', 'mu.username = m.username', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('p.status', 1)
                ->where('m.is_delete', 0)
                ->where($where)
                ->order('p.created_at', 'desc')
                ->limit(3)
                ->select();
                
            foreach ($pendingProjects as $project) {
                $todos[] = [
                    'title' => '待审核项目：' . $project['name'],
                    'time' => $project['created_at'],
                    'type' => 'warning'
                ];
            }
        } elseif (in_array($userMode, [5, 6])) {
            // 竞赛待审核项目
            $where = [];
            if ($userMode == 5) {
                $where[] = ['m.rank', '=', 1];
                $where[] = ['mu.college', '=', $college];
            }
            
            $pendingProjects = Jsproject::alias('p')
                ->field('p.name, p.created_at')
                ->join('member m', 'm.uid = p.uid', 'LEFT')
                ->join('user mu', 'mu.username = m.username', 'LEFT')
                ->where('p.is_delete', 0)
                ->where('p.status', 1)
                ->where('m.is_delete', 0)
                ->where($where)
                ->order('p.created_at', 'desc')
                ->limit(3)
                ->select();
                
            foreach ($pendingProjects as $project) {
                $todos[] = [
                    'title' => '待审核竞赛：' . $project['name'],
                    'time' => $project['created_at'],
                    'type' => 'warning'
                ];
            }
        } elseif ($userMode == 7) {
            // 英才库待审核申请
            $pendingApplications = Yckuser::where('is_delete', 0)
                ->where('status', 1)
                ->field('name, created_at')
                ->order('created_at', 'desc')
                ->limit(3)
                ->select();
                
            foreach ($pendingApplications as $application) {
                $todos[] = [
                    'title' => '待审核申请：' . $application['name'],
                    'time' => $application['created_at'],
                    'type' => 'warning'
                ];
            }
        }
        
        return $todos;
    }
    
    /**
     * 获取用户管理的新闻板块ID
     */
    private static function getUserNewsClassId($userMode)
    {
        // 根据用户组ID获取对应的新闻板块
        // 用户组ID与新闻板块的对应关系需要根据实际情况调整
        $newsClassMap = [
            8 => 1,  // 测试板块1新闻管理员 -> 板块1
            9 => 2,  // 测试板块2新闻管理员 -> 板块2
            10 => 3, // 测试板块3新闻管理员 -> 板块3
            12 => 4, // 测试板块4新闻管理员 -> 板块4
            13 => 5, // 测试板块5新闻管理员 -> 板块5
            14 => 6, // 测试板块1新闻管理员 -> 板块6
        ];
        
        // 如果用户组在映射表中，返回对应的板块ID
        if (isset($newsClassMap[$userMode])) {
            return $newsClassMap[$userMode];
        }
        
        // 否则返回第一个新闻板块的ID
        return Newsclass::where('is_delete', 0)->value('id') ?: 1;
    }
} 