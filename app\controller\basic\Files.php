<?php

namespace app\controller\basic;

use app\BaseController;

class Files extends BaseController
{
    public function addfile($class = '', $type = '')
    {
        // 调试信息
        LogExecution('文件上传参数 - class: ' . $class . ', type: ' . $type);
        
        //就一个作用，上传文件到指定文件夹，然后返回路径
        $file = request()->file('file');


        if ($file){
            //判断文件类型是否合法
            // 获取文件的 MIME 类型
            $mimeType = $file->getMime();
            $fileName = $file->getOriginalName();
            if ($type=='pdf'){
                $check=['application/pdf'];
            }elseif ($type=='conclude'||$type=='newsfile'){
                $check=[
                    'application/pdf',//PDF
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  // DOCX
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',       // XLSX
                    'application/zip', 'application/x-zip-compressed', 'application/x-rar-compressed', // ZIP, RAR
                    'application/vnd.ms-powerpoint', 'application/msword', 'application/vnd.ms-excel'  // PPT, DOC, XLS (旧版)
                ];
            }elseif ($type=='certificate' || $type=='contact'||$type=='news'||$type=='carousel'){
                $check=[
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'image/bmp',
                    'image/webp'
                ];
            }
            // 检查 MIME 类型
            if (in_array($mimeType, $check)) {
                // 设置上传目录
                $uploadDir = public_path().'static/files/upload/' .$class.'/'.session('user.username'). '/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                // 生成文件名
                $fileName = $file->getOriginalName();
                // 移动文件到目标目录
                if ($file->isValid()) {
                    if (!$file->move($uploadDir, $fileName)){
                        $uploadResults = [
                            'status' => 'error',
                            'message' => $fileName.'上传失败'
                        ];
                        LogExecution($fileName.'上传失败');
                    }else{
                        $path=$class.'/'.session('user.username'). '/'.$fileName;
                        $uploadResults = [
                            'status' => 'success',
                            'message' => $path,
                            'name'=>$fileName
                        ];
                        //新闻和轮播单独回一个要的location格式
                        if ($class=='news' || $class=='carousel'){
                            $uploadResults = [
                                'status' => 'success',
                                'location' =>'static/files/upload/'. $path,
                            ];
                        }
                        LogExecution($fileName.'上传成功至'.$uploadDir);

                    }

                } else {
                    $uploadResults = [
                        'status' => 'error',
                        'message' => $fileName.'上传失败'
                    ];
                    LogExecution($fileName.'上传失败');
                }
            } else {
                $uploadResults = [
                    'status' => 'error',
                    'message' => $fileName.'上传类型不是'.$type
                ];
                LogExecution($fileName.'上传类型不是'.$type);
            }


        }else{
            $uploadResults = [
                'status' => 'error',
                'message' => '没有文件被上传'
            ];
            LogExecution('上传空文件');
        }
        // 返回上传结果
        return json($uploadResults);
    }
    
    /**
     * 轮播图片上传
     */
    public function uploadCarousel()
    {
        // 调试信息
        LogExecution('轮播图片上传');
        
        $file = request()->file('file');
        
        if ($file) {
            // 获取文件的 MIME 类型
            $mimeType = $file->getMime();
            $fileName = $file->getOriginalName();
            
            // 检查图片类型
            $check = [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/bmp',
                'image/webp'
            ];
            
            // 检查 MIME 类型
            if (in_array($mimeType, $check)) {
                // 设置上传目录
                $uploadDir = public_path() . 'static/files/upload/carousel/' . session('user.username') . '/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                // 生成文件名
                $fileName = $file->getOriginalName();
                
                // 移动文件到目标目录
                if ($file->isValid()) {
                    if (!$file->move($uploadDir, $fileName)) {
                        $uploadResults = [
                            'status' => 'error',
                            'message' => $fileName . '上传失败'
                        ];
                        LogExecution($fileName . '上传失败');
                    } else {
                        $path = 'carousel/' . session('user.username') . '/' . $fileName;
                        $uploadResults = [
                            'status' => 'success',
                            'location' => 'static/files/upload/' . $path,
                        ];
                        LogExecution($fileName . '上传成功至' . $uploadDir);
                    }
                } else {
                    $uploadResults = [
                        'status' => 'error',
                        'message' => $fileName . '上传失败'
                    ];
                    LogExecution($fileName . '上传失败');
                }
            } else {
                $uploadResults = [
                    'status' => 'error',
                    'message' => $fileName . '上传类型不是图片'
                ];
                LogExecution($fileName . '上传类型不是图片');
            }
        } else {
            $uploadResults = [
                'status' => 'error',
                'message' => '没有文件被上传'
            ];
            LogExecution('上传空文件');
        }
        
        // 返回上传结果
        return json($uploadResults);
    }
}