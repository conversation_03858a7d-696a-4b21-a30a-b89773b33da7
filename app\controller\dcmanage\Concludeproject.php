<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcachievement;
use app\model\Dcconclude;
use app\model\Dcexpenditure;
use app\model\Dcinterm;
use app\model\Dcintermachievement;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\File;
use app\model\Member;
use think\facade\Db;

class Concludeproject extends BaseController
{
    /**
     * 结题报告页面
     */
    public function index($uid){
        if (!$uid){
            LogExecution('结题填报失败，uid不存在，请重新登录');
            return ['status'=>'error','message'=>'结题填报失败，请重新登录'];
        }
        
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('结题填报失败，项目不存在');
            return ['status'=>'error','message'=>'结题填报失败，项目不存在'];
        }

        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }

        // 检查项目状态是否允许提交结题报告
        if (!$this->canSubmitConcludeReport($project['status'])) {
            LogExecution(session('user.name').'当前不是可填报状态');
            return json(['status'=>'error','message' => '当前不是可填报状态']);
        }

        if ($project['status'] == 8){
            // 第一次填报
            return view('dcmanage/concludeproject');
        } else {
            // 被驳回需要显示之前信息
            $conclude = Dcconclude::where('is_delete',0)->where('uid',$uid)->find();
            $expenses = Dcexpenditure::where('is_delete',0)->where('uid',$uid)->where('type',2)->field('amount,detail')->select();
            $achievements = Dcachievement::where('is_delete',0)->where('uid',$uid)->select();
            
            $typeachievement = [];
            // 初始化
            $extras = [
                'lunwen' => [],
                'zuopin' => [],
                'zhuanli' => [],
                'diaochabaogao' => [],
                'shangyejihuashu' => [],
                'gongshangzhuce' => [],
                'zhuzuo' => [],
                'gzh' => [],
                'wz' => [],
                'rj' => [],
                'xcx' => [],
                'app' => [],
                'yyh' => [],
                'wk' => [],
                'sp' => [],
                'hb' => [],
                'other' => [],
            ];

            foreach ($achievements as $achievement){
                $type = $this->getAchievementTypeName($achievement['type']);
                array_push($typeachievement, $type);

                $extras[$achievement['type']][] = [
                    'title' => $achievement['title'],
                    'time' => $achievement['time'],
                    'remark' => $achievement['remark'],
                    'main' => $achievement['main'],
                    'num' => $achievement['num'],
                    'author' => $achievement['author'],
                ];
            }
            
            // 组装数据
            $data = [
                'results' => $conclude ? $conclude['results'] : '',
                'conclusion' => $conclude ? $conclude['conclusion'] : '',
                'problem' => $conclude ? $conclude['problem'] : '',
                'excellent_project' => $conclude ? $conclude['excellent_project'] : '',
                'excellent_lunwen' => $conclude ? $conclude['excellent_lunwen'] : '',
                'fileurls' => [],
                'expenses' => $expenses ?: [],
                'achievement' => $typeachievement,
                'extras' => $extras
            ];

            return view('dcmanage/concludeproject',['budget'=>$project['budget'],'data'=>$data]);
        }
    }

    /**
     * 提交结题报告
     */
    public function add_conclude($uid){
        $old_member = Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
        if (!$old_member || $old_member['username'] != session('user.username')){
            LogExecution(session('user.name').'你不是项目的负责人');
            return json(['status'=>'error','message' => '你不是项目的负责人']);
        }
        
        $data = input('post.data');
        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution('结题报告提交失败，项目不存在');
            return json(['status'=>'error','message' => '结题报告提交失败，项目不存在']);
        }

        // 检查项目状态是否允许提交结题报告
        if (!$this->canSubmitConcludeReport($project['status'])) {
            LogExecution(session('user.name').'当前不是可填报状态');
            return json(['status'=>'error','message' => '当前不是可填报状态']);
        }

        $new_status = $this->calculateConcludeStatus($project['status']);

        // 整理数据格式
        $conclude = [
            'uid' => $uid,
            'results' => $data['results'],
            'conclusion' => $data['conclusion'],
            'problem' => $data['problem'],
            'excellent_project' => $data['excellent_project'],
            'excellent_lunwen' => $data['excellent_lunwen']
        ];
        
        $expenditure = [];
        foreach ($data['expenses'] as $expenses) {
            $expenditure[] = [
                'uid' => $uid,
                'type' => 2,
                'detail' => $expenses['detail'],
                'amount' => $expenses['amount']
            ];
        }
        
        $files = [];
        foreach ($data['fileurls'] as $file) {
            $files[] = [
                'uid' => $uid,
                'path' => $file['url'],
                'name' => $file['name'],
                'type' => 3
            ];
        }
        
        $achievements = [];
        foreach ($data['achievement'] as $achievement){
            $type = $this->getAchievementTypeCode($achievement);
            if ($type) {
                $time = $data['extras'][$type]['time'] ?? null;
                if (empty($time)) $time = null;
                $achievements[] = [
                    'uid' => $uid,
                    'type' => $type,
                    'title' => $data['extras'][$type]['title'] ?? '',
                    'time' => $time,
                    'remark' => $data['extras'][$type]['remark'] ?? '',
                    'main' => $data['extras'][$type]['main'] ?? '',
                    'num' => $data['extras'][$type]['num'] ?? '',
                    'author' => $data['extras'][$type]['author'] ?? '',
                ];
            }
        }

        if ($project['status'] == 8) {
            // 新填报
            return $this->submitNewConcludeReport($conclude, $achievements, $expenditure, $files, $uid, $new_status);
        } else {
            // 修改填报
            return $this->updateConcludeReport($conclude, $achievements, $expenditure, $files, $uid, $new_status);
        }
    }

    /**
     * 检查是否可以提交结题报告
     */
    private function canSubmitConcludeReport($status) {
        // 状态8：待提交结题报告
        // 状态9：等待指导教师结题审核
        // 状态10：等待学院结题审核
        // 状态11：等待学校结题审核
        // 状态18：指导教师结题审核驳回
        // 状态19：学院结题审核驳回
        // 状态20：学校结题审核驳回
        // 状态22：待指导教师延期结题审核
        // 状态29：教师延期审核驳回
        // 状态30：学院延期审核驳回
        // 状态31：学校延期审核驳回
        // 状态32：延期审核通过，可提交结题报告
        $allowedStatus = [8, 9, 10, 11, 18, 19, 20, 22, 29, 30, 31, 32];
        return in_array($status, $allowedStatus);
    }

    /**
     * 计算结题报告提交后的新状态
     */
    private function calculateConcludeStatus($currentStatus) {
        $statusMap = [
            8 => 9,   // 新提交 -> 等待教师审核
            9 => 9,   // 重新提交 -> 等待教师审核
            18 => 9,  // 教师驳回后重新提交 -> 等待教师审核
            19 => 10, // 学院驳回后重新提交 -> 等待学院审核
            20 => 11, // 学校驳回后重新提交 -> 等待学校审核
            29 => 9,  // 教师延期驳回后提交结题报告 -> 等待教师审核
            30 => 10, // 学院延期驳回后提交结题报告 -> 等待学院审核
            31 => 11, // 学校延期驳回后提交结题报告 -> 等待学校审核
            32 => 9,  // 延期审核通过后提交结题报告 -> 等待教师审核
        ];
        return $statusMap[$currentStatus] ?? $currentStatus;
    }

    /**
     * 获取成果类型名称
     */
    private function getAchievementTypeName($type) {
        $typeMap = [
            'lunwen' => '论文',
            'zhuanli' => '专利',
            'diaochabaogao' => '调查报告',
            'shangyejihuashu' => '商业计划书',
            'zhuzuo' => '著作',
            'gongshangzhuce' => '工商注册',
            'gzh' => '公众号',
            'wz' => '网站',
            'rj' => '软件',
            'xcx' => '小程序',
            'app' => 'APP',
            'yyh' => '运营号',
            'wk' => '微课',
            'sp' => '视频',
            'hb' => '绘本（图册）',
            'other' => '其他',
        ];
        return $typeMap[$type] ?? $type;
    }

    /**
     * 获取成果类型代码
     */
    private function getAchievementTypeCode($typeName) {
        $codeMap = [
            '论文' => 'lunwen',
            '专利' => 'zhuanli',
            '调查报告' => 'diaochabaogao',
            '商业计划书' => 'shangyejihuashu',
            '著作' => 'zhuzuo',
            '工商注册' => 'gongshangzhuce',
            '公众号' => 'gzh',
            '网站' => 'wz',
            '软件' => 'rj',
            '小程序' => 'xcx',
            'APP' => 'app',
            '运营号' => 'yyh',
            '微课' => 'wk',
            '视频' => 'sp',
            '绘本（图册）' => 'hb',
            '其他' => 'other',
        ];
        return $codeMap[$typeName] ?? null;
    }

    /**
     * 提交新的结题报告
     */
    private function submitNewConcludeReport($conclude, $achievements, $expenditure, $files, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '结题申请',
            'remark' => '学生提交结题报告'
        ];
        
        try {
            Db::transaction(function () use ($conclude, $achievements, $expenditure, $files, $uid, $new_status, $progress) {
                // 插入结题报告
                $concludeResult = Dcconclude::insert($conclude);
                // 插入成果
                $achievementResult = Dcachievement::insertAll($achievements);
                // 插入支出
                $expenditureResult = Dcexpenditure::insertAll($expenditure);
                // 插入文件
                $fileResult = File::insertAll($files);
                // 更新项目状态
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                // 插入进度
                $progressResult = Dcprogress::insert($progress);
                // 只要无异常即可视为成功，不再要求projectResult为真
                if (!$concludeResult || !$achievementResult || !$expenditureResult || !$fileResult || !$progressResult) {
                    throw new \Exception('结题报告提交失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'结题报告提交成功');
            return ['status' => 'success', 'message' => '结题报告提交成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '结题报告提交失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '结题报告提交失败，数据表插入异常'];
        }
    }

    /**
     * 更新结题报告
     */
    private function updateConcludeReport($conclude, $achievements, $expenditure, $files, $uid, $new_status) {
        $progress = [
            'uid' => $uid,
            'action' => '修改结题申请',
            'remark' => '学生修改结题报告'
        ];
        
        try {
            Db::transaction(function () use ($conclude, $achievements, $expenditure, $files, $uid, $new_status, $progress) {
                // 软删除原有数据
                Dcconclude::where('is_delete',0)->where('uid',$uid)->update(['is_delete'=>1]);
                Dcachievement::where('is_delete',0)->where('uid',$uid)->update(['is_delete'=>1]);
                Dcexpenditure::where('is_delete',0)->where('uid',$uid)->where('type',2)->update(['is_delete'=>1]);
                File::where('is_delete',0)->where('uid',$uid)->where('type',3)->update(['is_delete'=>1]);
                
                // 插入新数据
                $concludeResult = Dcconclude::insert($conclude);
                $achievementResult = Dcachievement::insertAll($achievements);
                $expenditureResult = Dcexpenditure::insertAll($expenditure);
                $fileResult = File::insertAll($files);
                $progressResult = Dcprogress::insert($progress);
                $projectResult = Dcproject::where('is_delete', 0)->where('uid', $uid)->update(['status' => $new_status]);
                // 只要无异常即可视为成功，不再要求projectResult为真
                if (!$concludeResult || !$achievementResult || !$expenditureResult || !$fileResult || !$progressResult) {
                    throw new \Exception('结题报告修改失败，已回滚事务');
                }
            });

            LogExecution(session('user.name').'结题报告修改成功');
            return ['status' => 'success', 'message' => '结题报告修改成功'];

        } catch (\Exception $e) {
            LogExecution(session('user.name') . '结题报告修改失败：' . $e->getMessage());
            return ['status' => 'error', 'message' => '结题报告修改失败，数据表插入异常'];
        }
    }
}