<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dccheck;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Member;
use app\model\Teacher;
use app\model\Dcmidchange;
use think\facade\Db;

class Check extends BaseController
{
    /**
     * 项目审核主方法
     */
    public function checkProject($uid){
        if (!$uid){
            LogExecution('审核失败，项目ID不存在');
            return ['status'=>'error','message'=>'审核失败，项目ID不存在'];
        }

        // 检查用户是否有权限审核该项目
        if (!$this->checkPermission('dc_check_teacher') && 
            !$this->checkPermission('dc_check_college') && 
            !$this->checkPermission('dc_check_school')) {
            LogExecution('用户尝试审核无权限的项目：' . $uid);
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        // 检查用户是否可以审核该项目
        if (!$this->canCheckProject($uid, 'dc')) {
            LogExecution('用户尝试审核无权限的项目：' . $uid);
            return json(['status' => 'error', 'message' => '您没有权限审核该项目']);
        }

        $check = input('post.check.check');
        $remark = input('post.check.remark');
        
            if (!$remark){
                LogExecution($uid.'审核失败，请输入审核意见');
                return ['status'=>'error','message'=>'审核失败，请输入审核意见'];
            }

        $project = Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project) {
            LogExecution($uid.'审核失败，项目不存在');
            return ['status'=>'error','message'=>'审核失败，项目不存在'];
                    }

        // 获取审核结果
        $result = $this->processCheck($uid, $project, $check, $remark);
        
        if ($result['status'] === 'error') {
            return $result;
                    }

            try {
                // 开启事务
            Db::transaction(function () use ($uid, $project, $result) {
                    // 更新项目状态
                    $status = Dcproject::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                    ->update(['status' => $result['new_status']]);

                    // 插入检查记录
                $checksResult = Dccheck::insert($result['checks']);

                    // 插入进度记录
                $progressResult = Dcprogress::insert($result['progress']);

                // 如果是学校审核通过中期变更，需要更新项目数据
                if ($result['new_status'] == 4) {
                    $this->updateProjectDataAfterMidchange($project['uid']);
                }

                    // 检查所有操作是否成功
                    if (!$status || !$checksResult || !$progressResult) {
                        throw new \Exception('数据库操作失败，已回滚事务');
                    }
                });

                LogExecution($uid.'审核成功');
                return ['status' => 'success', 'message' => '审核成功'];

            } catch (\Exception $e) {
            LogExecution($uid.'审核失败：' . $e->getMessage());
                return ['status' => 'error', 'message' => '审核失败，数据异常'];
            }
    }

    /**
     * 处理审核逻辑
     */
    private function processCheck($uid, $project, $check, $remark) {
        $userMode = session('user.usermode');
        $currentStatus = $project['status'];
        
        // 验证当前状态是否可以被审核
        if (!$this->canCheckCurrentStatus($currentStatus, $userMode)) {
            return ['status' => 'error', 'message' => '当前状态不可审核'];
        }

        // 验证审核权限
        $permissionResult = $this->validateCheckPermission($uid, $userMode);
        if ($permissionResult['status'] === 'error') {
            return $permissionResult;
        }

        // 修正check判断，1为驳回，0为通过
        $isReject = ($check === '1' || $check === 1);
        $checks = [
            'uid' => $project['uid'],
            'type' => $this->getCheckType($userMode),
            'check' => $isReject ? 1 : 0,
            'status' => $this->getCheckStatus($currentStatus),
            'remark' => $remark,
        ];

        $progress = [
            'uid' => $project['uid'],
            'action' => $this->getActionName($userMode, $currentStatus),
            'remark' => $this->getActionRemark($userMode, $currentStatus, $isReject),
        ];

        // 计算新状态
        $newStatus = $this->calculateNewStatus($currentStatus, $userMode, $isReject);
        
        if ($newStatus == $currentStatus) {
            return ['status' => 'error', 'message' => '状态计算错误'];
        }

        // 如果是中期变更被驳回，恢复原成员和教师
        if ($isReject && in_array($currentStatus, [33,34,35]) && in_array($newStatus, [36,37,38])) {
            $this->restoreProjectDataBeforeMidchange($uid);
        }

        return [
            'status' => 'success',
            'checks' => $checks,
            'progress' => $progress,
            'new_status' => $newStatus
        ];
    }

    /**
     * 验证当前状态是否可以被审核
     */
    private function canCheckCurrentStatus($status, $userMode) {
        $checkableStatus = [
            2 => [1, 5, 9, 22, 33], // 教师可审核的状态（立项、中期、结题、延期、中期变更）
            3 => [2, 6, 10, 23, 34], // 学院可审核的状态（立项、中期、结题、延期、中期变更）
            4 => [3, 7, 11, 24, 35], // 学校可审核的状态（立项、中期、结题、延期、中期变更）
        ];

        return isset($checkableStatus[$userMode]) && in_array($status, $checkableStatus[$userMode]);
    }

    /**
     * 获取审核类型
     */
    private function getCheckType($userMode) {
        $typeMap = [
            2 => 1, // 教师
            3 => 2, // 学院
            4 => 3, // 学校
        ];
        return $typeMap[$userMode] ?? 0;
    }

    /**
     * 获取审核状态
     */
    private function getCheckStatus($currentStatus) {
        $statusMap = [
            1 => 1, // 立项审核
            2 => 1, // 立项审核
            3 => 1, // 立项审核
            5 => 2, // 中期审核
            6 => 2, // 中期审核
            7 => 2, // 中期审核
            9 => 3, // 结题审核
            10 => 3, // 结题审核
            11 => 3, // 结题审核
            22 => 4, // 延期审核
            23 => 4, // 延期审核
            24 => 4, // 延期审核
            33 => 5, // 中期变更审核
            34 => 5, // 中期变更审核
            35 => 5, // 中期变更审核
        ];
        return $statusMap[$currentStatus] ?? 0;
    }

    /**
     * 获取操作名称
     */
    private function getActionName($userMode, $currentStatus) {
        $userNames = [
            2 => '指导教师',
            3 => '学院',
            4 => '学校',
        ];
        
        $actionTypes = [
            1 => '立项审核',
            2 => '中期审核', 
            3 => '结题审核',
            4 => '延期审核',
            5 => '中期变更审核',
        ];

        $status = $this->getCheckStatus($currentStatus);
        return $userNames[$userMode] . $actionTypes[$status];
    }

    /**
     * 获取操作备注
     */
    private function getActionRemark($userMode, $currentStatus, $check) {
        $userNames = [
            2 => '指导教师',
            3 => '学院',
            4 => '学校',
        ];
        
        $actionTypes = [
            1 => '立项审核',
            2 => '中期审核',
            3 => '结题审核',
            4 => '延期审核',
            5 => '中期变更审核',
        ];

        $status = $this->getCheckStatus($currentStatus);
        $result = $check ? '驳回' : '通过';
        
        return $userNames[$userMode] . $actionTypes[$status] . $result;
    }

    /**
     * 计算新状态
     */
    private function calculateNewStatus($currentStatus, $userMode, $check) {
        if ($check) {
            // 驳回逻辑
            return $this->getRejectStatus($currentStatus, $userMode);
        } else {
            // 通过逻辑
            return $this->getApproveStatus($currentStatus, $userMode);
        }
    }

    /**
     * 获取通过后的新状态
     */
    private function getApproveStatus($currentStatus, $userMode) {
        $approveMap = [
            // 教师审核通过
            2 => [
                1 => 2,  // 立项审核通过 -> 等待学院审核
                5 => 6,  // 中期审核通过 -> 等待学院审核
                9 => 10, // 结题审核通过 -> 等待学院审核
                22 => 23, // 延期审核通过 -> 等待学院审核
                33 => 34, // 中期变更审核通过 -> 等待学院审核
            ],
            // 学院审核通过
            3 => [
                2 => 3,  // 立项审核通过 -> 等待学校审核
                6 => 7,  // 中期审核通过 -> 等待学校审核
                10 => 11, // 结题审核通过 -> 等待学校审核
                23 => 24, // 延期审核通过 -> 等待学校审核
                34 => 35, // 中期变更审核通过 -> 等待学校审核
            ],
            // 学校审核通过
            4 => [
                3 => 4,  // 立项审核通过 -> 待提交中期报告
                7 => 8,  // 中期审核通过 -> 待提交结题报告
                11 => 21, // 结题审核通过 -> 项目结题
                24 => 32, // 延期审核通过 -> 延期结题可以审核了
                35 => 4, // 中期变更审核通过 -> 数据已更新，可提交中期报告
            ],
        ];

        return $approveMap[$userMode][$currentStatus] ?? $currentStatus;
    }

    /**
     * 获取驳回后的新状态
     */
    private function getRejectStatus($currentStatus, $userMode) {
        $rejectMap = [
            // 教师审核驳回
            2 => [
                1 => 12, // 立项审核驳回
                5 => 15, // 中期审核驳回
                9 => 18, // 结题审核驳回
                22 => 29, // 延期审核驳回
                33 => 36, // 中期变更审核驳回
            ],
            // 学院审核驳回
            3 => [
                2 => 13, // 立项审核驳回
                6 => 16, // 中期审核驳回
                10 => 19, // 结题审核驳回
                23 => 30, // 延期审核驳回
                34 => 37, // 中期变更审核驳回
            ],
            // 学校审核驳回
            4 => [
                3 => 14, // 立项审核驳回
                7 => 17, // 中期审核驳回
                11 => 20, // 结题审核驳回
                24 => 31, // 延期审核驳回
                35 => 38, // 中期变更审核驳回
            ],
        ];

        return $rejectMap[$userMode][$currentStatus] ?? $currentStatus;
    }

    /**
     * 验证审核权限
     */
    private function validateCheckPermission($uid, $userMode) {
        // 教师审核时，若有中期变更，必须是midchange表new_teachers中rank=1的新教师
        if ($userMode == 2) {
            $midchange = Dcmidchange::where('uid', $uid)->find();
            if ($midchange && $midchange['new_teachers']) {
                $newTeachers = json_decode($midchange['new_teachers'], true);
                $firstTeacher = $newTeachers[0]['value'] ?? null;
                if ($firstTeacher && $firstTeacher != session('user.username')) {
                    return ['status'=>'error','message'=>'您不是该项目中期变更指定的新第一指导教师，无权审核'];
                }
            } else {
                // 没有中期变更，按现有教师校验
                $teacher = Teacher::where('is_delete',0)
                    ->where('uid',$uid)
                    ->where('rank',1)
                    ->where('type',0)
                    ->order('id','desc')
                    ->find();
                if (!$teacher || $teacher['username'] != session('user.username')) {
                    return ['status'=>'error','message'=>'您不是该项目的最新第一指导教师，无权审核'];
                }
            }
        }
        // 学院、学校审核权限保持不变
        return ['status'=>'success'];
    }

    /**
     * 中期变更审核通过后更新项目数据
     */
    public static function updateProjectDataAfterMidchange($uid) {
        // 获取中期变更申请数据
        $midchange = Dcmidchange::where('uid', $uid)->find();
        if (!$midchange) {
            LogExecution($uid.'中期变更数据不存在，无法更新项目数据');
            return;
        }

        $newMembers = json_decode($midchange['new_members'], true) ?: [];
        $newTeachers = json_decode($midchange['new_teachers'], true) ?: [];

        try {
            Db::transaction(function () use ($uid, $newMembers, $newTeachers) {
                // 软删除原有成员和教师
                Member::where('is_delete', 0)->where('uid', $uid)->update(['is_delete' => 1]);
                Teacher::where('is_delete', 0)->where('uid', $uid)->update(['is_delete' => 1]);

                // 先查原负责人（即使被软删除）
                $leader = Member::withTrashed()->where('uid', $uid)->where('rank', 1)->where('dc', 1)->order('id', 'desc')->find();
                if (!$leader) {
                    $leaderUsername = session('user.username');
                } else {
                    $leaderUsername = $leader['username'];
                }
                $memberData = [[
                    'uid' => $uid,
                    'username' => $leaderUsername,
                    'rank' => 1,
                    'dc' => 1,
                    'type' => 0
                ]];
                foreach ($newMembers as $index => $member) {
                    $memberData[] = [
                        'uid' => $uid,
                        'username' => $member['value'],
                        'rank' => $index + 2, // 从2开始
                        'dc' => 1,
                        'type' => 0
                    ];
                }
                if (!empty($memberData)) {
                    Member::insertAll($memberData);
                }

                // 插入新教师
                $teacherData = [];
                foreach ($newTeachers as $index => $teacher) {
                    $teacherData[] = [
                        'uid' => $uid,
                        'username' => $teacher['value'],
                        'rank' => $index + 1,
                        'type' => 0
                    ];
                }
                if (!empty($teacherData)) {
                    Teacher::insertAll($teacherData);
                }

                // 记录变更日志
                $progress = [
                    'uid' => $uid,
                    'action' => '中期变更数据更新',
                    'remark' => '学生提交中期变更，项目成员和指导教师已更新'
                ];
                Dcprogress::insert($progress);
            });

            LogExecution($uid.'中期变更数据更新成功');
        } catch (\Exception $e) {
            LogExecution($uid.'中期变更数据更新失败：' . $e->getMessage());
            throw $e;
        }
    }

    // 在Check.php中添加成员和教师快照恢复逻辑
    // 1. 学生提交中期变更时立即调用updateProjectDataAfterMidchange
    // 2. 审核被驳回时恢复成员和教师快照

    // 在审核驳回时恢复成员和教师
    private function restoreProjectDataBeforeMidchange($uid) {
        $midchange = Dcmidchange::where('uid', $uid)->find();
        if (!$midchange || !$midchange['old_members'] || !$midchange['old_teachers']) {
            LogExecution($uid.'中期变更恢复失败，缺少快照数据');
            return;
        }
        $oldMembers = json_decode($midchange['old_members'], true) ?: [];
        $oldTeachers = json_decode($midchange['old_teachers'], true) ?: [];
        try {
            Db::transaction(function () use ($uid, $oldMembers, $oldTeachers) {
                // 软删除现有成员和教师
                Member::where('is_delete', 0)->where('uid', $uid)->update(['is_delete' => 1]);
                Teacher::where('is_delete', 0)->where('uid', $uid)->update(['is_delete' => 1]);
                // 恢复原成员，补全字段
                $memberData = [];
                $rank = 1;
                foreach ($oldMembers as $member) {
                    $username = $member['username'] ?? ($member['value'] ?? '');
                    if (!$username) continue;
                    // 查user表补全name
                    $name = '';
                    $user = \app\model\User::where('username', $username)->where('status', 0)->find();
                    if ($user) {
                        $name = $user['name'];
                    } else {
                        $name = $username;
                    }
                    $memberData[] = [
                        'uid' => $uid,
                        'username' => $username,
                        'rank' => $rank,
                        'type' => 0,
                        'dc' => 1,
                        'name' => $name,
                        'is_delete' => 0
                    ];
                    $rank++;
                }
                if (!empty($memberData)) {
                    Member::insertAll($memberData);
                }
                // 恢复原教师，补全字段
                $teacherData = [];
                $rank = 1;
                foreach ($oldTeachers as $teacher) {
                    $username = $teacher['username'] ?? ($teacher['value'] ?? '');
                    if (!$username) continue;
                    // 查user表补全name
                    $name = '';
                    $user = \app\model\User::where('username', $username)->where('status', 0)->find();
                    if ($user) {
                        $name = $user['name'];
                    } else {
                        $name = $username;
                    }
                    $teacherData[] = [
                        'uid' => $uid,
                        'username' => $username,
                        'rank' => $rank,
                        'type' => 0,
                        'dc' => 1,
                        'name' => $name,
                        'is_delete' => 0
                    ];
                    $rank++;
                }
                if (!empty($teacherData)) {
                    Teacher::insertAll($teacherData);
                }
                // 记录恢复日志
                $progress = [
                    'uid' => $uid,
                    'action' => '中期变更驳回恢复',
                    'remark' => '中期变更被驳回，恢复原项目成员和指导教师'
                ];
                Dcprogress::insert($progress);
            });
            LogExecution($uid.'中期变更驳回已恢复原成员和教师');
        } catch (\Exception $e) {
            LogExecution($uid.'中期变更驳回恢复失败：' . $e->getMessage());
            throw $e;
        }
    }

    // 检查当前用户是否有权限审核该项目
    private function canCheckProject($uid, $type = 'dc') {
        $usermode = session('user.usermode');
        $username = session('user.username');
        $college = session('user.college');

        if ($usermode == 2) { // 教师
            // 必须是该项目的第一指导教师
            $teacher = Teacher::where('is_delete', 0)
                ->where('uid', $uid)
                ->where('username', $username)
                ->where('rank', 1)
                ->find();
            return !!$teacher;
        } elseif ($usermode == 3) { // 学院
            // 必须是本学院的项目
            $member = Member::alias('m')
                ->join('user u', 'm.username = u.username', 'LEFT')
                ->where('m.is_delete', 0)
                ->where('u.status', 0)
                ->where('m.rank', 1)
                ->where('m.uid', $uid)
                ->where('u.college', $college)
                ->find();
            return !!$member;
        } elseif ($usermode == 4) { // 学校
            // 校级管理员可以审核所有项目
            return true;
        } elseif ($usermode == 11) { // 超级管理员
            return true;
        }
        return false;
    }
}