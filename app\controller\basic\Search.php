<?php

namespace app\controller\basic;

use app\BaseController;
use app\model\Dcproject;
use app\model\Jsproject;
use app\model\Newsdetail;
use app\model\User;
use app\model\Yckcurriculum;
use app\model\Yckdomain;
use app\model\Yckproject;
use app\model\Ycktag;
use app\model\Yckuser;

class Search extends BaseController
{

    public function news($id){
        $detail=Newsdetail::where('is_delete',0)->where('id',$id)->find();
        if ($detail){
            return ['status'=>'success','message'=>$detail];

        }else{
            return ['status'=>'error','message'=>'新闻不存在'];

        }
    }

    public function index(){
    //搜索官网首页信息
//        $news=Newsdetail::where('is_delete', 0)
//            ->field('id,title,updated_at,class')
//            ->select();

        // 从数据库中查询所有需要的字段，并按 class 和 updated_at 排序
        $newsItems = Newsdetail::
        alias('d')
            ->join('newsclass c','c.id=d.class','LEFT')

            ->where('d.is_delete', 0)
            ->field('d.id,d.title,d.updated_at as date,d.class,c.name as c_name')
            ->order('c.rank', 'asc')
            ->order('d.class', 'asc')
            ->order('d.updated_at', 'desc')
            ->select();
// 将结果转换为数组，以便后续处理
        $newsArray = $newsItems->toArray();
// 初始化一个空数组来存储结果
        $result = [];
// 对结果按 class 进行分组，并限制每个 class 的记录数
        $groupedNews = [];
        foreach ($newsArray as $news) {
            if (!isset($groupedNews[$news['class']])) {
                $groupedNews[$news['class']] = [];
            }
            $groupedNews[$news['class']][] = $news;

            // 如果当前 class 的记录数超过 3，则截断数组
            if (count($groupedNews[$news['class']]) > 5) {
                $groupedNews[$news['class']] = array_slice($groupedNews[$news['class']], 0, 5);
            }
        }

// 将分组后的数据转换为所需的格式
        foreach ($groupedNews as $class => $newsList) {
//            return json($newsList[0]['c_name']);

            $resultItem = [
                'name'=>$newsList[0]['c_name'],
                'class'=>$newsList[0]['class'],
                'news' => $newsList,
                ];
            // 如果需要添加额外的字段（如 link），可以在这里添加
            // 例如：if ($class == 2) { $resultItem['link'] = '#'; }
            $result[] = $resultItem;
        }
        $data=[
            'news'=>$result,
        ];
//        return json($result);
        return ['status'=>'success','message'=>$data];

    }
    public function user($type){
        $query=input('post.query');
        $where=[];
        $where[]=['name|username','like','%'.$query.'%'];
        if ($type!='blacklist'){
            $where[]=['usermode','=',$type];
        }
        $user = User::
            where('status',0)
            ->where($where)
            ->where('username','!=',session('user.username'))
            ->field('username as value,name as label')
            ->select();
        if ($user){
            return ['status'=>'success','message'=>$user];
        }else{
            return ['status'=>'error','message'=>'数据异常'];
        }
    }
    public function dc($username=''){
        if (!$username){
            $username=session('user.username');
        }
        $user=User::where('status',0)->where('username',$username)->find();
        $where=[];
//        $field='p.uid,p.name,l.name as l_level,p.time';
        $field='p.uid, p.name';

        if ($user['usermode']==1){
            $where[]=['m.username','=',$username];
//            $field=$field.',m.rank';
        }elseif ($user['usermode']==2){
            $where[]=['t.username','=',$username];
//            $field=$field.',t.rank';
        }
        $data = Dcproject::alias('p')
            ->field($field)
//            ->field('
//        p.uid,p.proid,p.name,
//        l.name as l_level,s.name as s_status,
//        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
//        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
//        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
//        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames
//    ')
            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
//            ->join('user mu', 'mu.username = m.username', 'LEFT')
//            ->join('user tu', 'tu.username = t.username', 'LEFT')
            ->join('dclevel l','l.id=p.level','LEFT')
//            ->join('dcstatus s','s.id=p.status','LEFT')
            ->where('p.is_delete', 0)
            ->where('m.is_delete', 0)
//            ->where('mu.status', 0)
//            ->where('tu.status', 0)
            ->where($where)
            //必须结项
            ->where('p.status',21)
            ->distinct(true)
//            ->group('p.uid')
            ->select();
//        return json($data);

        if ($data){
            return ['status'=>'success','message'=>$data];
        }else{
            return ['status'=>'error','message'=>'数据异常'];
        }
    }
    public function js($username=''){
        if (!$username){
            $username=session('user.username');
        }
        $user=User::where('status',0)->where('username',$username)->find();
        $where=[];
//        $field='p.uid as key,p.name,l.name as l_level,CONCAT(a.level, a.type) AS award,p.created_at';
        $field='p.uid, p.name';
        if ($user['usermode']==1){
            $where[]=['m.username','=',$username];
//            $field=$field.',m.rank';
        }elseif ($user['usermode']==2){
            $where[]=['t.username','=',$username];
//            $field=$field.',t.rank';
        }
        $data = Jsproject::alias('p')
//            ->field('
//        p.uid,p.name,p.time,p.class,
//        c.name as c_name,
//        s.name as s_status,
//        CONCAT(a.level, a.type) AS award,
//        GROUP_CONCAT(DISTINCT mu.name ORDER BY m.rank ASC) as m_names,
//        GROUP_CONCAT(DISTINCT tu.name ORDER BY t.rank ASC) as t_names,
//        GROUP_CONCAT(DISTINCT mu.username ORDER BY m.rank ASC) as m_usernames,
//        GROUP_CONCAT(DISTINCT tu.username ORDER BY t.rank ASC) as t_usernames
//    ')
                ->field($field)
            ->join('jscompetition c', 'c.cuid = p.cuid', 'LEFT')
            ->join('jsaward a', 'a.id = p.award', 'LEFT')

            ->join('member m', 'm.uid = p.uid', 'LEFT')
            ->join('teacher t', 't.uid = p.uid', 'LEFT')
//            ->join('user mu', 'mu.username = m.username', 'LEFT')
//            ->join('user tu', 'tu.username = t.username', 'LEFT')
//            ->join('jsstatus s','s.id=p.status','LEFT')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->where('p.is_delete', 0)
            ->where('c.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
//            ->where('mu.status', 0)
//            ->where('tu.status', 0)
            ->where($where)
            //必须结项
            ->where('p.status',8)
//            ->group('p.uid')
            ->distinct(true)

            ->select();
//        return json($data);
        if ($data){
            return ['status'=>'success','message'=>$data];
        }else{
            return ['status'=>'error','message'=>'数据异常'];
        }
    }
    public function curriculum($username=''){
        if (!$username){
            $username=session('user.username');
        }
        if (!Yckuser::where('username',$username)->where('is_delete',0)->where('status',2)->find()){
            LogExecution(session('user.username').'进入英才库看'.$username.'用户不是英才');
            return json(['status' => 'error', 'message' => '用户不在英才库中']);
        }else{
            //限制查看的信息
//            $query = Yckcurriculum::
//            where('is_delete', 0)
//                ->where('username', $username)
//                ->find();
//            $field=['u.name','u.usermode','u.grade',''];
//            if ($query) {
//                // 检查每个字段是否为 1，并相应地添加到 $field 数组中
//                $ys = ['wx', 'qq', 'dd', 'qywx'];
//                $us = ['email'];
//
//                foreach ($ys as $y) {
//                    if ($query->$y == 1) {
//                        array_push($field, "y.{$y}");
//                    }
//                }
//                foreach ($us as $u) {
//                    if ($query->$u == 1) {
//                        array_push($field, "u.{$u}");
//                    }
//                }
//            }


            $user=User::alias('u')
                ->field('
                u.name,u.usermode,u.grade,
                y.intro,j.name as job,y.status,y.avatar,y.mark,
                d.name as college,
                m.name as major
                ')
                ->join('yckuser y','y.username=u.username','LEFT')
                ->join('yckjob j','y.job=j.id','LEFT')
                ->join('department d','d.id=u.college','LEFT')
                ->join('major m','m.id=u.major','LEFT')
                ->where('y.is_delete',0)
                ->where('u.status',0)
                ->where('u.username',$username)
                ->find();
            $domains=Yckdomain::where('is_delete',0)->where('username',$username)->column('name');
            $tags=Ycktag::where('is_delete',0)->where('username',$username)->column('name');
            $where=[];
            $field='p.uid,p.name,l.name as l_level,p.time';
            if ($user['usermode']==1){
                $where[]=['m.username','=',$username];
            $field=$field.',m.rank';
            }elseif ($user['usermode']==2){
                $where[]=['t.username','=',$username];
            $field=$field.',t.rank';
            }
            $dcs = Yckproject::alias('y')
                ->field($field)
                ->join('dcproject p', 'p.uid = y.uid', 'LEFT')
                ->join('member m', 'm.uid = y.uid', 'LEFT')
                ->join('teacher t', 't.uid = y.uid', 'LEFT')
                ->join('yckproject yp', 'yp.uid = y.uid', 'LEFT')


                ->join('dclevel l','l.id=p.level','LEFT')
                ->where('y.is_delete', 0)
                ->where('p.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('y.type','dc')
                ->where('yp.show',1)
                ->where('yp.is_delete',0)
                ->where($where)
                //必须结项
                ->where('p.status',21)
                ->distinct(true)
                ->select();
            $where=[];
        $field='p.name,l.name as l_level,CONCAT(a.level, a.type) AS award,p.created_at as time,c.name as c_name';
//            $field='p.uid, p.name';
            if ($user['usermode']==1){
                $where[]=['m.username','=',$username];
            $field=$field.',m.rank';
            }elseif ($user['usermode']==2){
                $where[]=['t.username','=',$username];
            $field=$field.',t.rank';
            }
            $jss = Yckproject::alias('y')
                ->field($field)
                ->join('jsproject p', 'p.uid = y.uid', 'LEFT')

                ->join('jscompetition c', 'c.cuid = p.cuid', 'LEFT')
                ->join('jsaward a', 'a.id = p.award', 'LEFT')
                ->join('member m', 'm.uid = y.uid', 'LEFT')
                ->join('yckproject yp', 'yp.uid = y.uid', 'LEFT')

                ->join('teacher t', 't.uid = y.uid', 'LEFT')
                ->join('jslevel l','l.id=c.level','LEFT')
                ->where('p.is_delete', 0)
                ->where('c.is_delete', 0)
                ->where('m.is_delete', 0)
                ->where('t.is_delete', 0)
                ->where('y.is_delete', 0)
                ->where('y.type','js')
                ->where('yp.show',1)
                ->where('yp.is_delete',0)
                ->where($where)
                //必须结项
                ->where('p.status',8)
                ->distinct(true)

                ->select();
            $data=[
                'user'=>$user,
                'domains'=>$domains,
                'tags'=>$tags,
                'dcs'=>$dcs,
                'jss'=>$jss
            ];
            if ($data){
                return ['status'=>'success','message'=>$data];
            }else{
                return ['status'=>'error','message'=>'数据异常'];
            }
        }
    }
}