{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/news/edit.css">
<script src="../../static/js/tinymce/tinymce.min.js"></script>
<div id="app">
    <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
      <a style="text-decoration: none;color: #00000097;" href="">首页</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="">新闻</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="news-lists">新闻管理</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="news-edit">新闻详情</a>
  </p>

  <template>
    <div id="main">
      <el-form :model="news" :rules="rules" ref="newsForm" label-position="top">
        <!-- 新闻标题 -->
        <el-form-item label="新闻标题" prop="title" class="form-item">
          <el-input 
            v-model="news.title" 
            placeholder="请输入新闻标题（2-30个字符）"
            class="custom-input"
          ></el-input>
        </el-form-item>
   
        <!-- 所属板块 -->
        <el-form-item label="所属板块" prop="class" class="form-item">
          <el-select 
            v-model="news.class" 
            placeholder="请选择新闻所属板块"
            class="custom-select"
          >
          {foreach $classes as $class}
            <el-option label="{$class.name}" value="{$class.id}"></el-option>
          {/foreach}
          </el-select>
        </el-form-item>
   
        <!-- 发布者 -->
        <el-form-item label="发布者" prop="auth" class="form-item">
          <el-input 
            v-model="news.auth" 
            placeholder="请输入发布者名称"
            class="custom-input"
          ></el-input>
        </el-form-item>
   
        <!-- 新闻内容 -->
        <el-form-item label="新闻内容" prop="content" class="form-item">
          <el-input 
            id="content" 
            type="textarea"
            :rows="6"
            placeholder="请输入新闻内容..."
            class="custom-textarea"
          ></el-input>
        </el-form-item>
      </el-form>
   
      <el-button 
        id="submit" 
        @click="submit" 
        type="primary" 
        class="submit-btn"
      >
        <span class="btn-text">提 交</span>
        <i class="el-icon-upload2 btn-icon"></i>
      </el-button>
    </div>
  </template>


  


</div> 
<script src="../../static/js/news/edit.js"></script>
</body>
</html>