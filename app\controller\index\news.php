<?php

namespace app\controller\index;

use app\BaseController;
use app\model\Newsclass;
use app\model\Newsdetail;

class news extends BaseController
{
    public function class(){
        $classes=Newsclass::where('is_delete',0)->order('rank','asc')->field('id,name')->select();
        $news=Newsdetail::where('is_delete',0)
            ->field('id,title,updated_at as time,class as category')
            ->order('class', 'asc')
            ->order('updated_at', 'desc')
            ->select();
        return view('index/newsclass',['classes'=>$classes,'news'=>$news]);
    }
    public function detail($news){
        $detail=Newsdetail::
            alias('d')
            ->join('newsclass c','c.id=d.class','LEFT')
            ->where('d.is_delete', 0)
            ->where('d.id',$news)
            ->field('d.id,d.title,d.updated_at as date,d.content,c.name as c_name,d.auth')
            ->find();
        return view('index/newsdetail',['detail'=>$detail]);
    }
}