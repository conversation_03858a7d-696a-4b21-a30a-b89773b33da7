<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;
Route::rule('login', '/admin/auth/login','GET|POST');
Route::get('logout', '/admin/auth/logout');

Route::get('manage', '/admin/index/index');
Route::get('dashboard', '/admin/index/dashboard');
Route::post('admin/getDashboardData', '/admin/index/getDashboardData');

Route::rule('dc-addproject', '/dcmanage/addProject/addProject','GET|POST');
Route::post('dc-search', '/dcmanage/lists/select_dc');
Route::post('dc-check', '/dcmanage/check/checkProject');
Route::post('dc-exportprojects', '/dcmanage/lists/export_projects');
Route::get('dc-intermproject', '/dcmanage/intermproject/index');
Route::get('dc-concludeproject', '/dcmanage/concludeproject/index');
Route::post('dc-addconclude', '/dcmanage/concludeproject/add_conclude');
Route::get('dc-extension', '/dcmanage/extension/index');
Route::post('dc-applyextension', '/dcmanage/extension/apply_extension');
Route::get('dc-midchange', '/dcmanage/midchange/index');
Route::post('dc-applymidchange', '/dcmanage/midchange/apply_midchange');

Route::get('dc-lists', '/dcmanage/lists/index');
Route::get('dc-detail', '/dcmanage/detail/index');
Route::post('dc-addinterm', '/dcmanage/intermproject/add_interm');

Route::post('bs-searchindex', '/basic/search/index');
Route::post('bs-searchuser', '/basic/search/user');
Route::post('bs-searchuser', '/basic/search/user');
Route::post('bs-searchdc', '/basic/search/dc');
Route::post('bs-searchjs', '/basic/search/js');
Route::post('bs-searchcurriculum', '/basic/search/curriculum');
Route::post('bs-searchnews', '/basic/search/news');




Route::rule('bs-addfile/:class/:type', '/basic/files/addfile', 'POST')->pattern(['class' => '\w+', 'type' => '\w+']);
Route::post('bs-addfile', '/basic/files/addfile');
Route::post('bs-upload-carousel', '/basic/files/uploadCarousel');


//竞赛管理
Route::get('js-competition', '/jsmanage/competition/index');
//竞赛列表（学生查看）
Route::get('js-lists', '/jsmanage/lists/index');
//参加项目列表
Route::get('js-project', '/jsmanage/project/index');
//项目详情
Route::get('js-projectdetail', '/jsmanage/detail/index');

Route::post('js-searchcompetition', '/jsmanage/competition/select_competition');
Route::post('js-searchlists', '/jsmanage/lists/select_competition');
Route::post('js-deletecompetition', '/jsmanage/competition/deleteCompetition');
Route::post('js-addcompetition', '/jsmanage/competition/addCompetition');
Route::post('js-updatecompetition', '/jsmanage/competition/updateCompetition');
Route::post('js-searchproject', '/jsmanage/project/select_project');
Route::post('js-exportprojects', '/jsmanage/project/export_projects');

Route::rule('js-addproject', '/jsmanage/addProject/addProject','GET|POST');
Route::rule('js-editproject', '/jsmanage/addProject/editProject','GET|POST');

Route::post('js-check', '/jsmanage/check/checkProject');
//上传证书
Route::post('js-addcertificate', '/jsmanage/certificate/addcertificate');

//英才库
Route::get('yck-curriculum', '/yckmanage/curriculum/index');
Route::post('yck-updatecurriculum', '/yckmanage/curriculum/update');

Route::rule('yck-apply', '/yckmanage/apply/apply','GET|POST');
Route::get('yck-applylist', '/yckmanage/applylist/index');
Route::get('yck-lists', '/yckmanage/lists/index');
Route::post('yck-searchlists', '/yckmanage/lists/select_lists');
Route::post('yck-updatelists', '/yckmanage/lists/update_lists');
Route::post('yck-exportlists', '/yckmanage/lists/export_lists');
Route::get('yck-homepage-talents', '/yckmanage/lists/get_homepage_talents');


Route::post('yck-searchapply', '/yckmanage/applylist/select_apply');
Route::get('yck-applydetail', '/yckmanage/applydetail/index');
Route::post('yck-check', '/yckmanage/check/checkyck');
Route::post('yck-seecontact', '/yckmanage/curriculum/seecontact');


//前台新闻管理
Route::get('news-class', '/index/news/class');
Route::get('news-detail', '/index/news/detail');
//后台新闻
Route::get('news-classes', '/newsmanage/classes/index');
Route::get('news-lists', '/newsmanage/lists/index');
Route::post('news-editclasses', '/newsmanage/classes/edit');


Route::rule('news-edit', '/newsmanage/edit/index','GET|POST');

Route::post('news-searchclasses', '/newsmanage/classes/classlist');
Route::post('news-searchlists', '/newsmanage/lists/newslist');
Route::post('news-export', '/newsmanage/lists/export_news');

// 轮播管理路由
Route::get('news-carousel', '/newsmanage/carouselmanage/index');
Route::post('news-carousel-list', '/newsmanage/carouselmanage/getCarouselList');
Route::rule('news-carousel-add', '/newsmanage/carouselmanage/addCarousel', 'GET|POST');
Route::rule('news-carousel-edit', '/newsmanage/carouselmanage/editCarousel', 'GET|POST');
Route::post('news-carousel-delete', '/newsmanage/carouselmanage/deleteCarousel');
Route::post('news-carousel-status', '/newsmanage/carouselmanage/updateStatus');
Route::post('news-carousel-sort', '/newsmanage/carouselmanage/updateSort');
Route::get('news-homepage-carousel', '/newsmanage/carouselmanage/getHomepageCarousel');
Route::get('news-carousel-newslist', '/newsmanage/carouselmanage/getNewsList');
Route::get('news-carousel-info', '/newsmanage/carouselmanage/getCarouselInfo');

//个人信息
Route::get('bs-mine', '/basic/mine/index');
Route::post('bs-searchmine', '/basic/mine/mine');
Route::post('bs-updatemine', '/basic/mine/update');

//管理
//成员管理
Route::get('bs-users', '/basic/users/index');
Route::post('bs-searchusers', '/basic/users/select_users');
Route::post('bs-getusermodes', '/basic/users/get_usermodes');
Route::post('bs-getdepartments', '/basic/users/get_departments');
Route::post('bs-getmajors', '/basic/users/get_majors');
Route::post('bs-deleteuser', '/basic/users/delete');
Route::post('bs-manageuser', '/basic/users/manage');
Route::post('bs-resetpassword', '/basic/users/reset_password');
Route::post('bs-exportusers', '/basic/users/export_users');

// 部门管理
Route::rule('bs-department', '/basic/departmentcontroller/index', 'GET');
Route::post('bs-getdepartmentlist', '/basic/departmentcontroller/get_departments');
Route::post('bs-getdepartment', '/basic/departmentcontroller/get_department');
Route::post('bs-adddepartment', '/basic/departmentcontroller/add_department');
Route::post('bs-updatedepartment', '/basic/departmentcontroller/update_department');
Route::post('bs-deletedepartment', '/basic/departmentcontroller/delete_department');
Route::post('bs-addmajor', '/basic/departmentcontroller/add_major');
Route::post('bs-updatemajor', '/basic/departmentcontroller/update_major');
Route::post('bs-deletemajor', '/basic/departmentcontroller/delete_major');
Route::post('bs-exportdepartments', '/basic/departmentcontroller/export_departments');






// 开启多模块URL自动解析 `8.1+`版本开始支持
Route::auto();
