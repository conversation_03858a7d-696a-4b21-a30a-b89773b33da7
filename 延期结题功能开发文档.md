# 大创平台延期结题功能开发文档

## 项目概述

本文档记录了大创平台延期结题功能的完整开发过程，包括需求分析、数据库设计、后端开发、前端开发等四个阶段。

## 功能需求

### 业务需求
1. **申请条件**：项目状态为待提交结题报告或延期结题驳回状态时，学生可申请延期结题
2. **延期选项**：学生可选择延期时间（6个月或1年）和填写延期理由
3. **审核流程**：经过教师、学院、学校三级审核
4. **状态流转**：审核通过后状态变为延期结题可审核状态，学生可继续提交结题报告
5. **界面要求**：
   - 前端悬浮菜单添加申请延期结题选项
   - 项目详情右侧和左侧表格展示延期信息
   - 教师结题审核状态增加延期结题审核状态

## 第一阶段：数据库设计与模型层开发

### 1.1 数据库表设计

#### dcextension 表结构
```sql
CREATE TABLE `dcextension` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) NOT NULL COMMENT '项目ID',
  `extension_time` varchar(10) NOT NULL COMMENT '延期时间（6或12个月）',
  `reason` text NOT NULL COMMENT '延期理由',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='延期结题申请表';
```

### 1.2 模型层开发

#### Dcextension 模型
**文件位置**：`app/model/Dcextension.php`

```php
<?php

namespace app\model;

use think\Model;

class Dcextension extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'dcextension';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
}
```

**功能特点**：
- 自动时间戳管理
- 软删除支持
- 与ThinkPHP框架完美集成

## 第二阶段：后端控制器开发

### 2.1 延期结题控制器

#### Extension 控制器
**文件位置**：`app/controller/dcmanage/Extension.php`

#### 核心方法

##### index() - 延期结题申请页面
```php
public function index($uid){
    // 验证项目存在性和用户权限
    // 检查项目状态是否允许申请延期结题
    // 返回延期申请页面
}
```

**功能特点**：
- 项目存在性验证
- 用户权限验证（只有项目负责人可申请）
- 状态验证（只允许特定状态申请）
- 支持修改已存在的延期申请

##### apply_extension() - 提交延期结题申请
```php
public function apply_extension($uid){
    // 验证用户权限和数据完整性
    // 计算新状态
    // 提交或更新延期申请
}
```

**功能特点**：
- 数据验证（延期时间、延期理由）
- 状态流转计算
- 事务处理确保数据一致性
- 支持新申请和修改申请

#### 状态验证方法
```php
private function canApplyExtension($status) {
    // 状态8：待提交结题报告
    // 状态29：指导教师延期结题驳回
    // 状态30：学院延期结题驳回
    // 状态31：学校延期结题驳回
    $allowedStatus = [8, 29, 30, 31];
    return in_array($status, $allowedStatus);
}
```

#### 状态流转计算
```php
private function calculateExtensionStatus($currentStatus) {
    $statusMap = [
        8 => 22,   // 新申请 -> 等待教师审核
        29 => 22,  // 教师驳回后重新申请 -> 等待教师审核
        30 => 23,  // 学院驳回后重新申请 -> 等待学院审核
        31 => 24,  // 学校驳回后重新申请 -> 等待学校审核
    ];
    return $statusMap[$currentStatus] ?? $currentStatus;
}
```

### 2.2 审核控制器扩展

#### Check 控制器修改
**文件位置**：`app/controller/dcmanage/Check.php`

#### 扩展内容

##### 可审核状态扩展
```php
private function canCheckCurrentStatus($status, $userMode) {
    $checkableStatus = [
        2 => [1, 5, 9, 22], // 教师可审核的状态（立项、中期、结题、延期）
        3 => [2, 6, 10, 23], // 学院可审核的状态（立项、中期、结题、延期）
        4 => [3, 7, 11, 24], // 学校可审核的状态（立项、中期、结题、延期）
    ];
    return isset($checkableStatus[$userMode]) && in_array($status, $checkableStatus[$userMode]);
}
```

##### 审核状态映射扩展
```php
private function getCheckStatus($currentStatus) {
    $statusMap = [
        // ... 原有状态映射
        22 => 4, // 延期审核
        23 => 4, // 延期审核
        24 => 4, // 延期审核
    ];
    return $statusMap[$currentStatus] ?? 0;
}
```

##### 审核通过状态流转
```php
private function getApproveStatus($currentStatus, $userMode) {
    $approveMap = [
        // 教师审核通过
        2 => [
            // ... 原有状态映射
            22 => 23, // 延期审核通过 -> 等待学院审核
        ],
        // 学院审核通过
        3 => [
            // ... 原有状态映射
            23 => 24, // 延期审核通过 -> 等待学校审核
        ],
        // 学校审核通过
        4 => [
            // ... 原有状态映射
            24 => 32, // 延期审核通过 -> 延期结题可以审核了
        ],
    ];
    return $approveMap[$userMode][$currentStatus] ?? $currentStatus;
}
```

##### 审核驳回状态流转
```php
private function getRejectStatus($currentStatus, $userMode) {
    $rejectMap = [
        // 教师审核驳回
        2 => [
            // ... 原有状态映射
            22 => 29, // 延期审核驳回
        ],
        // 学院审核驳回
        3 => [
            // ... 原有状态映射
            23 => 30, // 延期审核驳回
        ],
        // 学校审核驳回
        4 => [
            // ... 原有状态映射
            24 => 31, // 延期审核驳回
        ],
    ];
    return $rejectMap[$userMode][$currentStatus] ?? $currentStatus;
}
```

### 2.3 结题报告控制器扩展

#### Concludeproject 控制器修改
**文件位置**：`app/controller/dcmanage/Concludeproject.php`

#### 扩展内容

##### 可提交结题报告状态扩展
```php
private function canSubmitConcludeReport($status) {
    // 状态8：待提交结题报告
    // 状态18：指导教师结题审核驳回
    // 状态19：学院结题审核驳回
    // 状态20：学校结题审核驳回
    // 状态29：教师延期审核驳回
    // 状态30：学院延期审核驳回
    // 状态31：学校延期审核驳回
    // 状态32：延期审核通过，可提交结题报告
    $allowedStatus = [8, 9, 18, 19, 20, 29, 30, 31, 32];
    return in_array($status, $allowedStatus);
}
```

##### 结题报告状态流转扩展
```php
private function calculateConcludeStatus($currentStatus) {
    $statusMap = [
        // ... 原有状态映射
        29 => 9,  // 教师延期驳回后提交结题报告 -> 等待教师审核
        30 => 10, // 学院延期驳回后提交结题报告 -> 等待学院审核
        31 => 11, // 学校延期驳回后提交结题报告 -> 等待学校审核
        32 => 9,  // 延期审核通过后提交结题报告 -> 等待教师审核
    ];
    return $statusMap[$currentStatus] ?? $currentStatus;
}
```

### 2.4 项目详情控制器扩展

#### Detail 控制器修改
**文件位置**：`app/controller/dcmanage/Detail.php`

#### 扩展内容

##### 延期信息查询
```php
$extension = Dcextension::where('is_delete',0)->where('uid',$uid)->find();
```

##### 视图数据传递
```php
return view('dcmanage/detail',[
    // ... 原有数据
    'extension' => $extension,
]);
```

## 第三阶段：前端界面开发

### 3.1 延期申请页面

#### extension.html 页面
**文件位置**：`app/view/dcmanage/extension.html`

#### 页面结构
```html
<!DOCTYPE html>
<html>
<head>
    <!-- Element UI 样式和脚本 -->
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 项目信息展示 -->
            <div class="info-box">
                <h4>项目信息</h4>
                <p><strong>项目名称：</strong>{{project.name}}</p>
                <p><strong>项目负责人：</strong>{{project.leader}}</p>
                <p><strong>项目级别：</strong>{{project.level}}</p>
                <p><strong>当前状态：</strong>{{getStatusText(project.status)}}</p>
            </div>

            <!-- 延期申请表单 -->
            <el-form :model="form" :rules="rules" ref="form" label-width="120px">
                <div class="form-section">
                    <h3>延期申请信息</h3>
                    
                    <!-- 延期时间选择 -->
                    <el-form-item label="延期时间" prop="extension_time">
                        <el-select v-model="form.extension_time" placeholder="请选择延期时间">
                            <el-option label="6个月" value="6"></el-option>
                            <el-option label="1年" value="12"></el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 延期理由填写 -->
                    <el-form-item label="延期理由" prop="reason">
                        <el-input 
                            type="textarea" 
                            v-model="form.reason" 
                            placeholder="请详细说明申请延期的理由..."
                            :rows="6">
                        </el-input>
                    </el-form-item>
                </div>

                <!-- 提交按钮 -->
                <div class="submit-section">
                    <el-button type="primary" @click="submitForm" :loading="loading">
                        {{loading ? '提交中...' : '提交申请'}}
                    </el-button>
                    <el-button @click="goBack">返回</el-button>
                </div>
            </el-form>
        </div>
    </div>
</body>
</html>
```

#### Vue.js 功能实现
```javascript
new Vue({
    el: '#app',
    data() {
        return {
            project: {
                name: '{$project.name}',
                leader: '{$project.leader}',
                level: '{$project.level}',
                status: {$project.status}
            },
            extension: {$extension|json_encode},
            form: {
                extension_time: '',
                reason: ''
            },
            rules: {
                extension_time: [
                    { required: true, message: '请选择延期时间', trigger: 'change' }
                ],
                reason: [
                    { required: true, message: '请填写延期理由', trigger: 'blur' },
                    { min: 10, message: '延期理由至少10个字符', trigger: 'blur' }
                ]
            },
            loading: false
        }
    },
    mounted() {
        // 如果有已存在的延期申请，填充表单
        if (this.extension) {
            this.form.extension_time = this.extension.extension_time;
            this.form.reason = this.extension.reason;
        }
    },
    methods: {
        // 状态文本显示
        getStatusText(status) {
            const statusMap = {
                8: '待提交结题报告',
                22: '等待教师延期审核',
                23: '等待学院延期审核',
                24: '等待学校延期审核',
                29: '教师延期审核驳回',
                30: '学院延期审核驳回',
                31: '学校延期审核驳回',
                32: '延期审核通过，可提交结题报告'
            };
            return statusMap[status] || '未知状态';
        },
        // 提交表单
        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    
                    axios.post('/dcmanage/extension/apply_extension/{$project.uid}', {
                        data: this.form
                    })
                    .then(response => {
                        if (response.data.status === 'success') {
                            this.$message.success(response.data.message);
                            setTimeout(() => {
                                this.goBack();
                            }, 1500);
                        } else {
                            this.$message.error(response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('提交失败:', error);
                        this.$message.error('提交失败，请重试');
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                } else {
                    this.$message.warning('请完善表单信息');
                }
            });
        },
        goBack() {
            window.history.back();
        }
    }
});
```

#### 样式设计
```css
body {
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}
.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}
.info-box {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}
```

### 3.2 项目详情页面扩展

#### detail.html 页面修改
**文件位置**：`app/view/dcmanage/detail.html`

#### 延期信息显示区域
```html
{if $extension}
<el-descriptions :label-style="label_style" direction="vertical"  :content-style="content_style" class="margin-top" title="四.延期结题申请" :column="1" border>
  <el-descriptions-item>
    <template slot="label">
      <i class="el-icon-user"></i>
      延期时间
    </template>
    {$extension.extension_time}个月
  </el-descriptions-item>
  <el-descriptions-item>
    <template slot="label">
      <i class="el-icon-user"></i>
      延期理由
    </template>
    {$extension.reason}
  </el-descriptions-item>
</el-descriptions>
{/if}
```

### 3.3 悬浮菜单扩展

#### detail.html 悬浮菜单修改
```html
<!-- 学生操作 - 只有学生或超级管理员可见 -->
<el-menu-item-group v-if="isStudentOperation">
<template slot="title">学生操作</template>
<el-menu-item index="1" @click="handleMenuClick(1)">修改项目立项信息</el-menu-item>
<el-menu-item index="2" @click="handleMenuClick(2)">提交中期报告</el-menu-item>
<el-menu-item index="3" @click="handleMenuClick(3)">提交结题报告</el-menu-item>
<el-menu-item index="4" @click="handleMenuClick(4)">申请延期结题</el-menu-item>
<el-menu-item index="5" @click="handleMenuClick(5)">申请中期变更</el-menu-item>
</el-menu-item-group>
```

#### detail.js 菜单处理逻辑扩展
**文件位置**：`public/static/js/dc/detail.js`

```javascript
handleMenuClick(type){
    this.checktitle='';
    this.checkform.remark='';
    if(type == 1){
        window.location.href = 'dc-addproject?uid='+this.uid;
    }else if(type == 2){
        window.location.href = 'dc-intermproject?uid='+this.uid;
    }else if(type == 3){
        window.location.href = 'dc-concludeproject?uid='+this.uid;
    }else if(type == 4){
        window.location.href = 'dc-extension?uid='+this.uid;
    }else if(type == 5){
        // 申请中期变更（待实现）
        this.$message.info('中期变更功能待实现');
    }
    // ... 其他菜单项处理
}
```

## 第四阶段：路由配置与系统集成

### 4.1 路由配置

#### app.php 路由文件修改
**文件位置**：`route/app.php`

```php
// 延期结题相关路由
Route::get('dc-extension', '/dcmanage/extension/index');
Route::post('dc-applyextension', '/dcmanage/extension/apply_extension');
```

### 4.2 状态流转图

#### 延期申请状态流转
```
状态8（待提交结题报告）
    ↓ 申请延期
状态22（等待教师审核）
    ↓ 教师通过
状态23（等待学院审核）
    ↓ 学院通过
状态24（等待学校审核）
    ↓ 学校通过
状态32（延期审核通过，可提交结题报告）
    ↓ 提交结题报告
状态9（等待教师结题审核）

延期驳回后可以申请结题：
状态29（教师延期驳回）
    ↓ 申请结题报告
状态9（等待教师结题审核）

状态30（学院延期驳回）
    ↓ 申请结题报告
状态10（等待学院结题审核）

状态31（学校延期驳回）
    ↓ 申请结题报告
状态11（等待学校结题审核）
```

#### 延期驳回状态可申请结题设计
为了提供更灵活的操作选择，延期驳回状态（29、30、31）的学生也可以直接提交结题报告，无需重新申请延期。这样设计的好处：

1. **操作灵活性**：学生可以根据实际情况选择重新申请延期或直接提交结题报告
2. **流程简化**：如果学生认为项目已经可以结题，可以直接提交结题报告
3. **时间效率**：避免不必要的延期申请流程，提高项目完成效率

**实现方式**：
- 在结题报告控制器中，延期驳回状态（29、30、31）被添加到可提交结题报告的状态列表中
- 在延期申请页面中，延期驳回状态显示为"可重新申请延期或提交结题报告"
- 学生可以根据项目实际情况选择最合适的操作路径

#### 延期审核驳回状态流转
```
状态22（等待教师审核）
    ↓ 教师驳回
状态29（教师延期驳回）
    ↓ 重新申请
状态22（等待教师审核）

状态23（等待学院审核）
    ↓ 学院驳回
状态30（学院延期驳回）
    ↓ 重新申请
状态23（等待学院审核）

状态24（等待学校审核）
    ↓ 学校驳回
状态31（学校延期驳回）
    ↓ 重新申请
状态24（等待学校审核）
```

### 4.3 权限控制矩阵

| 用户角色 | 申请延期 | 教师审核 | 学院审核 | 学校审核 | 查看延期信息 |
|---------|---------|---------|---------|---------|-------------|
| 学生 | ✅ | ❌ | ❌ | ❌ | ✅ |
| 教师 | ❌ | ✅ | ❌ | ❌ | ✅ |
| 学院管理员 | ❌ | ❌ | ✅ | ❌ | ✅ |
| 学校管理员 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 超级管理员 | ✅ | ✅ | ✅ | ✅ | ✅ |

### 4.4 数据验证规则

#### 前端验证
```javascript
rules: {
    extension_time: [
        { required: true, message: '请选择延期时间', trigger: 'change' }
    ],
    reason: [
        { required: true, message: '请填写延期理由', trigger: 'blur' },
        { min: 10, message: '延期理由至少10个字符', trigger: 'blur' }
    ]
}
```

#### 后端验证
```php
// 验证数据
if (!$data['extension_time'] || !$data['reason']) {
    LogExecution(session('user.name').'延期结题申请数据不完整');
    return json(['status'=>'error','message' => '请填写完整的延期申请信息']);
}
```

## 功能测试要点

### 5.1 学生功能测试
1. **申请条件测试**：验证只有特定状态的项目可以申请延期
2. **权限测试**：验证只有项目负责人可以申请延期
3. **表单验证测试**：验证延期时间和理由的必填性
4. **提交功能测试**：验证延期申请的正常提交
5. **修改功能测试**：验证已存在延期申请的修改功能

### 5.2 审核功能测试
1. **教师审核测试**：验证教师可以审核延期申请
2. **学院审核测试**：验证学院可以审核延期申请
3. **学校审核测试**：验证学校可以审核延期申请
4. **驳回功能测试**：验证各级审核的驳回功能
5. **通过功能测试**：验证各级审核的通过功能

### 5.3 状态流转测试
1. **申请状态流转**：验证延期申请的状态正确流转
2. **审核状态流转**：验证延期审核的状态正确流转
3. **结题状态流转**：验证延期通过后可以提交结题报告
4. **驳回重新申请**：验证驳回后可以重新申请
5. **延期驳回可申请结题**：验证延期驳回状态可以直接提交结题报告

### 5.4 界面功能测试
1. **悬浮菜单显示**：验证延期申请选项正确显示
2. **项目详情显示**：验证延期信息正确显示
3. **表单交互**：验证延期申请表单的正常交互
4. **响应式设计**：验证界面在不同设备上的显示效果

## 技术特点总结

### 6.1 架构设计
- **MVC架构**：严格遵循ThinkPHP的MVC架构模式
- **分层设计**：模型层、控制器层、视图层职责分明
- **模块化设计**：延期功能作为独立模块，便于维护和扩展

### 6.2 数据安全
- **事务处理**：所有数据操作使用数据库事务确保一致性
- **权限控制**：严格的用户权限验证
- **数据验证**：前后端双重数据验证
- **软删除**：支持数据软删除，保护数据完整性

### 6.3 用户体验
- **响应式设计**：适配不同设备屏幕
- **实时反馈**：操作结果实时反馈给用户
- **表单验证**：友好的表单验证提示
- **状态显示**：清晰的状态流转显示
- **操作灵活性**：延期驳回状态可选择重新申请延期或直接提交结题报告

### 6.4 代码质量
- **代码规范**：遵循PSR-4自动加载规范
- **注释完整**：关键代码都有详细注释
- **错误处理**：完善的异常处理机制
- **日志记录**：详细的操作日志记录

## 后续优化建议

### 7.1 功能扩展
1. **中期变更功能**：实现项目中期变更申请和审核
2. **批量操作**：支持批量审核延期申请
3. **统计分析**：添加延期申请的统计分析功能
4. **通知功能**：添加延期申请状态变更通知

### 7.2 性能优化
1. **缓存机制**：添加Redis缓存提升查询性能
2. **分页优化**：大数据量时的分页性能优化
3. **数据库优化**：添加必要的数据库索引

### 7.3 用户体验优化
1. **移动端适配**：优化移动端显示效果
2. **操作引导**：添加用户操作引导
3. **快捷键支持**：添加键盘快捷键支持
4. **主题切换**：支持深色/浅色主题切换

## 总结

延期结题功能已经完成了四个阶段的开发：

1. **第一阶段**：完成了数据库设计和模型层开发，建立了数据基础
2. **第二阶段**：完成了后端控制器开发，实现了核心业务逻辑
3. **第三阶段**：完成了前端界面开发，提供了用户友好的操作界面
4. **第四阶段**：完成了路由配置和系统集成，确保功能正常运行

该功能严格按照需求设计，实现了完整的延期申请、审核、状态流转等功能，具有良好的扩展性和维护性。通过严格的权限控制和数据验证，确保了系统的安全性和数据完整性。

---

**文档版本**：v1.0  
**创建时间**：2024年12月  
**最后更新**：2024年12月  
**维护人员**：开发团队 