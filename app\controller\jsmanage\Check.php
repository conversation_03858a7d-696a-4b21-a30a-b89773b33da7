<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Jscheck;
use app\model\Jsprogress;
use app\model\Jsproject;
use app\model\Member;
use app\model\Teacher;
use Exception;
use think\facade\Db;

class Check extends BaseController
{
    public function checkProject($uid){
            // 检查用户是否有权限审核该项目
            if (!$this->checkPermission('js_check_teacher') && 
                !$this->checkPermission('js_check_college') && 
                !$this->checkPermission('js_check_school')) {
                LogExecution('用户尝试审核无权限的项目：' . $uid);
                return json(['status' => 'error', 'message' => '权限不足']);
            }
            
            // 检查用户是否可以审核该项目
            if (!$this->canCheckProject($uid, 'js')) {
                LogExecution('用户尝试审核无权限的项目：' . $uid);
                return json(['status' => 'error', 'message' => '您没有权限审核该项目']);
            }
            
            $check=input('post.check.check');
            $remark=input('post.check.remark');
            if (!$remark){
                LogExecution($uid.'审核失败，请输入审核意见');
                return ['status'=>'error','message'=>'审核失败，请输入审核意见'];
            }
            $project=Jsproject::where('is_delete',0)->where('uid',$uid)->find();
            $new_status=$project['status'];
            $checks=[
                'uid'=>$project['uid'],
                'type'=>'',
                'check'=>'',
                'status'=>'',
                'remark'=>'',
            ];
            $progess=[
                'uid'=>$project['uid'],
                'action'=>'',
                'remark'=>'',
            ];
            if (!$check){
                //通过
                $checks['check']=0;
                if (session('user.usermode')==2){
                    //判断是否为第一指导教师
                    if (
                        !Teacher::
                        where('is_delete',0)
                            ->where('uid',$uid)
                            ->where('username',session('user.username'))
                            ->where('rank',1)
                        ->find()
                    ){
                        LogExecution($uid.'审核失败，您不是该项目的第一指导教师'.session('user.username'));
                        return ['status'=>'error','message'=>'审核失败，您不是该项目的第一指导教师'];
                    }
                    $checks['type']=1;
                    if ($project['status']==1){
                        $checks['status']=1;
                        $new_status=2;
                        $progess['action']='指导教师报名审核';
                        $progess['remark']='指导教师报名审核通过';
                    }elseif ($project['status']==5){
                        $checks['status']=6;
                        $new_status=6;
                        $progess['action']='指导教师证书审核';
                        $progess['remark']='指导教师证书审核通过';
                    }
                }elseif (session('user.usermode')==5){
                    //判断是否为该学院内项目
                    if (
                        !Member::alias('m')
                            ->join('user u', 'm.username = u.username', 'LEFT')
                        
                        ->where('m.is_delete',0)
                            ->where('u.status',0)
                        ->where('m.rank',1)
                        ->where('m.uid',$uid)
                        ->where('u.college',session('user.college'))
                        ->find()
                    ){
                        LogExecution($uid.'审核失败，该项目不属于您的学院'.session('user.username'));
                        return ['status'=>'error','message'=>'审核失败，该项目不属于您的学院'];
                    }
                    $checks['type']=2;
                    if ($project['status']==2){
                        $checks['status']=1;
                        $new_status=3;
                        $progess['action']='学院报名审核';
                        $progess['remark']='学院报名审核通过';
                    }elseif ($project['status']==6){
                        $checks['status']=6;
                        $new_status=7;
                        $progess['action']='学院报名审核';
                        $progess['remark']='学院报名审核通过';
                    }
                }elseif (session('user.usermode')==6){
                    $checks['type']=3;
                    if ($project['status']==3){
                        $checks['status']=1;
                        $new_status=4;
                        $progess['action']='学校报名审核';
                        $progess['remark']='学校报名审核通过';
                    }elseif ($project['status']==7){
                        $checks['status']=6;
                        $new_status=8;
                        $progess['action']='学校报名审核';
                        $progess['remark']='学校报名审核通过';
                    }
                }
            }else{
                //驳回
                $checks['check']=1;
                if (session('user.usermode')==2){
                    //判断是否为第一指导教师
                    if (
                        !Teacher::
                        where('is_delete',0)
                            ->where('uid',$uid)
                            ->where('username',session('user.username'))
                            ->where('rank',1)
                            ->find()
                    ){
                        LogExecution($uid.'审核失败，您不是该项目的第一指导教师'.session('user.username'));
                        return ['status'=>'error','message'=>'审核失败，您不是该项目的第一指导教师'];
                    }
                    $checks['type']=1;
                    if ($project['status']==1){
                        $checks['status']=1;
                        $new_status=9;
                        $progess['action']='指导教师报名审核';
                        $progess['remark']='指导教师报名审核驳回';
                    }elseif ($project['status']==5){
                        $checks['status']=6;
                        $new_status=12;
                        $progess['action']='指导教师证书审核';
                        $progess['remark']='指导教师证书审核驳回';
                    }
                }elseif (session('user.usermode')==5){
                    //判断是否为该学院内项目
                    if (
                        !Member::alias('m')
                            ->join('user u', 'm.username = u.username', 'LEFT')

                            ->where('m.is_delete',0)
                            ->where('u.status',0)
                            ->where('m.rank',1)
                            ->where('m.uid',$uid)
                            ->where('u.college',session('user.college'))
                            ->find()
                    ){
                        LogExecution($uid.'审核失败，该项目不属于您的学院'.session('user.username'));
                        return ['status'=>'error','message'=>'审核失败，该项目不属于您的学院'];
                    }
                    $checks['type']=2;
                    if ($project['status']==2){
                        $checks['status']=1;
                        $new_status=10;
                        $progess['action']='学院报名审核';
                        $progess['remark']='学院报名审核驳回';
                    }elseif ($project['status']==6){
                        $checks['status']=6;
                        $new_status=13;
                        $progess['action']='学院证书审核';
                        $progess['remark']='学院证书审核驳回';
                    }
                }elseif (session('user.usermode')==6){
                    $checks['type']=3;
                    if ($project['status']==3){
                        $checks['status']=1;
                        $new_status=11;
                        $progess['action']='学校报名审核';
                        $progess['remark']='学校报名审核驳回';
                    }elseif ($project['status']==7){
                        $checks['status']=6;
                        $new_status=14;
                        $progess['action']='学校证书审核';
                        $progess['remark']='学校证书审核驳回';
                    }
                }
            }
            $checks['remark']=$remark;
            //插入三个表
//        return $new_status;
        // 开启事务
        Db::startTrans();
        try {
            if (
            Jsproject::where('is_delete',0)->where('uid',$project['uid'])->update(['status'=>$new_status])&&
            Jscheck::insert($checks)&&
            Jsprogress::insert($progess)
            ){
                // 如果所有操作成功，提交事务
                Db::commit();
                    LogExecution($uid.'审核成功');
                    return ['status'=>'success','message'=>'审核成功'];

            }else{
                // 如果前面的操作中有任何一个失败，回滚事务
                Db::rollback();
                throw new Exception('立项失败');
            }


        }catch (Exception $e) {
            // 如果前面的操作中有任何一个失败，回滚事务
            Db::rollback();
            // 捕获异常，记录失败日志并返回错误信息
            LogExecution($project['uid'] . '审核失败，请检查环境及填写内容');
            return ['status' => 'error', 'message' => '审核失败，请检查环境及填写内容'];
        }


    }
}