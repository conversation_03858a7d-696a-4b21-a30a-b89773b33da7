{include file="public/_header"}

<div id="app">
    <el-container>
        <el-header>
            <h2>轮播管理</h2>
        </el-header>
        
        <el-main>
            <!-- 搜索区域 -->
            <el-card class="search-card">
                <el-form :inline="true" :model="searchForm" class="search-form">
                    <el-form-item label="轮播标题">
                        <el-input v-model="searchForm.title" placeholder="请输入轮播标题" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                            <el-option label="启用" :value="1"></el-option>
                            <el-option label="禁用" :value="0"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchCarousel">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button type="success" @click="addCarousel">添加轮播</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
            
            <!-- 轮播列表 -->
            <el-card class="list-card">
                <el-table :data="carouselList" style="width: 100%" v-loading="loading">
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="title" label="轮播标题" min-width="200"></el-table-column>
                    <el-table-column label="轮播图片" width="150">
                        <template slot-scope="scope">
                            <el-image 
                                style="width: 100px; height: 60px" 
                                :src="scope.row.image_path" 
                                fit="cover"
                                :preview-src-list="[scope.row.image_path]">
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="news_title" label="关联新闻" min-width="200"></el-table-column>
                    <el-table-column prop="news_class" label="新闻分类" width="120"></el-table-column>
                    <el-table-column prop="sort_order" label="排序" width="80"></el-table-column>
                    <el-table-column label="状态" width="100">
                        <template slot-scope="scope">
                            <el-switch
                                v-model="scope.row.status"
                                :active-value="1"
                                :inactive-value="0"
                                @change="updateStatus(scope.row)">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" type="primary" @click="editCarousel(scope.row)">编辑</el-button>
                            <el-button size="mini" type="danger" @click="deleteCarousel(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="pagination.current"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pagination.size"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total">
                    </el-pagination>
                </div>
            </el-card>
        </el-main>
    </el-container>
</div>

<script>
new Vue({
    el: '#app',
    data() {
        return {
            loading: false,
            searchForm: {
                title: '',
                status: ''
            },
            carouselList: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0
            }
        }
    },
    created() {
        this.getCarouselList();
    },
    methods: {
        // 获取轮播列表
        getCarouselList() {
            this.loading = true;
            axios.post('/news-carousel-list', {
                search: this.searchForm
            }).then(response => {
                if (response.data.status === 'success') {
                    this.carouselList = response.data.message.data;
                    this.pagination.total = response.data.message.total;
                } else {
                    this.$message.error(response.data.message);
                }
            }).catch(error => {
                console.error('获取轮播列表错误:', error);
                this.$message.error('获取轮播列表失败');
            }).finally(() => {
                this.loading = false;
            });
        },
        
        // 搜索轮播
        searchCarousel() {
            this.pagination.current = 1;
            this.getCarouselList();
        },
        
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                title: '',
                status: ''
            };
            this.searchCarousel();
        },
        
        // 添加轮播
        addCarousel() {
            window.open('/news-carousel-add', '_blank');
        },
        
        // 编辑轮播
        editCarousel(row) {
            window.open('/news-carousel-edit?id=' + row.id, '_blank');
        },
        
        // 删除轮播
        deleteCarousel(row) {
            this.$confirm('确定要删除轮播"' + row.title + '"吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/news-carousel-delete', {
                    id: row.id
                }).then(response => {
                    if (response.data.status === 'success') {
                        this.$message.success('删除成功');
                        this.getCarouselList();
                    } else {
                        this.$message.error(response.data.message);
                    }
                }).catch(error => {
                    console.error('删除轮播错误:', error);
                    this.$message.error('删除失败');
                });
            });
        },
        
        // 更新状态
        updateStatus(row) {
            axios.post('/news-carousel-status', {
                id: row.id,
                status: row.status
            }).then(response => {
                if (response.data.status === 'success') {
                    this.$message.success('状态更新成功');
                } else {
                    this.$message.error(response.data.message);
                    // 恢复原状态
                    row.status = row.status === 1 ? 0 : 1;
                }
            }).catch(error => {
                console.error('更新状态错误:', error);
                this.$message.error('状态更新失败');
                // 恢复原状态
                row.status = row.status === 1 ? 0 : 1;
            });
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.size = val;
            this.getCarouselList();
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.getCarouselList();
        }
    }
});
</script>

<style scoped>
.search-card {
    margin-bottom: 20px;
}

.search-form {
    margin-bottom: 0;
}

.list-card {
    margin-bottom: 20px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.el-header {
    background-color: #f5f5f5;
    line-height: 60px;
    padding: 0 20px;
}

.el-main {
    padding: 20px;
}
</style>

{include file="public/_footer"} 