/* 仪表盘容器 */
.dashboard-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.current-time {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-left: 4px solid;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  height: 120px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 60px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: 400px;
}

.chart-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 12px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 活动和待办区域 */
.activities-section {
  margin-bottom: 24px;
}

.activity-card,
.todo-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: 400px;
}

.card-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 12px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.activity-list,
.todo-list {
  height: 320px;
  overflow-y: auto;
}

.activity-item,
.todo-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.3s ease;
}

.activity-item:hover,
.todo-item:hover {
  background-color: #f5f7fa;
}

.activity-item:last-child,
.todo-item:last-child {
  border-bottom: none;
}

.activity-icon,
.todo-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.activity-icon i,
.todo-icon i {
  font-size: 16px;
  color: #409eff;
}

.activity-content,
.todo-content {
  flex: 1;
  min-width: 0;
}

.activity-title,
.todo-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-time,
.todo-time {
  font-size: 12px;
  color: #909399;
}

.activity-type {
  font-size: 12px;
  color: #409eff;
  background: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-card {
    height: 100px;
    padding: 16px;
  }
  
  .stat-icon {
    font-size: 24px;
    width: 40px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .stat-card {
    margin-bottom: 12px;
  }
  
  .chart-card,
  .activity-card,
  .todo-card {
    margin-bottom: 12px;
  }
}

/* 滚动条样式 */
.activity-list::-webkit-scrollbar,
.todo-list::-webkit-scrollbar {
  width: 6px;
}

.activity-list::-webkit-scrollbar-track,
.todo-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb,
.todo-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover,
.todo-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card,
.chart-card,
.activity-card,
.todo-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 数据变化动画 */
.stat-value {
  transition: all 0.3s ease;
}

.stat-value.updating {
  transform: scale(1.1);
  color: #67c23a;
}

/* 图表容器响应式 */
@media (max-width: 768px) {
  .chart-container {
    height: 250px;
  }
  
  .activity-list,
  .todo-list {
    height: 250px;
  }
} 