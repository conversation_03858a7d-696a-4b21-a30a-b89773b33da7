<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcexpected;
use app\model\Dcperiod;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Dctype;
use app\model\File;
use app\model\Member;
use app\model\Teacher;
use app\validate\CheckAddDc;
use Ramsey\Uuid\Uuid;
use think\facade\Db;

class AddProject extends BaseController
{
    public function addProject($uid='')
    {
        $data=input('post.data');
        if (!$data){
            //这里是打开这个页面
            $types = Dctype::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            $periods = Dcperiod::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            LogExecution('进入大创项目立项页');
            return view('dcmanage/addProject',['types'=>$types,'periods'=>$periods]);

        }else{
            $type='';
            if($uid === 'null'&&$data['uid']){
                $type='update';
                //修改项目
                $uid=$data['uid'];
                $old_project=Dcproject::where('is_delete',0)->where('uid',$data['uid'])->find();
                if (!$old_project){
                    LogExecution('项目不存在');
                    return json(['status'=>'error','message' => '项目不存在']);
                }
                
                // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
                if (session('user.usermode') != 11) {
                    $current_status = $old_project['status'];
                    if ($current_status!=1&&$current_status!=9&&$current_status!=10&&$current_status!=11){
                        LogExecution('当前不是可修改立项信息状态');
                        return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
                    }
                } else {
                    LogExecution('超级管理员可以修改任意状态的项目');
                }

                $class='修改立项';
                $uid=$data['uid'];
            }elseif ($uid&&!isset($data['uid'])){
                $type='add';

                //项目立项
                $class='立项';
                $uid=Uuid::uuid4()->toString();
            }
            //判断数据校验以后写，这里先添加项目信息
            $project=[
                'uid'=>$uid,
                'name'=>$data['name'],
                'time'=>$data['time'],
                'introduction'=>$data['introduction'],
                'class'=>$data['class'],
                'type'=>$data['type'],
                'period'=>$data['period'],
                'status'=>1
            ];
            $time=$project['time'];
            $years=explode('-',$time);
            $year=$years[0];
            $project['year']=$year;
            $member=$data['member'];
            $teacher=$data['teacher'];
            $expected=$data['expected'];
            $expenditure=$data['expenditure'];
            $progress=$data['progress'];
            $files=$data['files'];
            //判断数据校验
            $validate = new CheckAddDc();
            if (!$validate->check($data)) {
                LogExecution('数据校验失败：'.$validate->getError());
                return json(['status'=>'error','message' => $validate->getError()]);
            }
            //开始事务
            Db::startTrans();
            try {
                if ($type=='add'){
                    $project_result=Dcproject::insert($project);
                }else{
                    $project_result=Dcproject::where('uid',$uid)->update($project);
                }
                if (!$project_result){
                    LogExecution($class.'项目信息失败');
                    Db::rollback();
                    return json(['status'=>'error','message' => $class.'项目信息失败']);
                }
                //删除原有的成员信息
                Member::where('uid',$uid)->update(['is_delete'=>1]);
                //添加成员信息
                foreach ($member as $key=>$value){
                    $member_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'rank'=>$key+1,
                        'type'=>0,
                        'is_delete'=>0
                    ];
                    $member_result=Member::insert($member_data);
                    if (!$member_result){
                        LogExecution($class.'成员信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'成员信息失败']);
                    }
                }
                //删除原有的教师信息
                Teacher::where('uid',$uid)->update(['is_delete'=>1]);
                //添加教师信息
                foreach ($teacher as $key=>$value){
                    $teacher_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'rank'=>$key+1,
                        'type'=>0,
                        'is_delete'=>0
                    ];
                    $teacher_result=Teacher::insert($teacher_data);
                    if (!$teacher_result){
                        LogExecution($class.'教师信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'教师信息失败']);
                    }
                }
                //删除原有的预期成果信息
                Dcexpected::where('uid',$uid)->update(['is_delete'=>1]);
                //添加预期成果信息
                foreach ($expected as $key=>$value){
                    $expected_data=[
                        'uid'=>$uid,
                        'name'=>$value['name'],
                        'type'=>$value['type'],
                        'rank'=>$key+1,
                        'is_delete'=>0
                    ];
                    $expected_result=Dcexpected::insert($expected_data);
                    if (!$expected_result){
                        LogExecution($class.'预期成果信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'预期成果信息失败']);
                    }
                }
                //删除原有的经费预算信息
                Dcexpenditure::where('uid',$uid)->update(['is_delete'=>1]);
                //添加经费预算信息
                foreach ($expenditure as $key=>$value){
                    $expenditure_data=[
                        'uid'=>$uid,
                        'name'=>$value['name'],
                        'money'=>$value['money'],
                        'rank'=>$key+1,
                        'is_delete'=>0
                    ];
                    $expenditure_result=Dcexpenditure::insert($expenditure_data);
                    if (!$expenditure_result){
                        LogExecution($class.'经费预算信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'经费预算信息失败']);
                    }
                }
                //删除原有的进度安排信息
                Dcprogress::where('uid',$uid)->update(['is_delete'=>1]);
                //添加进度安排信息
                foreach ($progress as $key=>$value){
                    $progress_data=[
                        'uid'=>$uid,
                        'name'=>$value['name'],
                        'time'=>$value['time'],
                        'rank'=>$key+1,
                        'is_delete'=>0
                    ];
                    $progress_result=Dcprogress::insert($progress_data);
                    if (!$progress_result){
                        LogExecution($class.'进度安排信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'进度安排信息失败']);
                    }
                }
                //删除原有的文件信息
                File::where('uid',$uid)->update(['is_delete'=>1]);
                //添加文件信息
                foreach ($files as $key=>$value){
                    $file_data=[
                        'uid'=>$uid,
                        'path'=>$value['path'],
                        'name'=>$value['name'],
                        'type'=>1,
                        'is_delete'=>0
                    ];
                    $file_result=File::insert($file_data);
                    if (!$file_result){
                        LogExecution($class.'文件信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'文件信息失败']);
                    }
                }
                Db::commit();
                LogExecution($class.'项目成功');
                return json(['status'=>'success','message' => $class.'项目成功']);
            } catch (\Exception $e) {
                Db::rollback();
                LogExecution($class.'项目失败：'.$e->getMessage());
                return json(['status'=>'error','message' => $class.'项目失败']);
            }
        }
    }
    
    public function editProject($uid){
        $data=input('post.data');
        //根据uid查cuid
        $project=Dcproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project){
            LogExecution('项目不存在');
            return json(['status'=>'error','message' => '项目不存在']);
        }
        
        // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
        if (session('user.usermode') != 11) {
            $current_status = $project['status'];
            if ($current_status!=1&&$current_status!=9&&$current_status!=10&&$current_status!=11){
                LogExecution('当前不是可修改立项信息状态');
                return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
            }
        } else {
            LogExecution('超级管理员可以修改任意状态的项目');
        }
        
        if (!$data){
            //这里是打开这个页面
            $types = Dctype::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            $periods = Dcperiod::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            $member=Member::alias('m')
                ->join('user u', 'u.username=m.username','LEFT')
                ->where('m.is_delete',0)
                ->where('u.status',0)
                ->where('m.uid',$uid)
                ->where('m.type',0)
                ->field('u.name as label,m.username as value')
                ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $teacher=Teacher::alias('t')
                ->join('user u', 'u.username=t.username','LEFT')
                ->where('t.is_delete',0)
                ->where('u.status',0)
                ->where('t.uid',$uid)
                ->where('t.type',0)
                ->field('t.username as value,u.name as label')
                ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $expected=Dcexpected::where('is_delete',0)->where('uid',$uid)->order('rank', 'asc')->select();
            $expenditure=Dcexpenditure::where('is_delete',0)->where('uid',$uid)->order('rank', 'asc')->select();
            $progress=Dcprogress::where('is_delete',0)->where('uid',$uid)->order('rank', 'asc')->select();
            $files=File::where('is_delete',0)->where('uid',$uid)->select();
            LogExecution('进入大创项目修改页');
            return view('dcmanage/addProject',[
                'types'=>$types,
                'periods'=>$periods,
                'project'=>$project,
                'members'=>$member,
                'teachers'=>$teacher,
                'expected'=>$expected,
                'expenditure'=>$expenditure,
                'progress'=>$progress,
                'files'=>$files
            ]);
        }
    }
}
