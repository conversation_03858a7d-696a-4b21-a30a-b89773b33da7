{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/yck/apply.css">
<div id="app">
    <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
      <a style="text-decoration: none;color: #00000097;" href="">首页</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="">英才库</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="yck-applylist">申请列表</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="yck-apply">申请</a>
  </p>



  <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="15rem" >
    <el-form-item label="卡号">
      <el-input readonly  value="{$mine.username}"></el-input>
    </el-form-item>
    <el-form-item label="姓名">
        <el-input readonly  value="{$mine.name}"></el-input>
      </el-form-item>
      <el-form-item label="学院">
        <el-input readonly value="{$mine.college}"></el-input>
      </el-form-item>
      <el-form-item label="专业">
        <el-input readonly value="{$mine.major}"></el-input>
      </el-form-item>
      <el-form-item label="电话">
        <el-input readonly value="{$mine.phone}"></el-input>
      </el-form-item>
      <el-form-item label="邮件">
        <el-input readonly value="{$mine.email}"></el-input>
      </el-form-item>
    <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="form.gender">
          <el-radio label="male">男</el-radio>
          <el-radio label="female">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="职称/学历" prop="job">
        <el-select
          v-model="form.job"
          placeholder="请输入职称/学历">
          {foreach $jobs as $job}
          <el-option label="{$job.name}" value="{$job.id}"></el-option>
          {/foreach}
        </el-select>
      </el-form-item>
      <el-form-item label="自我介绍" prop="intro">
        <el-input type="textarea" :rows="3" maxlength="300" show-word-limit v-model="form.intro"></el-input>
      </el-form-item>
      <el-form-item label="擅长领域" prop="domain">
        <el-select
          v-model="form.domain"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入擅长领域">
        </el-select>
      </el-form-item>
      <el-form-item label="个人标签" prop="tag">
        <el-select
          v-model="form.tag"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入个人标签">
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="号码" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入号码"></el-input>
    </el-form-item>
    <el-form-item label="电子邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
    </el-form-item> -->
    <el-form-item label="照片" prop="avatar">
        <el-upload
        class="avatar-uploader"
        drag
        action="bs-addfile?class=yck&type=contact"
        :before-upload="beforeUpload"
        :on-success="handleSuccessavatar"
        :on-remove="removeFileavatar"
        :limit="1" 
        >
        <img v-if="form.avatar" :src="`/static/files/upload/${form.avatar}`" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="微信二维码" prop="wx">
        <el-upload
        class="avatar-uploader"
        drag
        action="bs-addfile?class=yck&type=contact"
        :before-upload="beforeUpload"
        :on-success="handleSuccesswx"
        :on-remove="removeFilewx"
        :limit="1" 
        >
        <img v-if="form.wx" :src="`/static/files/upload/${form.wx}`" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="QQ二维码" prop="qq">
        <el-upload
        class="avatar-uploader"
        drag
        action="bs-addfile?class=yck&type=contact"
        :before-upload="beforeUpload"
        :on-success="handleSuccessqq"
        :on-remove="removeFileqq"
        :limit="1" 
        >
        <img v-if="form.qq" :src="`/static/files/upload/${form.qq}`" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="钉钉二维码" prop="dd">
        <el-upload
        class="avatar-uploader"
        drag
        action="bs-addfile?class=yck&type=contact"
        :before-upload="beforeUpload"
        :on-success="handleSuccessdd"
        :on-remove="removeFiledd"
        :limit="1" 
        >
        <img v-if="form.dd" :src="`/static/files/upload/${form.dd}`" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="企业微信二维码" prop="qywx">
        <el-upload
        class="avatar-uploader"
        drag
        action="bs-addfile?class=yck&type=contact"
        :before-upload="beforeUpload"
        :on-success="handleSuccessqywx"
        :on-remove="removeFileqywx"
        :limit="1" 
        >
        <img v-if="form.qywx" :src="`/static/files/upload/${form.qywx}`" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" :rows="3" maxlength="200" show-word-limit v-model="form.remark"></el-input>
      </el-form-item>
      <el-form-item label="申请大创项目（仅显示结项）" prop="dc">
        <el-transfer v-model="form.dc" :data="dc" :titles="['未选择', '已选择']"></el-transfer>
      </el-form-item>
      <el-form-item label="申请竞赛项目（仅显示结项）" prop="js">
        <el-transfer v-model="form.js" :data="js" :titles="['未选择', '已选择']"></el-transfer>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit('form')">提交</el-button>
        <el-button @click="resetForm('form')">重置</el-button>
      </el-form-item>
  </el-form>
</div> 
<script src="../../static/js/yck/apply.js"></script>
</body>
</html>