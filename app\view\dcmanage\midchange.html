{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/dc/addproject.css">
<style>
  body{
    overflow-x: hidden;
  }
  .form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;
  }
  .form-section h3 {
    margin-top: 0;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 0.5rem;
  }
  .current-members {
    margin-bottom: 1rem;
  }
  .current-members h4 {
    margin-bottom: 0.5rem;
    color: #606266;
  }
  .member-tag {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .word-count {
    margin-top: 0.5rem;
    color: #909399;
    font-size: 0.875rem;
  }
  .submit-section {
    text-align: center;
    margin-top: 2rem;
  }
  .submit-section .el-button {
    margin: 0 0.5rem;
  }
  .change-notice {
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  .change-notice h4 {
    margin-top: 0;
    color: #409eff;
  }
</style>

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">大创平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="dc-lists">项目管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="dc-midchange?uid={$project.uid}">申请中期变更</a>
  </p>

  <div class="container">
    <div class="change-notice">
      <h4>变更说明</h4>
      <p>• 您可以选择性地修改项目成员和指导教师，如果不需要修改请保持为空</p>
      <p>• 变更事项和变更原因必须填写，且至少10个字符</p>
      <p>• 如果更换指导教师，将由新的指导教师进行审核</p>
    </div>

    <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="10rem">
      <!-- 项目信息 -->
      <div class="form-section">
        <h3>项目信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称">
              <el-input v-model="project.name" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目负责人">
              <el-input v-model="project.leader" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目级别">
              <el-input v-model="project.level" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前状态">
              <el-input v-model="statusText" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 当前项目成员和指导教师 -->
      <div class="form-section">
        <h3>当前项目成员和指导教师</h3>
        
        <div class="current-members">
          <h4>当前项目成员</h4>
          <el-tag v-for="member in currentMembers" :key="member.value" class="member-tag" type="success">
            {{member.label}} ({{member.value}})
          </el-tag>
        </div>
        
        <div class="current-members">
          <h4>当前指导教师</h4>
          <el-tag v-for="teacher in currentTeachers" :key="teacher.value" class="member-tag" type="warning">
            {{teacher.label}} ({{teacher.value}})
          </el-tag>
        </div>
      </div>

      <!-- 新成员选择 -->
      <div class="form-section">
        <h3>重新选择项目成员（可选）</h3>
        
        <el-form-item label="项目成员" prop="new_members">
          <el-select 
            v-model="form.new_members" 
            multiple 
            filterable 
            remote 
            reserve-keyword
            placeholder="请输入姓名或学号搜索，顺序选择，最多选择4名成员（不含队长）"
            :remote-method="remoteStudents"
            :loading="loadingStudents"
            style="width: 100%;"
            @change="handleMembersChange">
            <el-option 
              v-for="student in studentOptions" 
              :key="student.value" 
              :label="student.label + ' (' + student.value + ')'" 
              :value="student"
              :disabled="form.new_members.length >= 4 && !form.new_members.find(item => item.value === student.value)">
              <span style="float: left">{{ student.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ student.value }}</span>
            </el-option>
          </el-select>
          <div class="word-count">
            已选择 {{form.new_members.length}} 名成员（最多4名，不含队长）
            <span v-if="form.new_members.length >= 4" style="color: #f56c6c;">已达到最大选择数量</span>
          </div>
        </el-form-item>
      </div>

      <!-- 新教师选择 -->
      <div class="form-section">
        <h3>重新选择指导教师（可选）</h3>
        
        <el-form-item label="指导教师" prop="new_teachers">
          <el-select 
            v-model="form.new_teachers" 
            multiple 
            filterable 
            remote 
            reserve-keyword
            placeholder="请输入姓名或工号搜索，顺序选择，最多选择2名教师"
            :remote-method="remoteTeachers"
            :loading="loadingTeachers"
            style="width: 100%;"
            @change="handleTeachersChange">
            <el-option 
              v-for="teacher in teacherOptions" 
              :key="teacher.value" 
              :label="teacher.label + ' (' + teacher.value + ')'" 
              :value="teacher"
              :disabled="form.new_teachers.length >= 2 && !form.new_teachers.find(item => item.value === teacher.value)">
              <span style="float: left">{{ teacher.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ teacher.value }}</span>
            </el-option>
          </el-select>
          <div class="word-count">
            已选择 {{form.new_teachers.length}} 名教师（最多2名）
            <span v-if="form.new_teachers.length >= 2" style="color: #f56c6c;">已达到最大选择数量</span>
          </div>
        </el-form-item>
      </div>

      <!-- 变更事项 -->
      <div class="form-section">
        <h3>变更申请信息</h3>
        
        <el-form-item label="变更事项" prop="change_items">
          <el-input 
            type="textarea" 
            v-model="form.change_items" 
            placeholder="请详细说明需要变更的事项，包括成员变更、教师变更等具体内容"
            :rows="6"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>

        <el-form-item label="变更原因" prop="change_reason">
          <el-input 
            type="textarea" 
            v-model="form.change_reason" 
            placeholder="请详细说明申请变更的原因，包括项目进展情况、遇到的问题、需要变更的具体原因等"
            :rows="6"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </div>

      <div class="submit-section">
        <el-button type="primary" @click="submitForm" :loading="loading" size="large">
          {{loading ? '提交中...' : '提交申请'}}
        </el-button>
        <el-button @click="goBack" size="large">返回</el-button>
      </div>
    </el-form>
  </div>
</div>

<script>
    new Vue({
        el: '#app',
        data() {
            return {
                project: {$project|json_encode|raw},
                currentMembers: {$current_members|json_encode|raw},
                currentTeachers: {$current_teachers|json_encode|raw},
                midchange: {$midchange|json_encode|raw},
                form: {
                    new_members: {$init_new_members|json_encode|raw},
                    new_teachers: {$init_new_teachers|json_encode|raw},
                    change_items: '',
                    change_reason: ''
                },
                studentOptions: [],
                teacherOptions: [],
                loadingStudents: false,
                loadingTeachers: false,
                rules: {
                    change_items: [
                        { required: true, message: '请填写变更事项', trigger: 'blur' },
                        { min: 10, message: '变更事项至少10个字符', trigger: 'blur' }
                    ],
                    change_reason: [
                        { required: true, message: '请填写变更原因', trigger: 'blur' },
                        { min: 10, message: '变更原因至少10个字符', trigger: 'blur' }
                    ]
                },
                loading: false
            }
        },
        computed: {
            statusText() {
                return this.getStatusText(this.project.status);
            }
        },
        mounted() {
            // 获取uid参数
            const url = window.location.href;
            const urlObj = new URL(url);
            const params = new URLSearchParams(urlObj.search);
            this.project.uid = params.get('uid');
            // 如果有已存在的中期变更申请，填充表单
            if (this.midchange) {
                this.form.change_items = this.midchange.change_items;
                this.form.change_reason = this.midchange.change_reason;
                // 只在为空时赋值，防止覆盖用户操作3
                console.log(this.form.new_members);
                this.studentOptions=this.form.new_members;
                this.teacherOptions=this.form.new_teachers;
                if (!this.form.new_members.length) {
                    this.form.new_members = this.$options.data().form.new_members;
                }
                if (!this.form.new_teachers.length) {
                    this.form.new_teachers = this.$options.data().form.new_teachers;
                }
            }
        },
        methods: {
            getStatusText(status) {
                const statusMap = {
                    4: '已立项待提交中期报告',
                    33: '等待教师中期变更审核',
                    34: '等待学院中期变更审核',
                    35: '等待学校中期变更审核',
                    36: '教师中期变更审核驳回（可重新申请变更或提交中期报告）',
                    37: '学院中期变更审核驳回（可重新申请变更或提交中期报告）',
                    38: '学校中期变更审核驳回（可重新申请变更或提交中期报告）'
                };
                return statusMap[status] || '未知状态';
            },
            // 远程搜索学生
            remoteStudents(query) {
                if (query !== '') {
                    this.loadingStudents = true;
                    axios.post('bs-searchuser?type=1', {
                        query: query
                    })
                    .then(response => {
                        if (response.data.status === 'success') {
                            this.studentOptions = response.data.message;
                        } else {
                            this.studentOptions = [];
                        }
                    })
                    .catch(error => {
                        console.error('搜索学生失败:', error);
                        this.studentOptions = [];
                    })
                    .finally(() => {
                        this.loadingStudents = false;
                    });
                } else {
                    this.studentOptions = [];
                }
            },
            // 远程搜索教师
            remoteTeachers(query) {
                if (query !== '') {
                    this.loadingTeachers = true;
                    axios.post('bs-searchuser?type=2', {
                        query: query
                    })
                    .then(response => {
                        if (response.data.status === 'success') {
                            this.teacherOptions = response.data.message;
                        } else {
                            this.teacherOptions = [];
                        }
                    })
                    .catch(error => {
                        console.error('搜索教师失败:', error);
                        this.teacherOptions = [];
                    })
                    .finally(() => {
                        this.loadingTeachers = false;
                    });
                } else {
                    this.teacherOptions = [];
                }
            },
            // 处理成员选择变化
            handleMembersChange(value) {
                if (value.length > 4) {
                    this.$message.warning('成员最多只能选择4名（不含队长）');
                    this.form.new_members = value.slice(0, 4);
                }
            },
            // 处理教师选择变化
            handleTeachersChange(value) {
                if (value.length > 2) {
                    this.$message.warning('教师最多只能选择2名');
                    this.form.new_teachers = value.slice(0, 2);
                }
            },
            // 验证是否有修改
            hasChanges() {
                return this.form.new_members.length > 0 || 
                       this.form.new_teachers.length > 0 || 
                       this.form.change_items.trim() !== '' || 
                       this.form.change_reason.trim() !== '';
            },
            submitForm() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        // 检查是否有任何修改
                        if (!this.hasChanges()) {
                            this.$message.warning('请至少填写一项变更内容');
                            return;
                        }
                        
                        this.loading = true;
                        
                        axios.post('dc-applymidchange?uid=' + this.project.uid, {
                            data: this.form
                        })
                        .then(response => {
                            if (response.data.status === 'success') {
                                this.$message.success(response.data.message);
                                setTimeout(() => {
                                    this.goBack();
                                }, 1500);
                            } else {
                                this.$message.error(response.data.message);
                            }
                        })
                        .catch(error => {
                            console.error('提交失败:', error);
                            this.$message.error('提交失败，请重试');
                        })
                        .finally(() => {
                            this.loading = false;
                        });
                    } else {
                        this.$message.warning('请完善表单信息');
                    }
                });
            },
            goBack() {
                window.history.back();
            }
        }
    });
</script>
</body>
</html> 