// 官网首页 JavaScript
window.app = new Vue({
    el: '#app',
    data() {
        return {
            // 登录相关
            dialogVisible: false,
            loading: false,
            login: {
                username: '',
                password: ''
            },
            rules: {
                username: [
                    { required: true, message: '请输入账号', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' }
                ]
            },
            // 英才库相关
            yck: {
                active: '英才学生'
            },
            talents: [],
            talentIndex: 0, // 当前显示的英才索引
            talentTimer: null, // 定时器
            // 新闻相关
            newsList: [],
            // 轮播相关
            carouselList: [] // ← 确保声明
        }
    },
    created() {
        this.fetchTalents();
        this.fetchNews();
        this.fetchCarousel(); // ← 页面加载自动获取轮播数据
        this.startTalentRotation();
    },
    beforeDestroy() {
        this.stopTalentRotation();
    },
    methods: {
        // 登录相关方法
        openDialog() {
            this.dialogVisible = true;
        },
        submit_login() {
            this.$refs.login.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    axios.post('/login', this.login)
                        .then(response => {
                            if (response.data.status === 'success') {
                                this.$message.success('登录成功');
                                this.dialogVisible = false;
                                window.location.reload();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        })
                        .catch(error => {
                            this.$message.error('登录失败，请重试');
                            console.error('登录错误:', error);
                        })
                        .finally(() => {
                            this.loading = false;
                        });
                }
            });
        },
        // 英才库相关方法
        fetchTalents() {
            axios.get('/yck-homepage-talents', {
                params: {
                    type: this.yck.active,
                    limit: 10
                }
            }).then(response => {
                if (response.data.status === 'success') {
                    this.talents = response.data.message.data.slice(0, 10).map(item => ({
                        name: item.name || '未知',
                        college: item.department || '未知学院',
                        score: item.mark || 0,
                        major: item.major_name || '',
                        tags: item.tags || ''
                    }));
                    if (this.talents.length === 0) {
                        this.talents = [{
                            name: '暂无数据',
                            college: '暂无',
                            score: 0,
                            major: '',
                            tags: ''
                        }];
                    }
                } else {
                    this.$message.error('获取英才库数据失败');
                    this.talents = [{
                        name: '数据加载失败',
                        college: '请稍后重试',
                        score: 0,
                        major: '',
                        tags: ''
                    }];
                }
            }).catch(error => {
                console.error('获取英才库数据错误:', error);
                this.$message.error('获取英才库数据失败');
                this.talents = [{
                    name: '网络错误',
                    college: '请检查网络连接',
                    score: 0,
                    major: '',
                    tags: ''
                }];
            });
        },
        // 开始英才轮播
        startTalentRotation() {
            if (this.talents.length > 1) {
                this.talentTimer = setInterval(() => {
                    this.talentIndex = (this.talentIndex + 1) % this.talents.length;
                }, 3000);
            }
        },
        // 停止英才轮播
        stopTalentRotation() {
            if (this.talentTimer) {
                clearInterval(this.talentTimer);
                this.talentTimer = null;
            }
        },
        // 英才类型切换
        onTalentTypeChange() {
            this.talentIndex = 0;
            this.fetchTalents();
        },
        // 获取当前显示的英才数据
        getCurrentTalent() {
            if (this.talents.length === 0) {
                return {
                    name: '暂无数据',
                    college: '暂无',
                    score: 0,
                    major: '',
                    tags: ''
                };
            }
            return this.talents[this.talentIndex];
        },
        // 新闻相关方法
        fetchNews() {
            axios.post('/bs-searchindex')
                .then(response => {
                    if (response.data.status === 'success') {
                        this.newsList = response.data.message.news;
                    } else {
                        this.$message.error('获取新闻数据失败');
                    }
                })
                .catch(error => {
                    console.error('获取新闻数据错误:', error);
                    this.$message.error('获取新闻数据失败');
                });
        },
        // 轮播相关方法
        fetchCarousel() {
            axios.get('/news-homepage-carousel')
                .then(response => {
                    if (response.data.status === 'success') {
                        this.carouselList = response.data.message.data;
                        if (this.carouselList.length === 0) {
                            this.carouselList = [{
                                id: 1,
                                title: '欢迎访问',
                                image_path: 'static/image/1.jpg',
                                news_id: null,
                                news_title: '创新创业管理系统'
                            }];
                        }
                    } else {
                        this.$message.error('获取轮播数据失败');
                        this.carouselList = [{
                            id: 1,
                            title: '欢迎访问',
                            image_path: 'static/image/1.jpg',
                            news_id: null,
                            news_title: '创新创业管理系统'
                        }];
                    }
                })
                .catch(error => {
                    this.$message.error('获取轮播数据失败');
                    this.carouselList = [{
                        id: 1,
                        title: '欢迎访问',
                        image_path: 'static/image/1.jpg',
                        news_id: null,
                        news_title: '创新创业管理系统'
                    }];
                });
        },
        // 跳转到新闻详情
        goToNews(newsId) {
            if (newsId) {
                window.open('/news-detail?news=' + newsId, '_blank');
            }
        }
    },
    watch: {
        'yck.active': function() {
            this.onTalentTypeChange();
        }
    }
}); 