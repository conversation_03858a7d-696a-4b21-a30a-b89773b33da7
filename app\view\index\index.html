{include file="public_index/_header"}
<link rel="stylesheet" href="../../static/css/index/index.css">
<div id="app">


        <!-- 登录对话框 -->
        <el-dialog
        title="欢迎登录"
        :visible.sync="dialogVisible"
        width="30%"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :show-close="false"
        append-to-body
        custom-class="login-dialog">
        <el-form :rules="rules" hide-required-asterisk:true :model="login" ref="login">
          <el-form-item label="" prop="username">
              <el-input
                      placeholder="请输入账号"
                      v-model="login.username"
                      clearable
                      style="flex: 1;margin-top:1rem"
                      prefix-icon="el-icon-user"
                      class="lgoin">
              </el-input>
          </el-form-item>
          <el-form-item label="" prop="password">
              <el-input
                      type='password'
                      show-password
                      placeholder="请输入密码"
                      v-model="login.password"
                      prefix-icon="el-icon-lock"
                      clearable
                      style="flex: 1;margin-top:1rem"
                      class="lgoin"
              >
              </el-input>
          </el-form-item>
        </el-form>
      
      
          <!-- 登录按钮 -->
          <el-button 
            type="primary" 
            class="login-btn"
            @click="submit_login"
            style="width: 100%;"
            :loading="loading">
            登 录
          </el-button>
        </el-form>
      </el-dialog>


    <!-- 主体内容 -->
    <main class="main-content flex-full">
        <!-- 轮播+英才 -->
        <el-row  :gutter="20" class="flex-full" style="height: 100%;">
            <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16" class="flex-full" style="height: 100%;">
              <el-carousel 
                style="height: 100%;" 
                class="banner-box"
                :interval="5000"
                :autoplay="true"
                indicator-position="outside">
                <el-carousel-item 
                  v-for="carousel in carouselList" 
                  :key="carousel.id"
                  style="height: 100%;">
                  <div 
                    class="carousel-img-wrap"
                    @click="goToNews(carousel.news_id)">
                    <img 
                      :src="carousel.image_path.startsWith('/') ? carousel.image_path : '/' + carousel.image_path" 
                      :alt="carousel.title"
                      class="carousel-img">
                    <div class="carousel-overlay">
                      <h3>{{ carousel.title }}</h3>
                      <p>{{ carousel.news_title }}</p>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </el-col>
            
            <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8" class="flex-full" style="height: 100%;">
                <div class="talent-box" style="height: 100%;" >
                    <h3 class="section-title">
                      英才库
                      <el-radio-group v-model="yck.active">
                        <el-radio-button label="英才学生"></el-radio-button>
                        <el-radio-button label="精英教师"></el-radio-button>
                      </el-radio-group>
                      <el-link 
                        class="more-link" 
                        :underline="false"
                        @click="goToTalentList"
                      >
                        查看更多
                        <i class="el-icon-arrow-right"></i>
                      </el-link>
                    </h3>
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                      <el-button icon="el-icon-arrow-up" size="mini" @click="prevTalent" style="margin-right: 4px;"></el-button>
                      <el-button icon="el-icon-arrow-down" size="mini" @click="nextTalent"></el-button>
                    </div>
                    <el-table
                        :data="talents"
                        style="width: 100%;overflow: auto;"
                        stripe
                        :row-class-name="row => (row.$index === talentIndex ? 'talent-highlight' : '')"
                        @mouseenter.native="onTalentMouseEnter"
                        @mouseleave.native="onTalentMouseLeave"
                        @row-click="goToTalentDetail"
                    >
                        <el-table-column prop="name" label="姓名" min-width="100"></el-table-column>
                        <el-table-column prop="college" label="学院" min-width="100"></el-table-column>
                        <el-table-column prop="domain" label="擅长领域" min-width="100"></el-table-column>
                        <el-table-column prop="score" label="评分" min-width="100">
                            <template slot-scope="scope">
                              <el-rate disabled v-model="scope.row.score"></el-rate>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" min-width="80">
                            <template slot-scope="scope">
                              <el-button type="text" size="mini" @click="goToTalentDetail(scope.row)">简历</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
        </el-row>

        <!-- 新闻板块 -->

      <el-row :gutter="20"  style="width: 100%;margin: auto;margin-top: 2rem;">
        <el-col 
          :xs="24" 
          :sm="12" 
          :md="8" 
          :lg="8" 
          :xl="4" 
          v-for="(section, sectionIndex) in newsList" 
          :key="sectionIndex"
        >
          <div class="news-card">
            <div class="card-header">
              <h3 class="section-title">
                {{section.name}}
                <el-link 
                  class="more-link" 
                  :underline="false"
                  :href="'/news-class?class=' + section.class"
                  target="_blank"

                >
                  查看更多
                  <i class="el-icon-arrow-right"></i>
                </el-link>
              </h3>
            </div>
       
            <div class="card-content">

              <div 
                class="news-item"
                v-for="(news, newsIndex) in section.news"
                :key="newsIndex"
              >

                <div class="date-box">
                  <div class="date-day">{{ news.date.split(' ')[0].split('-')[2] }}</div>
                  <div class="date-month">{{ news.date.split('-')[0] }}年{{ news.date.split('-')[1] }}月</div>
                </div>
                <el-link 
                class="more-link" 
                :underline="false"
                :href="'/news-detail?news=' + news.id"
                target="_blank"
  
              >
                <div class="news-info">
                  <h4 class="news-title">{{ news.title }}</h4>
                </div>
              </el-link>
              </div>
              
            </div>
          </div>
        </el-col>
      </el-row>
       
<!-- 新闻板块 -->
    </main>

  </div>
    <script src="../../static/js/index/index.js"></script>
    <script>
      {if !session('user.username')}
    app.openDialog();
    {/if}
    </script>
    {include file="public_index/_footer"}
