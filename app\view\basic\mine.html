{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/basic/mine.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/bs-mine">个人信息</a>
</p>

<div class="personal-info-container">
  <el-card class="info-card">
    <div slot="header" class="card-header">
      <span class="header-title">个人信息</span>
      <el-button 
        type="primary" 
        class="edit-btn" 
        @click="showEditDialog = true"
        icon="el-icon-edit"
      >
        修改信息
      </el-button>
    </div>
 
    <el-descriptions :column="1" border class="info-list">
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user-solid"></i>
          卡号
        </template>
        {{ mine.username }}
      </el-descriptions-item>
      
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-s-custom"></i>
          姓名
        </template>
        {{ mine.name }}
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-phone"></i>
          电话
        </template>
        {{ mine.phone }}
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-message"></i>
          邮箱
        </template>
        {{ mine.email }}
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-notebook-2"></i>
          学历/职称
        </template>
        {{ mine.job }}
      </el-descriptions-item>
 
      <el-descriptions-item v-if="mine.grade">
        <template slot="label">
          <i class="el-icon-school"></i>
          年级
        </template>
        {{ mine.grade }}
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-s-management"></i>
          用户组
        </template>
        {{ mine.m_name }}
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-office-building"></i>
          部门
        </template>
        <el-tag>{{ mine.department }}</el-tag>
      </el-descriptions-item>
 
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-menu"></i>
          专业
        </template>
        <el-tag>{{ mine.major }}</el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </el-card>
 
  <!-- 修改信息对话框 -->
  <el-dialog
    title="修改个人信息"
    :visible.sync="showEditDialog"
    width="500px"
    @closed="resetForm"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="editForm" 
      label-width="100px"
    >
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
      </el-form-item>
 
      <el-form-item label="电子邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
      </el-form-item>
 
      <el-divider>修改密码（留空则不修改）</el-divider>
 
      <el-form-item label="原密码" prop="oldPassword">
        <el-input 
          v-model="form.oldPassword" 
          placeholder="请输入原密码"
          show-password
        ></el-input>
      </el-form-item>
 
      <el-form-item label="新密码" prop="newPassword">
        <el-input 
          v-model="form.newPassword" 
          placeholder="请输入新密码"
          show-password
        ></el-input>
      </el-form-item>
 
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input 
          v-model="form.confirmPassword" 
          placeholder="请再次输入新密码"
          show-password
        ></el-input>
      </el-form-item>
    </el-form>
 
    <span slot="footer" class="dialog-footer">
      <el-button @click="showEditDialog = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确认修改</el-button>
    </span>
  </el-dialog>
</div>

      
</div>
<script src="../../static/js/basic/mine.js"></script>
</body>
</html>