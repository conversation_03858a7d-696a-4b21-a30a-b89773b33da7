<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\Dcproject;
use app\model\Department;
use app\model\Yckstatus;
use app\model\Yckuser;

class Applylist extends BaseController
{
    public function index()
    {
        LogExecution('进入英才库申请列表');
        $search=[
            'department'=>Department::where('is_delete',0)->select(),
            'status'=>Yckstatus::where('is_delete',0)->select(),
        ];
        return view('yckmanage/applylist',['search'=>$search]);
    }
    public function select_apply(){
        $search=input('post.search');
        $where=[];
        $where[]=['y.is_delete','=',0];
        $where[]=['u.status','=',0];

        if (session('user.usermode')!=7){
            $where[]=['yu.username','=',session('user.username')];
        }
        if ($search['user']!=''){
            $where[]=['yu.username','=',$search['user']];
        }
        if ($search['department']!='all'){
            $where[]=['u.college','=',$search['department']];
        }
        if ($search['status']!='all'){
            $where[]=['y.status','=',$search['status']];
        }
//        if ($search['texts']){
//            foreach ($search['texts'] as $text){
//                $where[]=['r.room_number|r.name|r.area|r.nature|r.management_unit|r.manager|r.safety_manager|r.remarks|r.use|r.phone1|r.phone','like','%'.$text.'%'];
//            }
//        }
//        if ($search['building_id']!='all'){
//            $where[]=['r.building_id','=',$search['building_id']];
//        }
//        if (session('user.level')==2){
//            $where[]=['r.building_id','=',session('user.building_id')];
//        }
        $data = Yckuser::alias('yu')
            ->field('
               yu.username,
               u.name,
               d.name as department,
               m.name as major,
               j.name as job,
                s.name as status
               
                   
               ')
            ->join('user u', 'u.username = yu.username', 'LEFT')
            ->join('yckuser y','y.username=u.username','LEFT')
            ->join('yckjob j','y.job=j.id','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->join('yckstatus s','s.id=y.status','LEFT')
            ->where($where)
            ->select();
        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($data),
            'data'=>$data
        ]]);
    }
}