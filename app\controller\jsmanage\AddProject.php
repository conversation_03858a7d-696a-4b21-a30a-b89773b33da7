<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\File;
use app\model\Jscompetition;
use app\model\Jslevel;
use app\model\Jsproject;
use app\model\Jssetting;
use app\model\Member;
use app\model\Teacher;
use app\validate\CheckAddDc;
use Exception;
use Ramsey\Uuid\Uuid;
use think\facade\Db;

class AddProject extends BaseController
{
    public function editProject($uid){
        $data=input('post.data');
        //根据uid查cuid
        $project=Jsproject::alias('p')
            ->join('jscompetition c','c.cuid=p.cuid','LEFT')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->where('p.is_delete',0)
            ->where('p.uid',$uid)
            ->field('p.*,c.name as c_name,l.name as l_level')
            ->find();
        if (!$project){
            LogExecution('项目不存在');
            return json(['status'=>'error','message' => '项目不存在']);
        }
        
        // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
        if (session('user.usermode') != 11) {
            $current_status = $project['status'];
            if ($current_status!=1&&$current_status!=9&&$current_status!=10&&$current_status!=11){
                LogExecution('当前不是可修改立项信息状态');
                return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
            }
        } else {
            LogExecution('超级管理员可以修改任意状态的项目');
        }
        
        if (!$data){
            //这里是打开这个页面
            $member=Member::alias('m')
                ->join('user u', 'u.username=m.username','LEFT')
                ->where('m.is_delete',0)
                ->where('u.status',0)
                ->where('m.uid',$uid)
                ->where('m.rank','!=',1)
                ->field('u.name as label,m.username as value')
                ->join('department d','d.id=u.college','LEFT')
                ->join('major mj','mj.id=u.major','LEFT')
                ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $teacher=Teacher::alias('t')
                ->join('user u', 'u.username=t.username','LEFT')
                ->where('t.is_delete',0)
                ->where('u.status',0)
                ->where('t.uid',$uid)
                ->where('t.type',0)
                ->field('t.username as value,u.name as label')
                ->join('department d','d.id=u.college','LEFT')
                ->join('major m','m.id=u.major','LEFT')
                ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $outstudent=Member::alias('m')
                ->where('m.is_delete',0)
                ->where('m.uid',$uid)
                ->where('m.type',1)
                ->field('m.name as label,m.username as value,m.unit,m.phone,m.email')
                ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $outteacher=Teacher::alias('t')
                ->where('t.is_delete',0)
                ->where('t.uid',$uid)
                ->where('t.type',1)
                ->field('t.name as label,t.username as value,t.unit,t.job,t.phone,t.email')
                ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                ->select();
            $files=File::where('is_delete',0)->where('uid',$uid)->select();
            LogExecution('进入竞赛项目修改页');
            return view('jsmanage/addProject',[
                'project'=>$project,
                'members'=>$member,
                'teachers'=>$teacher,
                'outstudent'=>$outstudent,
                'outteacher'=>$outteacher,
                'files'=>$files
            ]);

        }else{
            $type='';
            if($data['uid']){
                $type='update';
                //修改项目
                $uid=$data['uid'];
                $old_project=Jsproject::where('is_delete',0)->where('uid',$data['uid'])->find();
                if (!$old_project){
                    LogExecution('项目不存在');
                    return json(['status'=>'error','message' => '项目不存在']);
                }
                
                // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
                if (session('user.usermode') != 11) {
                    $current_status = $old_project['status'];
                    if ($current_status!=1&&$current_status!=9&&$current_status!=10&&$current_status!=11){
                        LogExecution('当前不是可修改立项信息状态');
                        return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
                    }
                } else {
                    LogExecution('超级管理员可以修改任意状态的项目');
                }

                $class='修改立项';
                $uid=$data['uid'];
            }
            //判断数据校验以后写，这里先添加竞赛信息
            $project=[
                'uid'=>$uid,
                'cuid'=>$data['cuid'],
                'name'=>$data['name'],
                'time'=>$data['time'],
                'introduction'=>$data['introduction'],
                'class'=>$data['class'],
                'status'=>1
            ];
            $time=$project['time'];
            $years=explode('-',$time);
            $year=$years[0];
            $project['year']=$year;
            $member=$data['member'];
            $teacher=$data['teacher'];
            $outstudent=$data['outstudent'];
            $outteacher=$data['outteacher'];
            $files=$data['files'];
            //判断数据校验
            $validate = new CheckAddDc();
            if (!$validate->check($data)) {
                LogExecution('数据校验失败：'.$validate->getError());
                return json(['status'=>'error','message' => $validate->getError()]);
            }
            //开始事务
            Db::startTrans();
            try {
                if ($type=='add'){
                    $project_result=Jsproject::insert($project);
                }else{
                    $project_result=Jsproject::where('uid',$uid)->update($project);
                }
                if (!$project_result){
                    LogExecution($class.'项目信息失败');
                    Db::rollback();
                    return json(['status'=>'error','message' => $class.'项目信息失败']);
                }
                //删除原有的成员信息
                Member::where('uid',$uid)->update(['is_delete'=>1]);
                //添加成员信息
                foreach ($member as $key=>$value){
                    $member_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'rank'=>$key+2,
                        'type'=>0,
                        'is_delete'=>0
                    ];
                    $member_result=Member::insert($member_data);
                    if (!$member_result){
                        LogExecution($class.'成员信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'成员信息失败']);
                    }
                }
                //添加校外成员信息
                foreach ($outstudent as $key=>$value){
                    $member_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'name'=>$value['label'],
                        'unit'=>$value['unit'],
                        'phone'=>$value['phone'],
                        'email'=>$value['email'],
                        'rank'=>$key+2+count($member),
                        'type'=>1,
                        'is_delete'=>0
                    ];
                    $member_result=Member::insert($member_data);
                    if (!$member_result){
                        LogExecution($class.'校外成员信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'校外成员信息失败']);
                    }
                }
                //删除原有的教师信息
                Teacher::where('uid',$uid)->update(['is_delete'=>1]);
                //添加教师信息
                foreach ($teacher as $key=>$value){
                    $teacher_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'rank'=>$key+1,
                        'type'=>0,
                        'is_delete'=>0
                    ];
                    $teacher_result=Teacher::insert($teacher_data);
                    if (!$teacher_result){
                        LogExecution($class.'教师信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'教师信息失败']);
                    }
                }
                //添加校外教师信息
                foreach ($outteacher as $key=>$value){
                    $teacher_data=[
                        'uid'=>$uid,
                        'username'=>$value['value'],
                        'name'=>$value['label'],
                        'unit'=>$value['unit'],
                        'job'=>$value['job'],
                        'phone'=>$value['phone'],
                        'email'=>$value['email'],
                        'rank'=>$key+1+count($teacher),
                        'type'=>1,
                        'is_delete'=>0
                    ];
                    $teacher_result=Teacher::insert($teacher_data);
                    if (!$teacher_result){
                        LogExecution($class.'校外教师信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'校外教师信息失败']);
                    }
                }
                //删除原有的文件信息
                File::where('uid',$uid)->update(['is_delete'=>1]);
                //添加文件信息
                foreach ($files as $key=>$value){
                    $file_data=[
                        'uid'=>$uid,
                        'path'=>$value['path'],
                        'name'=>$value['name'],
                        'type'=>1,
                        'is_delete'=>0
                    ];
                    $file_result=File::insert($file_data);
                    if (!$file_result){
                        LogExecution($class.'文件信息失败');
                        Db::rollback();
                        return json(['status'=>'error','message' => $class.'文件信息失败']);
                    }
                }
                Db::commit();
                LogExecution($class.'项目成功');
                return json(['status'=>'success','message' => $class.'项目成功']);
            } catch (Exception $e) {
                Db::rollback();
                LogExecution($class.'项目失败：'.$e->getMessage());
                return json(['status'=>'error','message' => $class.'项目失败']);
            }
        }
    }
}
