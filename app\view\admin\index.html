{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/admin.css">
   
  <div id="app" >
    <!-- 左侧导航 -->

    <el-menu
    :default-active="currentFrameSrc"
    class="el-menu-vertical-demo left-nav"
    :collapse="isCollapsed"
    background-color="#3498db"
    text-color="#ffffff"
    active-text-color="#ffffff"
    @open="handleOpen"
    @close="handleClose">
    
    <div class="logo-area" style="height: 10vh;" >

      <div v-if="!isCollapsed" style="margin-right: 1rem;" class="logo-text">沈阳师范大学大创中心</div>
      <div class="collapse-btn" @click="toggleCollapse">
        <i style="color: #fff;" class="el-icon-s-fold" :class="{ 'is-active': isCollapsed }"></i>
      </div>
    </div>
        <el-menu-item index="home" @click="logAndNavigate('/dashboard')">
          <i style="color: #fff;"  class="el-icon-s-home"></i>
          <span slot="title">首页</span>
        </el-menu-item>
        <el-menu-item index="mine" @click="logAndNavigate('/bs-mine')">
          <i style="color: #fff;"  class="el-icon-user-solid"></i>
          <span slot="title">个人信息</span>
        </el-menu-item>
        <el-submenu index="dc" v-if="menuPermissions.dc">
          <template slot="title">
            <i style="color: #fff;" class="el-icon-document-add"></i>
            <span>大创平台</span>
          </template>
          <el-menu-item index="dc-addproject" @click="logAndNavigate('dc-addproject')" v-if="canAddProject">
            <i style="color: #fff;" class="el-icon-folder-add"></i>
            <span>项目立项</span>
          </el-menu-item>
          <el-menu-item index="dc-lists" @click="logAndNavigate('dc-lists')">
            <i style="color: #fff;" class="el-icon-files"></i>
            <span>项目管理</span>
          </el-menu-item>
        </el-submenu>

        <el-submenu index="js" v-if="menuPermissions.js">
          <template slot="title">
            <i style="color: #fff;" class="el-icon-trophy"></i>
            <span>竞赛平台</span>
          </template>
          <el-menu-item index="js-competition" @click="logAndNavigate('js-competition')" v-if="canViewCompetition">
            <i style="color: #fff;" class="el-icon-medal"></i>
            <span>竞赛管理</span>
          </el-menu-item>
          <el-menu-item index="js-project" @click="logAndNavigate('js-project')">
            <i style="color: #fff;" class="el-icon-notebook-2"></i>
            <span>项目管理</span>
          </el-menu-item>
        </el-submenu>

        <el-submenu index="yck" v-if="menuPermissions.yck">
          <template slot="title">
            <i style="color: #fff;" class="el-icon-star-on"></i>
            <span>英才库</span>
          </template>
          <el-menu-item index="yck-apply" @click="logAndNavigate('yck-apply')" v-if="canApplyYck">
            <i style="color: #fff;" class="el-icon-edit"></i>
            <span>英才申请</span>
          </el-menu-item>
          <el-menu-item index="yck-lists" @click="logAndNavigate('yck-lists')">
            <i style="color: #fff;" class="el-icon-user"></i>
            <span>英才列表</span>
          </el-menu-item>
          <el-menu-item index="yck-applylist" @click="logAndNavigate('yck-applylist')" v-if="canCheckYck">
            <i style="color: #fff;" class="el-icon-s-order"></i>
            <span>申请列表</span>
          </el-menu-item>
          <el-menu-item index="yck-curriculum" @click="logAndNavigate('yck-curriculum?username={:session('user.username')}')" v-if="canViewYckCurriculum">
            <i style="color: #fff;" class="el-icon-document"></i>
            <span>我的简历</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="news" v-if="menuPermissions.news">
          <template slot="title">
            <i style="color: #fff;" class="el-icon-s-comment"></i>
            <span>新闻</span>
          </template>
          <el-menu-item index="news-carousel" @click="logAndNavigate('/news-carousel')" v-if="canManageCarousel">
            <i style="color: #fff;" class="el-icon-picture"></i>
            <span>轮播管理</span>
          </el-menu-item>
          <el-menu-item index="news-classes" @click="logAndNavigate('/news-classes')" v-if="canManageNews">
            <i style="color: #fff;" class="el-icon-s-management"></i>
            <span>板块管理</span>
          </el-menu-item>
          <el-menu-item index="news-lists" @click="logAndNavigate('/news-lists')">
            <i style="color: #fff;" class="el-icon-s-marketing"></i>
            <span>新闻管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="manage" v-if="menuPermissions.manage">
          <template slot="title">
            <i style="color: #fff;" class="el-icon-s-tools"></i>
            <span>管理</span>
          </template>
          <el-menu-item index="bs-users" @click="logAndNavigate('/bs-users')" v-if="canManageUsers">
            <i style="color: #fff;" class="el-icon-s-custom"></i>
            <span>成员管理</span>
          </el-menu-item>
          <el-menu-item index="department" @click="logAndNavigate('/bs-department')" v-if="canManageDepartments">
            <i style="color: #fff;" class="el-icon-office-building"></i>
            <span>部门管理</span>
          </el-menu-item>
          <el-menu-item index="yc_teacher" @click="logAndNavigate('/yc_teacher.html')" v-if="canManageSystem">
            <i style="color: #fff;" class="el-icon-setting"></i>
            <span>系统设置</span>
          </el-menu-item>
        </el-submenu>
      </el-menu>

    <!-- 右侧内容 -->
    <div class="right-content">
      <div class="top-bar" style="height: 6vh;">
        <div class="user-info">

          <div class="user-detail">
            <div class="user-name">{$user.usermode_text}：{$user.name}</div>
          </div>
        </div>
        
        <div class="top-btns">
          <el-button 
            class="front-btn"
            type="text"
            icon="el-icon-s-promotion"
            @click="navigateToFront"
          >前台</el-button>
          <el-button 
            class="logout-btn"
            type="text"
            icon="el-icon-switch-button"
            @click="logout"
          >退出登录</el-button>
        </div>
      </div>

      <div class="content-area">
        <transition name="fade-slide" mode="out-in">
          <iframe 
          style="padding: 1rem !important;box-sizing: border-box;"
            :key="currentFrameSrc" 
            :src="currentFrameSrc" 
            frameborder="0"
            class="main-iframe"
          ></iframe>
        </transition>
      </div>
    </div>
  </div>
<script>
// 传递用户信息给JavaScript
window.userinfo = {
  username: '{$user.username}',
  name: '{$user.name}',
  usermode: {$user.usermode},
  college: '{$user.college}'
};
</script>
<script src="../../static/js/admin.js"></script>
{include file="public/_footer"}
