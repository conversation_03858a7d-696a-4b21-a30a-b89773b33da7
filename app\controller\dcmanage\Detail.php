<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcachievement;
use app\model\Dccheck;
use app\model\Dcconclude;
use app\model\Dcexpected;
use app\model\Dcextension;
use app\model\Dcmidchange;
use app\model\Dcexpenditure;
use app\model\Dcinterm;
use app\model\Dcintermachievement;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\File;
use app\model\Member;
use app\model\Teacher;

class Detail extends BaseController
{
    public function index($uid){
        LogExecution('查看'.$uid.'项目详情');
        
        // 检查用户是否有权限查看该项目
        if (!$this->checkPermission('dc_view') || !$this->canViewProject($uid, 'dc')) {
            LogExecution('用户尝试查看无权限的项目：' . $uid);
            return json(['status' => 'error', 'message' => '权限不足']);
        }
        
        session('uid',$uid);
        $project=
            Dcproject::alias('p')
                ->join('dcstatus s', 's.id = p.status', 'LEFT')
                ->join('dctype t', 't.id = p.type', 'LEFT')
                ->join('dcperiod pe', 'pe.id = p.period', 'LEFT')
                ->join('dclevel l', 'l.id = p.level', 'LEFT')
                ->where('p.is_delete',0)
                ->where('p.uid',$uid)
                ->field('p.*, s.name as status_name, t.name as type_name, pe.name as period_name, l.name as level_name')
                ->find();
        $expected=Dcexpected::where('is_delete',0)->where('uid',$uid)->find();
        $member=Member::alias('m')
            ->join('user u', 'u.username=m.username','LEFT')
            ->join('department d', 'd.id=u.college','LEFT')
            ->join('major ma', 'ma.id=u.major','LEFT')
            ->where('m.is_delete',0)
            ->where('u.status',0)
            ->where('m.uid',$uid)
            ->field('m.username,m.rank,u.name,d.name as college_name,ma.name as major_name,u.phone,u.email')
            ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $teacher=Teacher::alias('t')
            ->join('user u', 'u.username=t.username','LEFT')
            ->join('department d', 'd.id=u.college','LEFT')
            ->join('major ma', 'ma.id=u.major','LEFT')
            ->where('t.is_delete',0)
            ->where('u.status',0)
            ->where('t.uid',$uid)
            ->where('t.type',0)
            ->field('t.username,t.rank,u.name,d.name as college_name,ma.name as major_name,u.phone,u.email,u.job')
            ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $outteacher=Teacher::alias('t')
            ->where('t.is_delete',0)
            ->where('t.uid',$uid)
            ->where('t.type',1)
            ->field('t.name,t.unit,t.job,t.phone,t.email')
            ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $file1=File::where('is_delete',0)->where('uid',$uid)->where('type',1)->find();
        //查找各级用户审核意见
        $checks=[
            //立项
            'status1'=>[
                'teacher'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',1)->select(),
                'college'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',2)->select(),
                'school'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',3)->select(),
            ],
            //中期
            'status2'=>[
                'teacher'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',2)->where('type',1)->select(),
                'college'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',2)->where('type',2)->select(),
                'school'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',2)->where('type',3)->select(),
            ],
            //结项
            'status3'=>[
                'teacher'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',3)->where('type',1)->select(),
                'college'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',3)->where('type',2)->select(),
                'school'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',3)->where('type',3)->select(),
            ],
            //延期
            'status4'=>[
                'teacher'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',4)->where('type',1)->select(),
                'college'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',4)->where('type',2)->select(),
                'school'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',4)->where('type',3)->select(),
            ],
            //变更
            'status5'=>[
                'teacher'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',5)->where('type',1)->select(),
                'college'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',5)->where('type',2)->select(),
                'school'=>Dccheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',5)->where('type',3)->select(),
            ],

        ];
        $progesses=Dcprogress::where('is_delete',0)->where('uid',$uid)->order('created_at', 'asc')->select();
        $interm=[
            'interm'=>Dcinterm::where('is_delete',0)->where('uid',$uid)->find(),
            'achievement'=>Dcintermachievement::where('is_delete',0)->where('uid',$uid)->order('id', 'asc')->select(),
            'expenditure'=>Dcexpenditure::where('is_delete',0)->where('type',1)->where('uid',$uid)->order('id', 'asc')->select(),
        ];
        $achievements=Dcachievement::where('is_delete',0)->where('uid',$uid)->order('id', 'asc')->select();
        foreach ($achievements as $achievement){
            if ($achievement['type'] == 'lunwen') {
                $achievement['type'] =  '论文';
            } elseif ($achievement['type'] == 'zhuanli') {
                $achievement['type'] =  '专利';
            } elseif ($achievement['type'] == 'diaochabaogao') {
                $achievement['type'] =  '调查报告';
            } elseif ($achievement['type'] == 'shangyejihuashu') {
                $achievement['type'] =  '商业计划书';
            } elseif ($achievement['type'] == 'zhuzuo') {
                $achievement['type'] =  '著作';
            } elseif ($achievement['type'] == 'gongshangzhuce') {
                $achievement['type'] =  '工商注册';
            } elseif ($achievement['type'] == 'gzh') {
                $achievement['type'] =  '公众号';
            } elseif ($achievement['type'] == 'wz') {
                $achievement['type'] =  '网站';
            } elseif ($achievement['type'] == 'rj') {
                $achievement['type'] =  '软件';
            } elseif ($achievement['type'] == 'xcx') {
                $achievement['type'] =  '小程序';
            } elseif ($achievement['type'] == 'app') {
                $achievement['type'] =  'APP';
            } elseif ($achievement['type'] == 'yyh') {
                $achievement['type'] =  '运营号';
            } elseif ($achievement['type'] == 'wk') {
                $achievement['type'] =  '微课';
            } elseif ($achievement['type'] == 'sp') {
                $achievement['type'] =  '视频';
            } elseif ($achievement['type'] == 'hb') {
                $achievement['type'] =  '绘本（图册）';
            } elseif ($achievement['type'] == 'other') {
                $achievement['type'] =  '其他';
            }
        }
        $conclude=[
            'conclude'=>Dcconclude::where('is_delete',0)->where('uid',$uid)->find(),
            'expenditure'=>Dcexpenditure::where('is_delete',0)->where('uid',$uid)->order('id', 'asc')->select(),
            'achievement'=>$achievements,
            'files'=>File::where('is_delete',0)->where('uid',$uid)->where('type',3)->order('id', 'asc')->select(),
        ];
        $extension=Dcextension::where('is_delete',0)->where('uid',$uid)->find();
        $midchange=Dcmidchange::where('uid',$uid)->find();
        $old_members = [];
        $old_teachers = [];
        // 处理中期变更的JSON数据
        if ($midchange) {
            if ($midchange['new_members']) {
                $midchange['new_members_array'] = json_decode($midchange['new_members'], true);
            } else {
                $midchange['new_members_array'] = [];
            }
            if ($midchange['new_teachers']) {
                $midchange['new_teachers_array'] = json_decode($midchange['new_teachers'], true);
            } else {
                $midchange['new_teachers_array'] = [];
            }
            // 解析原项目成员和原指导教师
            if (!empty($midchange['old_members'])) {
                $old_members = json_decode($midchange['old_members'], true);
            }
            if (!empty($midchange['old_teachers'])) {
                $old_teachers = json_decode($midchange['old_teachers'], true);
            }
        }
        // 补全old_members/old_teachers的name字段
        if (!empty($old_members)) {
            foreach ($old_members as &$m) {
                if (empty($m['name']) && !empty($m['username'])) {
                    $user = \app\model\User::where('username', $m['username'])->find();
                    $m['name'] = $user ? $user['name'] : '--';
                }
            }
            unset($m);
        }
        if (!empty($old_teachers)) {
            foreach ($old_teachers as &$t) {
                if (empty($t['name']) && !empty($t['username'])) {
                    $user = \app\model\User::where('username', $t['username'])->find();
                    $t['name'] = $user ? $user['name'] : '--';
                }
            }
            unset($t);
        }
        return view('dcmanage/detail',[
            'project'=>$project,
            'member'=>$member,
            'teacher'=>$teacher,
            'outteacher'=>$outteacher,
            'expected'=>$expected,
            'checks'=>$checks,
            'file1_path'=>$file1['path'],
            'progesses'=>$progesses,
            'interm'=>$interm,
            'conclude'=>$conclude,
            'extension'=>$extension,
            'midchange'=>$midchange,
            'old_members'=>$old_members,
            'old_teachers'=>$old_teachers,
            ]);
    }
}