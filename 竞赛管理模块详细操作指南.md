# 竞赛管理模块详细操作指南

## 目录
1. [模块概述](#1-模块概述)
2. [数据库设计](#2-数据库设计)
3. [竞赛管理功能](#3-竞赛管理功能)
4. [竞赛项目申请](#4-竞赛项目申请)
5. [竞赛项目审核](#5-竞赛项目审核)
6. [证书管理](#6-证书管理)
7. [状态流转逻辑](#7-状态流转逻辑)
8. [权限控制](#8-权限控制)
9. [操作流程详解](#9-操作流程详解)
10. [常见问题处理](#10-常见问题处理)

---

## 1. 模块概述

### 1.1 功能简介
竞赛管理模块是创新创业系统的核心功能之一，主要负责管理各类学科竞赛的申请、审核、证书上传等全流程业务。

### 1.2 主要功能
- **竞赛信息管理**：添加、修改、删除竞赛信息
- **竞赛项目申请**：学生申请参加竞赛
- **多级审核流程**：指导教师→学院→学校三级审核
- **证书管理**：获奖证书上传和管理
- **数据统计**：竞赛项目数据统计和导出

### 1.3 涉及角色
- **学生**：申请竞赛项目、上传证书
- **教师**：审核指导的竞赛项目
- **院级管理员**：审核本院竞赛项目
- **校级管理员**：审核全校竞赛项目、管理竞赛信息

---

## 2. 数据库设计

### 2.1 核心数据表

#### 2.1.1 jscompetition（竞赛信息表）
```sql
CREATE TABLE `jscompetition` (
  `cuid` varchar(36) NOT NULL COMMENT '竞赛ID',
  `name` varchar(255) NOT NULL COMMENT '竞赛名称',
  `level` int NOT NULL COMMENT '竞赛级别ID',
  `url` varchar(500) DEFAULT NULL COMMENT '官网链接',
  `remark` text COMMENT '备注',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cuid`)
);
```

#### 2.1.2 jssetting（竞赛设置表）
```sql
CREATE TABLE `jssetting` (
  `cuid` varchar(36) NOT NULL COMMENT '竞赛ID',
  `active` tinyint(1) DEFAULT '0' COMMENT '是否启用',
  `years` json DEFAULT NULL COMMENT '举办年份',
  `member` int DEFAULT '0' COMMENT '成员数量限制',
  `teacher` int DEFAULT '0' COMMENT '指导教师数量限制',
  `outstu` tinyint(1) DEFAULT '0' COMMENT '是否允许校外学生',
  `outstu_num` int DEFAULT '0' COMMENT '校外学生数量限制',
  `outtea` tinyint(1) DEFAULT '0' COMMENT '是否允许校外教师',
  `outtea_num` int DEFAULT '0' COMMENT '校外教师数量限制',
  PRIMARY KEY (`cuid`)
);
```

#### 2.1.3 jsproject（竞赛项目表）
```sql
CREATE TABLE `jsproject` (
  `uid` varchar(36) NOT NULL COMMENT '项目ID',
  `cuid` varchar(36) NOT NULL COMMENT '竞赛ID',
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `class` varchar(255) DEFAULT NULL COMMENT '赛道/类别',
  `time` varchar(255) DEFAULT NULL COMMENT '立项时间',
  `status` int DEFAULT '1' COMMENT '项目状态',
  `award` int DEFAULT NULL COMMENT '获奖等级ID',
  `certificate` varchar(500) DEFAULT NULL COMMENT '证书路径',
  `award_remark` text COMMENT '获奖备注',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`)
);
```

#### 2.1.4 jscheck（竞赛审核表）
```sql
CREATE TABLE `jscheck` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(36) NOT NULL COMMENT '项目ID',
  `type` int NOT NULL COMMENT '审核类型：1教师，2学院，3学校',
  `check` tinyint(1) NOT NULL COMMENT '审核结果：0通过，1驳回',
  `status` int NOT NULL COMMENT '审核状态',
  `remark` text COMMENT '审核意见',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 2.1.5 jsprogress（竞赛进度表）
```sql
CREATE TABLE `jsprogress` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(36) NOT NULL COMMENT '项目ID',
  `action` varchar(255) NOT NULL COMMENT '操作类型',
  `remark` text COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 2.2 关联表
- **jslevel**：竞赛级别表
- **jsstatus**：竞赛状态表
- **jsaward**：获奖等级表
- **member**：项目成员表
- **teacher**：指导教师表

---

## 3. 竞赛管理功能

### 3.1 竞赛信息管理

#### 3.1.1 添加竞赛
**操作步骤**：
1. 登录系统，进入"竞赛平台"→"竞赛管理"
2. 点击"添加竞赛"按钮
3. 填写竞赛信息：
   - **竞赛名称**（必填）：竞赛的完整名称
   - **竞赛级别**（必选）：国家级、省级、校级等
   - **官网链接**（必填）：竞赛官方网站地址
   - **备注**（可选）：竞赛相关说明
4. 设置竞赛参数：
   - **是否启用**：控制竞赛是否开放申请
   - **举办年份**：JSON格式存储年份数组
   - **成员数量限制**：每个项目最多成员数
   - **指导教师数量限制**：每个项目最多指导教师数
   - **校外学生**：是否允许校外学生参加
   - **校外学生数量**：校外学生数量限制
   - **校外教师**：是否允许校外教师指导
   - **校外教师数量**：校外教师数量限制
5. 点击"保存"提交

**业务逻辑**：
```php
// 验证必填字段
if (empty($data['name'])) {
    return json(['status' => 'error', 'message' => '竞赛名称不能为空']);
}

// 检查竞赛名称是否已存在
$existingCompetition = Jscompetition::where('name', $data['name'])
    ->where('is_delete', 0)
    ->find();

// 生成UUID作为竞赛ID
$cuid = Uuid::uuid4()->toString();

// 事务处理：同时插入竞赛信息和设置信息
Db::startTrans();
try {
    // 插入竞赛基本信息
    $competitionResult = Jscompetition::insert($competitionData);
    
    // 插入竞赛设置信息
    $settingResult = Jssetting::insert($settingData);
    
    if ($competitionResult && $settingResult) {
        Db::commit();
        return json(['status' => 'success', 'message' => '竞赛添加成功']);
    }
} catch (Exception $e) {
    Db::rollback();
    return json(['status' => 'error', 'message' => '添加失败']);
}
```

#### 3.1.2 修改竞赛
**操作步骤**：
1. 在竞赛列表中找到要修改的竞赛
2. 点击"编辑"按钮
3. 修改相关信息
4. 点击"保存"提交

**业务逻辑**：
```php
// 验证竞赛是否存在
$existingCompetition = Jscompetition::where('cuid', $data['cuid'])
    ->where('is_delete', 0)
    ->find();

// 检查名称是否被其他竞赛使用
$nameExists = Jscompetition::where('name', $data['name'])
    ->where('cuid', '<>', $data['cuid'])
    ->where('is_delete', 0)
    ->find();

// 事务更新
Db::startTrans();
try {
    $competitionResult = Jscompetition::where('cuid', $data['cuid'])->update($competitionData);
    $settingResult = Jssetting::where('cuid', $data['cuid'])->update($settingData);
    
    if ($competitionResult !== false && $settingResult !== false) {
        Db::commit();
        return json(['status' => 'success', 'message' => '竞赛修改成功']);
    }
} catch (Exception $e) {
    Db::rollback();
    return json(['status' => 'error', 'message' => '修改失败']);
}
```

#### 3.1.3 删除竞赛
**操作步骤**：
1. 在竞赛列表中找到要删除的竞赛
2. 点击"删除"按钮
3. 确认删除操作

**注意事项**：
- 只能删除没有关联项目的竞赛
- 删除操作为软删除，数据不会真正删除

#### 3.1.4 竞赛列表查询
**查询条件**：
- 竞赛级别筛选
- 关键词搜索（竞赛名称、ID、官网链接）
- 启用状态筛选

**查询逻辑**：
```php
$data = Jscompetition::alias('c')
    ->field('
        c.cuid,c.name,c.url,c.remark,c.level,
        s.active,s.years,s.member,s.teacher,s.outstu,s.outstu_num,s.outtea,s.outtea_num,
        l.name as l_level
    ')
    ->join('jslevel l','l.id=c.level','LEFT')
    ->join('jssetting s','s.cuid=c.cuid','LEFT')
    ->where($where)
    ->where('c.is_delete', 0)
    ->order('s.active','desc')
    ->order('c.updated_at')
    ->select();
```

---

## 4. 竞赛项目申请

### 4.1 申请流程

#### 4.1.1 选择竞赛
**操作步骤**：
1. 学生登录系统，进入"竞赛平台"
2. 点击"项目申请"
3. 在竞赛列表中选择要参加的竞赛
4. 查看竞赛详情和要求

#### 4.1.2 填写申请信息
**必填信息**：
- **项目名称**：参赛项目的名称
- **赛道/类别**：竞赛的具体赛道或类别
- **项目描述**：项目的简要描述

#### 4.1.3 添加项目成员
**操作步骤**：
1. 点击"添加成员"
2. 搜索并选择成员
3. 设置成员角色（负责人/成员）
4. 确认成员信息

**限制条件**：
- 成员数量不能超过竞赛设置的限制
- 负责人只能有一个
- 成员必须是本校学生（除非竞赛允许校外学生）

#### 4.1.4 添加指导教师
**操作步骤**：
1. 点击"添加教师"
2. 搜索并选择指导教师
3. 确认教师信息

**限制条件**：
- 指导教师数量不能超过竞赛设置的限制
- 指导教师必须是本校教师（除非竞赛允许校外教师）

#### 4.1.5 上传相关材料
**支持格式**：
- 文档：PDF、DOC、DOCX
- 图片：JPG、PNG、GIF
- 压缩包：ZIP、RAR

**文件大小限制**：
- 单个文件最大10MB

#### 4.1.6 提交申请
**操作步骤**：
1. 检查所有信息是否完整
2. 点击"提交申请"
3. 确认提交

**提交后状态**：
- 项目状态变为"等待指导教师审核"
- 系统自动发送通知给指导教师

---

## 5. 竞赛项目审核

### 5.1 审核流程

#### 5.1.1 指导教师审核
**审核权限**：
- 只能审核自己指导的项目
- 必须是第一指导教师

**审核操作**：
1. 登录系统，进入"竞赛平台"→"项目审核"
2. 查看待审核项目列表
3. 点击项目名称查看详情
4. 填写审核意见
5. 选择审核结果（通过/驳回）
6. 点击"提交审核"

**审核逻辑**：
```php
if (session('user.usermode') == 2) {
    // 检查是否为第一指导教师
    if (!Teacher::where('is_delete',0)
        ->where('uid',$uid)
        ->where('username',session('user.username'))
        ->where('rank',1)
        ->find()) {
        return ['status'=>'error','message'=>'您不是该项目的第一指导教师'];
    }
    
    if ($project['status'] == 1) {
        // 报名审核
        if ($check == 0) {
            // 通过
            $new_status = 2;
            $progess['action'] = '指导教师报名审核';
            $progess['remark'] = '指导教师报名审核通过';
        } else {
            // 驳回
            $new_status = 9;
            $progess['action'] = '指导教师报名审核';
            $progess['remark'] = '指导教师报名审核驳回';
        }
    } elseif ($project['status'] == 5) {
        // 证书审核
        if ($check == 0) {
            // 通过
            $new_status = 6;
            $progess['action'] = '指导教师证书审核';
            $progess['remark'] = '指导教师证书审核通过';
        } else {
            // 驳回
            $new_status = 12;
            $progess['action'] = '指导教师证书审核';
            $progess['remark'] = '指导教师证书审核驳回';
        }
    }
}
```

#### 5.1.2 学院审核
**审核权限**：
- 只能审核本院的项目
- 院级管理员权限

**审核操作**：
1. 查看本院待审核项目
2. 审核项目信息
3. 填写审核意见
4. 选择审核结果
5. 提交审核

**审核逻辑**：
```php
if (session('user.usermode') == 5) {
    // 检查是否为该学院内项目
    if (!Member::alias('m')
        ->join('user u', 'm.username = u.username', 'LEFT')
        ->where('m.is_delete',0)
        ->where('u.status',0)
        ->where('m.rank',1)
        ->where('m.uid',$uid)
        ->where('u.college',session('user.college'))
        ->find()) {
        return ['status'=>'error','message'=>'该项目不属于您的学院'];
    }
    
    if ($project['status'] == 2) {
        // 报名审核
        if ($check == 0) {
            $new_status = 3;
            $progess['action'] = '学院报名审核';
            $progess['remark'] = '学院报名审核通过';
        } else {
            $new_status = 10;
            $progess['action'] = '学院报名审核';
            $progess['remark'] = '学院报名审核驳回';
        }
    } elseif ($project['status'] == 6) {
        // 证书审核
        if ($check == 0) {
            $new_status = 7;
            $progess['action'] = '学院证书审核';
            $progess['remark'] = '学院证书审核通过';
        } else {
            $new_status = 13;
            $progess['action'] = '学院证书审核';
            $progess['remark'] = '学院证书审核驳回';
        }
    }
}
```

#### 5.1.3 学校审核
**审核权限**：
- 可以审核全校项目
- 校级管理员权限

**审核操作**：
1. 查看全校待审核项目
2. 审核项目信息
3. 填写审核意见
4. 选择审核结果
5. 提交审核

**审核逻辑**：
```php
if (session('user.usermode') == 6) {
    if ($project['status'] == 3) {
        // 报名审核
        if ($check == 0) {
            $new_status = 4;
            $progess['action'] = '学校报名审核';
            $progess['remark'] = '学校报名审核通过';
        } else {
            $new_status = 11;
            $progess['action'] = '学校报名审核';
            $progess['remark'] = '学校报名审核驳回';
        }
    } elseif ($project['status'] == 7) {
        // 证书审核
        if ($check == 0) {
            $new_status = 8;
            $progess['action'] = '学校证书审核';
            $progess['remark'] = '学校证书审核通过';
        } else {
            $new_status = 14;
            $progess['action'] = '学校证书审核';
            $progess['remark'] = '学校证书审核驳回';
        }
    }
}
```

### 5.2 审核记录管理

#### 5.2.1 审核记录存储
每次审核都会在以下表中记录：
- **jscheck**：审核记录表
- **jsprogress**：进度记录表
- **jsproject**：项目状态更新

#### 5.2.2 审核历史查询
**查询功能**：
- 查看项目的完整审核历史
- 查看审核意见和结果
- 查看审核时间线

---

## 6. 证书管理

### 6.1 证书上传

#### 6.1.1 上传条件
**状态要求**：
- 项目状态为4（学校报名审核通过）
- 项目状态为5（指导教师证书审核中）
- 项目状态为12、13、14（证书审核驳回后重新提交）

#### 6.1.2 上传操作
**操作步骤**：
1. 在竞赛项目列表中找到对应项目
2. 点击"上传证书"
3. 选择获奖等级
4. 上传证书扫描件
5. 填写获奖备注
6. 点击"提交"

**业务逻辑**：
```php
public function addcertificate($uid){
    // 检查项目是否存在
    $project = Jsproject::where('is_delete',0)->where('uid',$uid)->find();
    if (!$project){
        return json(['status'=>'error','message' => '项目不存在']);
    }
    
    // 检查状态是否允许上传证书
    if ($project['status']!=4 && $project['status']!=5 && 
        $project['status']!=12 && $project['status']!=13 && $project['status']!=14){
        return json(['status'=>'error','message' => '当前不是可以提交证书状态']);
    }
    
    // 确定新状态
    if ($project['status']==4){
        $new_status = 5;
    } elseif ($project['status']==5){
        $new_status = 5;
    } elseif ($project['status']==12){
        $new_status = 5;
    } elseif ($project['status']==13){
        $new_status = 6;
    } elseif ($project['status']==14){
        $new_status = 7;
    }
    
    // 更新项目信息
    $award = [
        'award' => $data['award'],
        'certificate' => $data['certificate'],
        'award_remark' => $data['remark'],
        'status' => $new_status
    ];
    
    // 添加进度记录
    $progress = [
        'uid' => $uid,
        'action' => '证书上传',
        'remark' => '学生提交获奖信息'
    ];
    
    // 事务处理
    Db::startTrans();
    try {
        $updateResult = Jsproject::where('is_delete', 0)->where('uid', $uid)->update($award);
        $insertResult = Jsprogress::insert($progress);
        
        if ($updateResult && $insertResult) {
            Db::commit();
            return json(['status' => 'success', 'message' => '获奖信息上传成功']);
        } else {
            Db::rollback();
            return json(['status' => 'error', 'message' => '数据异常']);
        }
    } catch (\Exception $e) {
        Db::rollback();
        return json(['status' => 'error', 'message' => '系统异常，请稍后再试']);
    }
}
```

### 6.2 证书审核

#### 6.2.1 审核流程
证书上传后需要经过三级审核：
1. **指导教师审核**：审核证书真实性
2. **学院审核**：确认获奖情况
3. **学校审核**：最终确认

#### 6.2.2 审核标准
- 证书格式是否正确
- 获奖等级是否准确
- 证书内容是否真实
- 获奖时间是否合理

---

## 7. 状态流转逻辑

### 7.1 项目状态定义

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 待提交 | 项目草稿状态 |
| 2 | 等待指导教师审核 | 已提交，等待教师审核 |
| 3 | 等待学院审核 | 教师审核通过，等待学院审核 |
| 4 | 等待学校审核 | 学院审核通过，等待学校审核 |
| 5 | 等待指导教师证书审核 | 学校审核通过，等待教师证书审核 |
| 6 | 等待学院证书审核 | 教师证书审核通过，等待学院证书审核 |
| 7 | 等待学校证书审核 | 学院证书审核通过，等待学校证书审核 |
| 8 | 已获奖 | 所有审核通过，项目完成 |
| 9 | 指导教师报名审核驳回 | 教师审核驳回 |
| 10 | 学院报名审核驳回 | 学院审核驳回 |
| 11 | 学校报名审核驳回 | 学校审核驳回 |
| 12 | 指导教师证书审核驳回 | 教师证书审核驳回 |
| 13 | 学院证书审核驳回 | 学院证书审核驳回 |
| 14 | 学校证书审核驳回 | 学校证书审核驳回 |

### 7.2 状态流转图

```
1(待提交) → 2(等待教师审核) → 3(等待学院审核) → 4(等待学校审核)
    ↓              ↓              ↓              ↓
   驳回            9(教师驳回)     10(学院驳回)    11(学校驳回)
    ↓              ↓              ↓              ↓
   重新提交        重新提交        重新提交        重新提交

4(等待学校审核) → 5(等待教师证书审核) → 6(等待学院证书审核) → 7(等待学校证书审核) → 8(已获奖)
    ↓                    ↓                    ↓                    ↓
   驳回                  12(教师证书驳回)       13(学院证书驳回)       14(学校证书驳回)
    ↓                    ↓                    ↓                    ↓
   重新提交              重新提交              重新提交              重新提交
```

### 7.3 状态转换逻辑

#### 7.3.1 报名阶段状态转换
```php
// 指导教师审核
if ($project['status'] == 1) {
    if ($check == 0) {
        $new_status = 2; // 通过，进入学院审核
    } else {
        $new_status = 9; // 驳回
    }
}

// 学院审核
if ($project['status'] == 2) {
    if ($check == 0) {
        $new_status = 3; // 通过，进入学校审核
    } else {
        $new_status = 10; // 驳回
    }
}

// 学校审核
if ($project['status'] == 3) {
    if ($check == 0) {
        $new_status = 4; // 通过，进入证书阶段
    } else {
        $new_status = 11; // 驳回
    }
}
```

#### 7.3.2 证书阶段状态转换
```php
// 指导教师证书审核
if ($project['status'] == 5) {
    if ($check == 0) {
        $new_status = 6; // 通过，进入学院证书审核
    } else {
        $new_status = 12; // 驳回
    }
}

// 学院证书审核
if ($project['status'] == 6) {
    if ($check == 0) {
        $new_status = 7; // 通过，进入学校证书审核
    } else {
        $new_status = 13; // 驳回
    }
}

// 学校证书审核
if ($project['status'] == 7) {
    if ($check == 0) {
        $new_status = 8; // 通过，项目完成
    } else {
        $new_status = 14; // 驳回
    }
}
```

---

## 8. 权限控制

### 8.1 角色权限矩阵

| 功能 | 学生 | 教师 | 院级管理员 | 校级管理员 | 超级管理员 |
|------|------|------|-----------|-----------|-----------|
| 查看竞赛列表 | ✓ | ✓ | ✓ | ✓ | ✓ |
| 申请竞赛项目 | ✓ | - | - | - | ✓ |
| 审核指导项目 | - | ✓ | - | - | ✓ |
| 审核本院项目 | - | - | ✓ | - | ✓ |
| 审核全校项目 | - | - | - | ✓ | ✓ |
| 管理竞赛信息 | - | - | - | ✓ | ✓ |
| 上传证书 | ✓ | - | - | - | ✓ |
| 查看统计数据 | - | - | ✓ | ✓ | ✓ |
| 导出数据 | - | - | ✓ | ✓ | ✓ |

### 8.2 权限验证逻辑

#### 8.2.1 审核权限验证
```php
public function checkPermission($permission) {
    $userMode = session('user.usermode');
    
    switch ($permission) {
        case 'js_check_teacher':
            return in_array($userMode, [2, 11]);
        case 'js_check_college':
            return in_array($userMode, [5, 11]);
        case 'js_check_school':
            return in_array($userMode, [6, 11]);
        default:
            return false;
    }
}
```

#### 8.2.2 项目操作权限验证
```php
public function canCheckProject($projectUid, $projectType = 'js') {
    $userMode = session('user.usermode');
    $user = session('user');
    
    // 超级管理员可以审核所有项目
    if ($userMode == 11) {
        return true;
    }
    
    // 教师只能审核自己指导的项目
    if ($userMode == 2) {
        return $this->isProjectTeacher($projectUid, $projectType);
    }
    
    // 院级管理员只能审核本院项目
    if ($userMode == 5) {
        return $this->isProjectInCollege($projectUid, $user['college']);
    }
    
    // 校级管理员可以审核所有项目
    if ($userMode == 6) {
        return true;
    }
    
    return false;
}
```

---

## 9. 操作流程详解

### 9.1 完整申请流程

#### 9.1.1 学生申请流程
1. **登录系统** → 进入竞赛平台
2. **选择竞赛** → 查看竞赛详情和要求
3. **填写申请** → 项目信息、成员、教师
4. **上传材料** → 相关文档和图片
5. **提交申请** → 等待审核

#### 9.1.2 审核流程
1. **指导教师审核** → 检查项目可行性
2. **学院审核** → 确认项目质量
3. **学校审核** → 最终确认
4. **证书上传** → 学生上传获奖证书
5. **证书审核** → 三级审核证书
6. **项目完成** → 状态变为已获奖

### 9.2 管理员操作流程

#### 9.2.1 竞赛管理流程
1. **添加竞赛** → 填写竞赛信息和设置
2. **启用竞赛** → 开放学生申请
3. **监控申请** → 查看申请情况
4. **管理项目** → 审核和管理项目
5. **统计分析** → 查看数据统计

#### 9.2.2 审核管理流程
1. **查看待审核** → 筛选待审核项目
2. **审核项目** → 查看详情并审核
3. **填写意见** → 详细的审核意见
4. **提交结果** → 通过或驳回
5. **跟踪进度** → 查看审核历史

### 9.3 数据管理流程

#### 9.3.1 数据导出流程
1. **选择条件** → 设置导出筛选条件
2. **生成报表** → 系统生成Excel文件
3. **下载文件** → 下载到本地
4. **数据分析** → 进行数据分析

#### 9.3.2 数据统计流程
1. **选择维度** → 按时间、学院、竞赛等维度
2. **生成统计** → 系统计算统计数据
3. **查看图表** → 可视化展示
4. **导出报告** → 生成统计报告

---

## 10. 常见问题处理

### 10.1 申请相关问题

#### 10.1.1 申请被驳回怎么办？
**解决方案**：
1. 查看驳回原因
2. 根据意见修改项目
3. 重新提交申请

**操作步骤**：
1. 在项目列表中找到被驳回的项目
2. 点击"查看详情"
3. 查看审核意见
4. 修改项目信息
5. 重新提交

#### 10.1.2 成员数量超限怎么办？
**解决方案**：
1. 检查竞赛设置的限制
2. 调整项目成员
3. 确保不超过限制

**限制说明**：
- 成员数量：竞赛设置中定义
- 指导教师数量：竞赛设置中定义
- 校外人员：根据竞赛设置决定

### 10.2 审核相关问题

#### 10.2.1 看不到待审核项目？
**可能原因**：
1. 权限不足
2. 项目不属于管辖范围
3. 项目状态不正确

**解决方法**：
1. 检查用户角色权限
2. 确认项目所属学院
3. 联系系统管理员

#### 10.2.2 审核意见怎么写？
**建议格式**：
1. **项目优点**：项目创新性、可行性等
2. **存在问题**：具体的问题描述
3. **改进建议**：具体的改进方向
4. **审核结论**：通过或驳回的决定

### 10.3 证书相关问题

#### 10.3.1 证书上传失败？
**可能原因**：
1. 文件格式不支持
2. 文件大小超限
3. 项目状态不允许

**解决方法**：
1. 检查文件格式（支持PDF、JPG等）
2. 压缩文件大小（限制10MB）
3. 确认项目状态

#### 10.3.2 证书审核被驳回？
**解决方案**：
1. 查看驳回原因
2. 重新上传正确的证书
3. 确保证书信息准确

### 10.4 系统相关问题

#### 10.4.1 页面加载慢？
**解决方法**：
1. 检查网络连接
2. 清除浏览器缓存
3. 联系技术支持

#### 10.4.2 数据不显示？
**解决方法**：
1. 刷新页面
2. 检查筛选条件
3. 联系管理员

### 10.5 权限相关问题

#### 10.5.1 无法访问某些功能？
**解决方法**：
1. 检查用户角色
2. 联系管理员分配权限
3. 确认功能是否启用

#### 10.5.2 审核权限不足？
**解决方法**：
1. 确认是否为项目指导教师
2. 确认是否为学院管理员
3. 联系系统管理员

---

## 11. 最佳实践建议

### 11.1 申请阶段
1. **提前准备**：提前了解竞赛要求和流程
2. **材料完整**：确保所有材料齐全
3. **信息准确**：仔细核对项目信息
4. **及时提交**：在规定时间内提交

### 11.2 审核阶段
1. **认真审核**：仔细查看项目详情
2. **意见明确**：提供具体的审核意见
3. **及时处理**：在规定时间内完成审核
4. **沟通协调**：与相关人员保持沟通

### 11.3 管理阶段
1. **定期检查**：定期查看项目状态
2. **数据备份**：定期备份重要数据
3. **统计分析**：定期进行数据分析
4. **流程优化**：根据实际情况优化流程

---

**文档版本**：v1.0  
**更新时间**：2024年12月  
**适用范围**：竞赛管理模块所有用户 