/*
 Navicat Premium Data Transfer

 Source Server         : 创新创业
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : cxcysys

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 28/08/2025 16:23:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for carousel
-- ----------------------------
DROP TABLE IF EXISTS `carousel`;
CREATE TABLE `carousel`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '轮播标题',
  `image_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '轮播图片路径',
  `news_id` int(11) NULL DEFAULT NULL COMMENT '关联的新闻ID',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_news_id`(`news_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of carousel
-- ----------------------------

-- ----------------------------
-- Table structure for dcachievement
-- ----------------------------
DROP TABLE IF EXISTS `dcachievement`;
CREATE TABLE `dcachievement`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time` date NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `main` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `num` int(11) NULL DEFAULT NULL,
  `author` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcachievement
-- ----------------------------
INSERT INTO `dcachievement` VALUES (23, '3537f00b-5842-4120-871a-f52b83f17805', 'zhuanli', '', NULL, '', '', 0, '', '2025-07-15 21:42:49', '2025-07-15 21:42:49', 0);
INSERT INTO `dcachievement` VALUES (24, '3537f00b-5842-4120-871a-f52b83f17805', 'gzh', '', NULL, '', '', 0, '', '2025-07-15 21:42:49', '2025-07-15 21:42:49', 0);

-- ----------------------------
-- Table structure for dccheck
-- ----------------------------
DROP TABLE IF EXISTS `dccheck`;
CREATE TABLE `dccheck`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `type` int(11) NULL DEFAULT NULL COMMENT '类型1教师2学院3学校',
  `check` int(11) NULL DEFAULT NULL COMMENT '0通过1驳回',
  `status` int(11) NULL DEFAULT NULL COMMENT '1立项2中期3结项4延期5变更6证书',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 59 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dccheck
-- ----------------------------
INSERT INTO `dccheck` VALUES (35, '3537f00b-5842-4120-871a-f52b83f17805', 1, 0, 1, '通过', '2025-07-15 19:37:14', '2025-07-15 19:37:14', 0);
INSERT INTO `dccheck` VALUES (36, '3537f00b-5842-4120-871a-f52b83f17805', 2, 0, 1, '通过', '2025-07-15 19:37:33', '2025-07-15 19:37:33', 0);
INSERT INTO `dccheck` VALUES (37, '3537f00b-5842-4120-871a-f52b83f17805', 3, 0, 1, '通过', '2025-07-15 19:37:55', '2025-07-15 19:37:55', 0);
INSERT INTO `dccheck` VALUES (38, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 5, '测试驳回', '2025-07-15 19:43:20', '2025-07-15 19:43:20', 0);
INSERT INTO `dccheck` VALUES (39, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 5, '测试驳回', '2025-07-15 19:51:15', '2025-07-15 19:51:15', 0);
INSERT INTO `dccheck` VALUES (40, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 5, '驳回', '2025-07-15 20:17:49', '2025-07-15 20:17:49', 0);
INSERT INTO `dccheck` VALUES (41, '3537f00b-5842-4120-871a-f52b83f17805', 1, 0, 5, '通过', '2025-07-15 20:20:05', '2025-07-15 20:20:05', 0);
INSERT INTO `dccheck` VALUES (42, '3537f00b-5842-4120-871a-f52b83f17805', 2, 1, 5, '测试驳回', '2025-07-15 20:48:11', '2025-07-15 20:48:11', 0);
INSERT INTO `dccheck` VALUES (43, '3537f00b-5842-4120-871a-f52b83f17805', 2, 0, 5, '通过', '2025-07-15 20:51:37', '2025-07-15 20:51:37', 0);
INSERT INTO `dccheck` VALUES (44, '3537f00b-5842-4120-871a-f52b83f17805', 3, 1, 5, '驳回', '2025-07-15 20:51:57', '2025-07-15 20:51:57', 0);
INSERT INTO `dccheck` VALUES (45, '3537f00b-5842-4120-871a-f52b83f17805', 3, 0, 5, '通过', '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0);
INSERT INTO `dccheck` VALUES (46, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 2, '测试驳回', '2025-07-15 21:00:59', '2025-07-15 21:00:59', 0);
INSERT INTO `dccheck` VALUES (47, '3537f00b-5842-4120-871a-f52b83f17805', 1, 0, 2, '通过', '2025-07-15 21:02:08', '2025-07-15 21:02:08', 0);
INSERT INTO `dccheck` VALUES (48, '3537f00b-5842-4120-871a-f52b83f17805', 2, 1, 2, '驳回', '2025-07-15 21:02:29', '2025-07-15 21:02:29', 0);
INSERT INTO `dccheck` VALUES (49, '3537f00b-5842-4120-871a-f52b83f17805', 2, 0, 2, '通过', '2025-07-15 21:02:53', '2025-07-15 21:02:53', 0);
INSERT INTO `dccheck` VALUES (50, '3537f00b-5842-4120-871a-f52b83f17805', 3, 1, 2, '驳回', '2025-07-15 21:03:25', '2025-07-15 21:03:25', 0);
INSERT INTO `dccheck` VALUES (51, '3537f00b-5842-4120-871a-f52b83f17805', 3, 0, 2, '通过', '2025-07-15 21:04:00', '2025-07-15 21:04:00', 0);
INSERT INTO `dccheck` VALUES (52, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 4, '驳回', '2025-07-15 21:32:03', '2025-07-15 21:32:03', 0);
INSERT INTO `dccheck` VALUES (53, '3537f00b-5842-4120-871a-f52b83f17805', 1, 0, 4, '通过', '2025-07-15 21:33:26', '2025-07-15 21:33:26', 0);
INSERT INTO `dccheck` VALUES (54, '3537f00b-5842-4120-871a-f52b83f17805', 2, 1, 4, '驳回', '2025-07-15 21:33:40', '2025-07-15 21:33:40', 0);
INSERT INTO `dccheck` VALUES (55, '3537f00b-5842-4120-871a-f52b83f17805', 2, 0, 4, '体育', '2025-07-15 21:34:13', '2025-07-15 21:34:13', 0);
INSERT INTO `dccheck` VALUES (56, '3537f00b-5842-4120-871a-f52b83f17805', 3, 1, 4, '测试', '2025-07-15 21:34:36', '2025-07-15 21:34:36', 0);
INSERT INTO `dccheck` VALUES (57, '3537f00b-5842-4120-871a-f52b83f17805', 3, 0, 4, '通过', '2025-07-15 21:35:23', '2025-07-15 21:35:23', 0);
INSERT INTO `dccheck` VALUES (58, '3537f00b-5842-4120-871a-f52b83f17805', 1, 1, 3, '驳回', '2025-07-15 21:43:39', '2025-07-15 21:43:39', 0);

-- ----------------------------
-- Table structure for dcconclude
-- ----------------------------
DROP TABLE IF EXISTS `dcconclude`;
CREATE TABLE `dcconclude`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `results` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目成功简介',
  `conclusion` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目结题报告',
  `problem` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目实施过程中存在的问题和建议',
  `excellent_project` int(11) NULL DEFAULT NULL COMMENT '是否申请优秀项目0否1是',
  `excellent_lunwen` int(11) NULL DEFAULT NULL COMMENT '是否申请优秀论文0否1是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcconclude
-- ----------------------------
INSERT INTO `dcconclude` VALUES (14, '3537f00b-5842-4120-871a-f52b83f17805', '项目成果简介', '项目结题报告让他吧', '项目实施过程中存在的问题和建议', 1, 1, '2025-07-15 21:42:49', '2025-07-15 21:42:49', 0);

-- ----------------------------
-- Table structure for dcexpected
-- ----------------------------
DROP TABLE IF EXISTS `dcexpected`;
CREATE TABLE `dcexpected`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `lunwen_level` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '无' COMMENT '论文级别\r\n',
  `lunwen_num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '论文数量',
  `zhuanli` int(10) NULL DEFAULT 0 COMMENT '专利数量0是无',
  `diaochabaogao` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '调查报告数量',
  `shangyejihuashu` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '商业计划书数量',
  `zhuzuo` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '著作数量',
  `zuopin_class` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作品性质',
  `zuopin_num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '作品数量',
  `other` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他0无',
  `gzh` int(255) NULL DEFAULT 0,
  `wz` int(255) NULL DEFAULT 0,
  `rj` int(255) NULL DEFAULT 0,
  `xcx` int(255) NULL DEFAULT 0,
  `app` int(255) NULL DEFAULT 0,
  `yyh` int(255) NULL DEFAULT 0,
  `wk` int(255) NULL DEFAULT 0,
  `sp` int(255) NULL DEFAULT 0,
  `hb` int(255) NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 55 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcexpected
-- ----------------------------
INSERT INTO `dcexpected` VALUES (54, '3537f00b-5842-4120-871a-f52b83f17805', '无', 0, 2, 0, 0, 0, NULL, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-07-15 19:36:51', '2025-07-15 19:36:51', 0);

-- ----------------------------
-- Table structure for dcexpenditure
-- ----------------------------
DROP TABLE IF EXISTS `dcexpenditure`;
CREATE TABLE `dcexpenditure`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `amount` double NULL DEFAULT NULL COMMENT '经费金额',
  `detail` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经费明细',
  `type` int(11) NULL DEFAULT NULL COMMENT '经费类型1中期2结项',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 50 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcexpenditure
-- ----------------------------
INSERT INTO `dcexpenditure` VALUES (41, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大', 1, '2025-07-15 20:53:06', '2025-07-15 20:53:24', 1);
INSERT INTO `dcexpenditure` VALUES (42, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 20:53:24', '2025-07-15 20:53:58', 1);
INSERT INTO `dcexpenditure` VALUES (43, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 20:53:58', '2025-07-15 20:59:04', 1);
INSERT INTO `dcexpenditure` VALUES (44, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 20:59:04', '2025-07-15 21:01:29', 1);
INSERT INTO `dcexpenditure` VALUES (45, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 21:01:29', '2025-07-15 21:02:39', 1);
INSERT INTO `dcexpenditure` VALUES (46, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 21:02:39', '2025-07-15 21:03:42', 1);
INSERT INTO `dcexpenditure` VALUES (47, '3537f00b-5842-4120-871a-f52b83f17805', 800, '阿松大打法', 1, '2025-07-15 21:03:42', '2025-07-15 21:03:42', 0);
INSERT INTO `dcexpenditure` VALUES (48, '3537f00b-5842-4120-871a-f52b83f17805', 20, '爱的色放', 1, '2025-07-15 21:03:42', '2025-07-15 21:03:42', 0);
INSERT INTO `dcexpenditure` VALUES (49, '3537f00b-5842-4120-871a-f52b83f17805', 3, '阿萨', 2, '2025-07-15 21:42:49', '2025-07-15 21:42:49', 0);

-- ----------------------------
-- Table structure for dcextension
-- ----------------------------
DROP TABLE IF EXISTS `dcextension`;
CREATE TABLE `dcextension`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
  `extension_time` int(11) NOT NULL COMMENT '延期时间（月）',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '延期理由',
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `is_delete` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '延期结题申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcextension
-- ----------------------------
INSERT INTO `dcextension` VALUES (2, '3537f00b-5842-4120-871a-f52b83f17805', 12, '测试一下延期结题测试一下延期结题888859778士大夫', NULL, NULL, 0);

-- ----------------------------
-- Table structure for dcinterm
-- ----------------------------
DROP TABLE IF EXISTS `dcinterm`;
CREATE TABLE `dcinterm`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `progress` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '研究工作进展情况',
  `statement` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '资金使用补充说明',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcinterm
-- ----------------------------
INSERT INTO `dcinterm` VALUES (9, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况', '项目中期经费支出情况', '2025-07-15 20:53:06', '2025-07-15 20:53:24', 1);
INSERT INTO `dcinterm` VALUES (10, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况', '项目中期经费支出情况阿达啊', '2025-07-15 20:53:24', '2025-07-15 20:53:58', 1);
INSERT INTO `dcinterm` VALUES (11, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况你好', '项目中期经费支出情况阿达啊', '2025-07-15 20:53:58', '2025-07-15 20:59:04', 1);
INSERT INTO `dcinterm` VALUES (12, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况你好', '项目中期经费支出情况阿达啊', '2025-07-15 20:59:04', '2025-07-15 21:01:29', 1);
INSERT INTO `dcinterm` VALUES (13, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况你好', '项目中期经费支出情况阿达啊', '2025-07-15 21:01:29', '2025-07-15 21:02:39', 1);
INSERT INTO `dcinterm` VALUES (14, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况你好', '项目中期经费支出情况阿达啊', '2025-07-15 21:02:39', '2025-07-15 21:03:42', 1);
INSERT INTO `dcinterm` VALUES (15, '3537f00b-5842-4120-871a-f52b83f17805', '研究工作进展情况你好', '项目中期经费支出情况阿达啊', '2025-07-15 21:03:42', '2025-07-15 21:03:42', 0);

-- ----------------------------
-- Table structure for dcintermachievement
-- ----------------------------
DROP TABLE IF EXISTS `dcintermachievement`;
CREATE TABLE `dcintermachievement`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成果名称',
  `date` date NULL DEFAULT NULL COMMENT '成果完成时间',
  `form` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成果形式',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 20 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcintermachievement
-- ----------------------------
INSERT INTO `dcintermachievement` VALUES (13, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大', '2025-07-08', '阿松大', '66', '2025-07-15 20:53:06', '2025-07-15 20:53:24', 1);
INSERT INTO `dcintermachievement` VALUES (14, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大', '66', '2025-07-15 20:53:24', '2025-07-15 20:53:58', 1);
INSERT INTO `dcintermachievement` VALUES (15, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大', '66', '2025-07-15 20:53:58', '2025-07-15 20:59:04', 1);
INSERT INTO `dcintermachievement` VALUES (16, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大120', '66', '2025-07-15 20:59:04', '2025-07-15 21:01:29', 1);
INSERT INTO `dcintermachievement` VALUES (17, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大120000', '66', '2025-07-15 21:01:29', '2025-07-15 21:02:39', 1);
INSERT INTO `dcintermachievement` VALUES (18, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大120000', '', '2025-07-15 21:02:39', '2025-07-15 21:03:42', 1);
INSERT INTO `dcintermachievement` VALUES (19, '3537f00b-5842-4120-871a-f52b83f17805', '阿松大微软', '2025-07-08', '阿松大120000', '', '2025-07-15 21:03:42', '2025-07-15 21:03:42', 0);

-- ----------------------------
-- Table structure for dclevel
-- ----------------------------
DROP TABLE IF EXISTS `dclevel`;
CREATE TABLE `dclevel`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dclevel
-- ----------------------------
INSERT INTO `dclevel` VALUES (5, '未定级', '2025-03-03 14:24:28', '2025-03-08 08:13:29', 0);
INSERT INTO `dclevel` VALUES (1, '国家级', '2025-03-03 14:24:39', '2025-03-08 08:13:38', 0);
INSERT INTO `dclevel` VALUES (2, '省级', '2025-03-03 14:25:01', '2025-03-08 08:13:42', 0);
INSERT INTO `dclevel` VALUES (3, '校级', '2025-03-08 08:13:47', '2025-03-08 08:14:13', 0);
INSERT INTO `dclevel` VALUES (4, '院级', '2025-03-08 08:14:00', '2025-03-08 08:14:13', 0);

-- ----------------------------
-- Table structure for dcmidchange
-- ----------------------------
DROP TABLE IF EXISTS `dcmidchange`;
CREATE TABLE `dcmidchange`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
  `change_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更事项',
  `change_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更原因',
  `new_members` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新项目成员JSON',
  `new_teachers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新指导教师JSON',
  `old_members` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `old_teachers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `is_delete` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中期变更申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcmidchange
-- ----------------------------
INSERT INTO `dcmidchange` VALUES (7, '3537f00b-5842-4120-871a-f52b83f17805', '测试变更测试变更测试变更测试变更啊', '测试变更测试变更测试变更', '[{\"value\":\"2200802777\",\"label\":\"\\u5b812\"},{\"value\":\"220080277\",\"label\":\"\\u5b811\"}]', '[{\"value\":\"002\",\"label\":\"\\u8d22\\u52a1\\u5904\\u6559\\u5e08\"},{\"value\":\"001\",\"label\":\"\\u8f6f\\u4ef6\\u6559\\u5e08\"}]', '[{\"name\":\"\\u5b81\\u57ce\\u5609\",\"username\":\"22008027\"},{\"name\":\"\\u5b811\",\"username\":\"220080277\"},{\"name\":\"\\u5b812\",\"username\":\"2200802777\"}]', '[{\"name\":\"\\u8f6f\\u4ef6\\u6559\\u5e08\",\"username\":\"001\"}]', NULL, NULL, 0);

-- ----------------------------
-- Table structure for dcperiod
-- ----------------------------
DROP TABLE IF EXISTS `dcperiod`;
CREATE TABLE `dcperiod`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcperiod
-- ----------------------------
INSERT INTO `dcperiod` VALUES (1, '一年', '2025-03-03 14:24:28', '2025-03-03 14:36:49', 0);
INSERT INTO `dcperiod` VALUES (2, '两年', '2025-03-03 14:24:39', '2025-03-03 14:36:49', 0);
INSERT INTO `dcperiod` VALUES (3, '三年', '2025-03-03 14:25:01', '2025-03-03 14:36:49', 0);

-- ----------------------------
-- Table structure for dcprogress
-- ----------------------------
DROP TABLE IF EXISTS `dcprogress`;
CREATE TABLE `dcprogress`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大创项目编号',
  `action` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件名称',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件详情',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 163 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcprogress
-- ----------------------------
INSERT INTO `dcprogress` VALUES (100, '3537f00b-5842-4120-871a-f52b83f17805', '立项申请', '学生提交立项申请', 0, '2025-07-15 19:36:51');
INSERT INTO `dcprogress` VALUES (101, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师立项审核', '指导教师立项审核通过', 0, '2025-07-15 19:37:14');
INSERT INTO `dcprogress` VALUES (102, '3537f00b-5842-4120-871a-f52b83f17805', '学院立项审核', '学院立项审核通过', 0, '2025-07-15 19:37:33');
INSERT INTO `dcprogress` VALUES (103, '3537f00b-5842-4120-871a-f52b83f17805', '学校立项审核', '学校立项审核通过', 0, '2025-07-15 19:37:55');
INSERT INTO `dcprogress` VALUES (104, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更申请', '学生提交中期变更申请', 0, '2025-07-15 19:38:28');
INSERT INTO `dcprogress` VALUES (105, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学校审核通过中期变更，项目成员和指导教师已更新', 0, '2025-07-15 19:38:28');
INSERT INTO `dcprogress` VALUES (106, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更驳回恢复', '中期变更被驳回，恢复原项目成员和指导教师', 0, '2025-07-15 19:43:20');
INSERT INTO `dcprogress` VALUES (107, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期变更审核', '指导教师中期变更审核驳回', 0, '2025-07-15 19:43:20');
INSERT INTO `dcprogress` VALUES (108, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更驳回恢复', '中期变更被驳回，恢复原项目成员和指导教师', 0, '2025-07-15 19:51:15');
INSERT INTO `dcprogress` VALUES (109, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期变更审核', '指导教师中期变更审核驳回', 0, '2025-07-15 19:51:15');
INSERT INTO `dcprogress` VALUES (110, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期变更申请', '学生修改中期变更申请', 0, '2025-07-15 20:15:30');
INSERT INTO `dcprogress` VALUES (111, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学校审核通过中期变更，项目成员和指导教师已更新', 0, '2025-07-15 20:15:30');
INSERT INTO `dcprogress` VALUES (112, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更驳回恢复', '中期变更被驳回，恢复原项目成员和指导教师', 0, '2025-07-15 20:17:49');
INSERT INTO `dcprogress` VALUES (113, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期变更审核', '指导教师中期变更审核驳回', 0, '2025-07-15 20:17:49');
INSERT INTO `dcprogress` VALUES (114, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期变更申请', '学生修改中期变更申请', 0, '2025-07-15 20:18:17');
INSERT INTO `dcprogress` VALUES (115, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学校审核通过中期变更，项目成员和指导教师已更新', 0, '2025-07-15 20:18:17');
INSERT INTO `dcprogress` VALUES (116, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期变更审核', '指导教师中期变更审核通过', 0, '2025-07-15 20:20:05');
INSERT INTO `dcprogress` VALUES (117, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更驳回恢复', '中期变更被驳回，恢复原项目成员和指导教师', 0, '2025-07-15 20:48:11');
INSERT INTO `dcprogress` VALUES (118, '3537f00b-5842-4120-871a-f52b83f17805', '学院中期变更审核', '学院中期变更审核驳回', 0, '2025-07-15 20:48:11');
INSERT INTO `dcprogress` VALUES (119, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期变更申请', '学生修改中期变更申请', 0, '2025-07-15 20:48:24');
INSERT INTO `dcprogress` VALUES (120, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学生提交中期变更，项目成员和指导教师已更新', 0, '2025-07-15 20:48:24');
INSERT INTO `dcprogress` VALUES (121, '3537f00b-5842-4120-871a-f52b83f17805', '学院中期变更审核', '学院中期变更审核通过', 0, '2025-07-15 20:51:37');
INSERT INTO `dcprogress` VALUES (122, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更驳回恢复', '中期变更被驳回，恢复原项目成员和指导教师', 0, '2025-07-15 20:51:57');
INSERT INTO `dcprogress` VALUES (123, '3537f00b-5842-4120-871a-f52b83f17805', '学校中期变更审核', '学校中期变更审核驳回', 0, '2025-07-15 20:51:57');
INSERT INTO `dcprogress` VALUES (124, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期变更申请', '学生修改中期变更申请', 0, '2025-07-15 20:52:13');
INSERT INTO `dcprogress` VALUES (125, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学生提交中期变更，项目成员和指导教师已更新', 0, '2025-07-15 20:52:13');
INSERT INTO `dcprogress` VALUES (126, '3537f00b-5842-4120-871a-f52b83f17805', '学校中期变更审核', '学校中期变更审核通过', 0, '2025-07-15 20:52:30');
INSERT INTO `dcprogress` VALUES (127, '3537f00b-5842-4120-871a-f52b83f17805', '中期变更数据更新', '学生提交中期变更，项目成员和指导教师已更新', 0, '2025-07-15 20:52:30');
INSERT INTO `dcprogress` VALUES (128, '3537f00b-5842-4120-871a-f52b83f17805', '中期申请', '学生提交中期报告', 0, '2025-07-15 20:53:06');
INSERT INTO `dcprogress` VALUES (129, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 20:53:24');
INSERT INTO `dcprogress` VALUES (130, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 20:53:58');
INSERT INTO `dcprogress` VALUES (131, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 20:59:04');
INSERT INTO `dcprogress` VALUES (132, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期审核', '指导教师中期审核驳回', 0, '2025-07-15 21:00:59');
INSERT INTO `dcprogress` VALUES (133, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 21:01:29');
INSERT INTO `dcprogress` VALUES (134, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师中期审核', '指导教师中期审核通过', 0, '2025-07-15 21:02:08');
INSERT INTO `dcprogress` VALUES (135, '3537f00b-5842-4120-871a-f52b83f17805', '学院中期审核', '学院中期审核驳回', 0, '2025-07-15 21:02:29');
INSERT INTO `dcprogress` VALUES (136, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 21:02:39');
INSERT INTO `dcprogress` VALUES (137, '3537f00b-5842-4120-871a-f52b83f17805', '学院中期审核', '学院中期审核通过', 0, '2025-07-15 21:02:53');
INSERT INTO `dcprogress` VALUES (138, '3537f00b-5842-4120-871a-f52b83f17805', '学校中期审核', '学校中期审核驳回', 0, '2025-07-15 21:03:25');
INSERT INTO `dcprogress` VALUES (139, '3537f00b-5842-4120-871a-f52b83f17805', '修改中期申请', '学生修改中期报告', 0, '2025-07-15 21:03:42');
INSERT INTO `dcprogress` VALUES (140, '3537f00b-5842-4120-871a-f52b83f17805', '学校中期审核', '学校中期审核通过', 0, '2025-07-15 21:04:00');
INSERT INTO `dcprogress` VALUES (141, '3537f00b-5842-4120-871a-f52b83f17805', '延期结题申请', '学生提交延期结题申请', 0, '2025-07-15 21:05:39');
INSERT INTO `dcprogress` VALUES (142, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:20:32');
INSERT INTO `dcprogress` VALUES (143, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:20:36');
INSERT INTO `dcprogress` VALUES (144, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:20:42');
INSERT INTO `dcprogress` VALUES (145, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:22:00');
INSERT INTO `dcprogress` VALUES (146, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:22:29');
INSERT INTO `dcprogress` VALUES (147, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:25:18');
INSERT INTO `dcprogress` VALUES (148, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:25:21');
INSERT INTO `dcprogress` VALUES (149, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:25:25');
INSERT INTO `dcprogress` VALUES (150, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:30:00');
INSERT INTO `dcprogress` VALUES (151, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:30:03');
INSERT INTO `dcprogress` VALUES (152, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师延期审核', '指导教师延期审核驳回', 0, '2025-07-15 21:32:03');
INSERT INTO `dcprogress` VALUES (153, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:33:08');
INSERT INTO `dcprogress` VALUES (154, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师延期审核', '指导教师延期审核通过', 0, '2025-07-15 21:33:26');
INSERT INTO `dcprogress` VALUES (155, '3537f00b-5842-4120-871a-f52b83f17805', '学院延期审核', '学院延期审核驳回', 0, '2025-07-15 21:33:40');
INSERT INTO `dcprogress` VALUES (156, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:34:01');
INSERT INTO `dcprogress` VALUES (157, '3537f00b-5842-4120-871a-f52b83f17805', '学院延期审核', '学院延期审核通过', 0, '2025-07-15 21:34:13');
INSERT INTO `dcprogress` VALUES (158, '3537f00b-5842-4120-871a-f52b83f17805', '学校延期审核', '学校延期审核驳回', 0, '2025-07-15 21:34:36');
INSERT INTO `dcprogress` VALUES (159, '3537f00b-5842-4120-871a-f52b83f17805', '修改延期结题申请', '学生修改延期结题申请', 0, '2025-07-15 21:34:45');
INSERT INTO `dcprogress` VALUES (160, '3537f00b-5842-4120-871a-f52b83f17805', '学校延期审核', '学校延期审核通过', 0, '2025-07-15 21:35:23');
INSERT INTO `dcprogress` VALUES (161, '3537f00b-5842-4120-871a-f52b83f17805', '修改结题申请', '学生修改结题报告', 0, '2025-07-15 21:42:49');
INSERT INTO `dcprogress` VALUES (162, '3537f00b-5842-4120-871a-f52b83f17805', '指导教师结题审核', '指导教师结题审核驳回', 0, '2025-07-15 21:43:39');

-- ----------------------------
-- Table structure for dcproject
-- ----------------------------
DROP TABLE IF EXISTS `dcproject`;
CREATE TABLE `dcproject`  (
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目申请编号',
  `proid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目正式编号',
  `level` int(11) NULL DEFAULT NULL COMMENT '项目级别\r\n对应dclevel表\r\n',
  `status` int(11) NULL DEFAULT NULL COMMENT '项目当前状态，在dcstatus表里面对应',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `time` date NULL DEFAULT NULL COMMENT '立项时间',
  `type` int(11) NULL DEFAULT NULL COMMENT '项目类型，对应dctype表',
  `period` int(11) NULL DEFAULT NULL COMMENT '项目周期，对应dcperiod表',
  `introduction` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目简介',
  `reason` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请理由',
  `innovation` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目特色及创新点',
  `schedule` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目进度安排',
  `budget` double NULL DEFAULT NULL COMMENT '项目经费',
  `plan` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '经费使用计划',
  `shangyehua` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否商业化',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `certificate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证书',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`uid`, `id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcproject
-- ----------------------------
INSERT INTO `dcproject` VALUES ('3537f00b-5842-4120-871a-f52b83f17805', NULL, 0, 18, '测试中期变更事宜', '2025-07-09', 1, 1, '项目简介', '项目简介', '项目简介', '项目简介', 5000, '项目简介', '0', 0, '2025-07-15 19:36:51', '2025-07-15 21:43:39', NULL, 1);

-- ----------------------------
-- Table structure for dcstatus
-- ----------------------------
DROP TABLE IF EXISTS `dcstatus`;
CREATE TABLE `dcstatus`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 39 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dcstatus
-- ----------------------------
INSERT INTO `dcstatus` VALUES (1, '等待指导教师立项审核', '2025-03-07 19:44:28', '2025-03-07 19:44:28', 0);
INSERT INTO `dcstatus` VALUES (2, '等待学院立项审核', '2025-03-07 19:45:06', '2025-03-07 19:45:06', 0);
INSERT INTO `dcstatus` VALUES (3, '等待学校立项审核', '2025-03-07 19:45:23', '2025-03-07 19:45:23', 0);
INSERT INTO `dcstatus` VALUES (4, '已立项待提交中期报告', '2025-03-07 19:45:50', '2025-03-07 19:45:50', 0);
INSERT INTO `dcstatus` VALUES (5, '等待指导教师中期审核', '2025-03-07 19:46:17', '2025-03-07 19:46:17', 0);
INSERT INTO `dcstatus` VALUES (6, '等待学院中期审核', '2025-03-07 19:46:28', '2025-03-07 19:46:28', 0);
INSERT INTO `dcstatus` VALUES (7, '等待学校中期审核', '2025-03-07 19:46:37', '2025-03-07 19:46:37', 0);
INSERT INTO `dcstatus` VALUES (8, '待提交结题报告', '2025-03-07 19:46:58', '2025-03-07 19:46:58', 0);
INSERT INTO `dcstatus` VALUES (9, '等待指导教师结题审核', '2025-03-07 19:47:20', '2025-03-07 19:47:20', 0);
INSERT INTO `dcstatus` VALUES (10, '等待学院结题审核', '2025-03-07 19:47:28', '2025-03-07 19:47:28', 0);
INSERT INTO `dcstatus` VALUES (11, '等待学校结题审核', '2025-03-07 19:47:36', '2025-03-07 19:47:36', 0);
INSERT INTO `dcstatus` VALUES (12, '指导教师立项审核驳回', '2025-03-07 21:42:22', '2025-03-07 21:44:44', 0);
INSERT INTO `dcstatus` VALUES (13, '学院立项审核驳回', '2025-03-07 21:44:57', '2025-03-07 21:44:57', 0);
INSERT INTO `dcstatus` VALUES (14, '学校立项审核驳回', '2025-03-07 21:45:06', '2025-03-07 21:45:06', 0);
INSERT INTO `dcstatus` VALUES (15, '指导教师中期审核驳回', '2025-03-07 21:45:21', '2025-03-07 21:45:21', 0);
INSERT INTO `dcstatus` VALUES (16, '学院中期审核驳回', '2025-03-07 21:45:32', '2025-03-07 21:45:32', 0);
INSERT INTO `dcstatus` VALUES (17, '学校中期审核驳回', '2025-03-07 21:45:43', '2025-03-07 21:45:43', 0);
INSERT INTO `dcstatus` VALUES (18, '指导教师结题审核驳回', '2025-03-07 21:45:59', '2025-03-07 21:45:59', 0);
INSERT INTO `dcstatus` VALUES (19, '学院结题审核驳回', '2025-03-07 21:46:14', '2025-03-07 21:46:14', 0);
INSERT INTO `dcstatus` VALUES (20, '学校结题审核驳回', '2025-03-07 21:46:29', '2025-03-07 21:46:29', 0);
INSERT INTO `dcstatus` VALUES (21, '项目结题', '2025-03-11 17:31:12', '2025-03-12 10:18:57', 0);
INSERT INTO `dcstatus` VALUES (22, '待指导教师延期结题审核', '2025-03-12 10:24:32', '2025-03-12 10:24:32', 0);
INSERT INTO `dcstatus` VALUES (23, '待学院延期结题审核', '2025-03-12 10:24:51', '2025-03-12 10:24:51', 0);
INSERT INTO `dcstatus` VALUES (24, '待学校延期结题审核', '2025-03-12 10:25:01', '2025-03-12 10:25:01', 0);
INSERT INTO `dcstatus` VALUES (25, '延期结题待提交结题报告', '2025-03-12 10:25:21', '2025-03-12 10:25:21', 0);
INSERT INTO `dcstatus` VALUES (26, '等待指导教师延期结题审核', '2025-07-15 08:11:14', '2025-07-15 08:11:14', 0);
INSERT INTO `dcstatus` VALUES (27, '等待学院延期结题审核', '2025-07-15 08:11:43', '2025-03-07 19:44:28', 0);
INSERT INTO `dcstatus` VALUES (28, '等待学校延期结题审核', '2025-07-15 08:12:05', '2025-07-15 08:12:05', 0);
INSERT INTO `dcstatus` VALUES (29, '指导教师延期结题驳回', '2025-07-15 08:13:12', '2025-07-15 08:13:12', 0);
INSERT INTO `dcstatus` VALUES (30, '学院延期结题驳回', '2025-07-15 08:13:26', '2025-07-15 08:13:26', 0);
INSERT INTO `dcstatus` VALUES (31, '学校延期结题驳回', '2025-07-15 08:13:36', '2025-07-15 08:13:36', 0);
INSERT INTO `dcstatus` VALUES (32, '延期结题待提交结题报告', '2025-07-15 08:18:23', '2025-07-15 21:35:47', 0);
INSERT INTO `dcstatus` VALUES (33, '等待指导教师中期变更审核', '2025-07-15 08:11:14', '2025-07-15 08:36:51', 0);
INSERT INTO `dcstatus` VALUES (34, '等待学院中期变更审核', '2025-07-15 08:37:00', '2025-07-15 08:37:00', 0);
INSERT INTO `dcstatus` VALUES (35, '等待学校中期变更审核', '2025-07-15 08:37:12', '2025-07-15 08:37:12', 0);
INSERT INTO `dcstatus` VALUES (36, '指导教师中期变更驳回', '2025-07-15 08:37:27', '2025-07-15 08:37:27', 0);
INSERT INTO `dcstatus` VALUES (37, '学院中期变更驳回', '2025-07-15 08:37:38', '2025-07-15 08:37:38', 0);
INSERT INTO `dcstatus` VALUES (38, '学校中期变更驳回', '2025-07-15 08:37:45', '2025-07-15 08:37:45', 0);

-- ----------------------------
-- Table structure for dctype
-- ----------------------------
DROP TABLE IF EXISTS `dctype`;
CREATE TABLE `dctype`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dctype
-- ----------------------------
INSERT INTO `dctype` VALUES (1, '创新训练项目', '2025-03-03 14:24:28', '2025-03-03 14:36:54', 0);
INSERT INTO `dctype` VALUES (2, '创业训练项目', '2025-03-03 14:24:39', '2025-03-03 14:36:54', 0);
INSERT INTO `dctype` VALUES (3, '创业实践项目', '2025-03-03 14:25:01', '2025-03-03 14:36:54', 0);

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 77 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department` VALUES (1, '财务处', '2025-03-20 11:42:44', '2025-07-13 17:33:06', 0);
INSERT INTO `department` VALUES (15, '大学外语教学部', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (16, '党委统战部', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (17, '党委宣传部（网络信息中心）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (18, '党委巡察工作领导小组办公室（巡察办）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (19, '党委组织部（党校、机关党委）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (20, '党政办公室（乡村振兴协调服务办公室）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (21, '发展规划与交流合作处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (22, '法学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (23, '附属学校', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (24, '附属艺术学校', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (25, '工会', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (26, '古生物学院（古生物博物馆）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (27, '管理学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (28, '国际交流合作处（港澳台事务办公室、国际中文教育办公室、国际教育学院）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (29, '国际商学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (30, '后勤工作处（教学场馆管理中心）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (31, '后勤集团', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (32, '化学化工学院（能源与环境催化研究所）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (33, '计算机与数学基础教学部（辽宁省电化教育馆）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (34, '继续教育学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (35, '教师教育学院（辽宁省基础教育教研培训中心）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (36, '教务处（公共艺术教育中心）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (37, '教学质量监控与评估处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (38, '教育经济与管理研究所', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (39, '教育科学学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (40, '教育硕士研究生院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (41, '科研处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (42, '离退休工作处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (43, '粮食学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (44, '辽宁省检察官学院巡视巡察分院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (45, '辽宁省职业教育研究院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (46, '旅游管理学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (47, '马克思主义学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (48, '美术与设计学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (49, '人事处（党委教师工作部）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (50, '软件学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (51, '社会学学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (52, '审查调查室', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (53, '审计处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (54, '生命科学学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (55, '实验教学中心', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (56, '实验教学中心（大学生创新创业中心）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (57, '数学与系统科学学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (58, '体育科学学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (59, '图书馆（档案馆）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (60, '团委', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (61, '外国语学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (62, '文学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (63, '物理科学与技术学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (64, '戏剧艺术学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (65, '校医院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (66, '校园安全与管理处（党委保卫工作部）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (67, '新闻与传播学院（教育技术学院）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (68, '学报编辑部', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (69, '学前与初等教育学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (70, '学生处（党委学生工作部、武装部）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (71, '研究生院（党委研究生工作部）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (72, '音乐学院', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (73, '招生就业指导处', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (74, '中国文化与文学研究所', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (75, '资产管理处（招标采购委员会办公室）', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);
INSERT INTO `department` VALUES (76, '综合监察室', '2025-03-20 11:42:44', '2025-03-20 11:42:44', 0);

-- ----------------------------
-- Table structure for file
-- ----------------------------
DROP TABLE IF EXISTS `file`;
CREATE TABLE `file`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属项目',
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件保存路径',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大创文件所属周期（仅针对大创项目）\r\n1.立项文件\r\n2.中期文件\r\n3.结题文件\r\n4.延期文件',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 76 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of file
-- ----------------------------
INSERT INTO `file` VALUES (73, 'ad39df72-915a-4e9d-9e68-b46e1a68ef28', 'dc/22008027/一轮项目需求整理.pdf', '1', 0, '2025-07-15 11:07:24', NULL);
INSERT INTO `file` VALUES (74, '3537f00b-5842-4120-871a-f52b83f17805', 'dc/22008027/一轮项目需求整理.pdf', '1', 0, '2025-07-15 19:36:51', NULL);
INSERT INTO `file` VALUES (75, '3537f00b-5842-4120-871a-f52b83f17805', 'dc/22008027/Doc1.docx', '3', 0, '2025-07-15 21:42:49', 'Doc1.docx');

-- ----------------------------
-- Table structure for jsaward
-- ----------------------------
DROP TABLE IF EXISTS `jsaward`;
CREATE TABLE `jsaward`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 46 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jsaward
-- ----------------------------
INSERT INTO `jsaward` VALUES (26, '国家级', '特等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (27, '国家级', '一等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (28, '国家级', '二等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (29, '国家级', '三等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (30, '国家级', '优秀奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (31, '省级', '特等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (32, '省级', '一等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (33, '省级', '二等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (34, '省级', '三等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (35, '省级', '优秀奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (36, '校级', '特等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (37, '校级', '一等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (38, '校级', '二等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (39, '校级', '三等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (40, '校级', '优秀奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (41, '院级', '特等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (42, '院级', '一等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (43, '院级', '二等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (44, '院级', '三等奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);
INSERT INTO `jsaward` VALUES (45, '院级', '优秀奖', '2025-03-17 10:15:25', '2025-03-17 10:15:25', 0);

-- ----------------------------
-- Table structure for jscheck
-- ----------------------------
DROP TABLE IF EXISTS `jscheck`;
CREATE TABLE `jscheck`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `type` int(11) NULL DEFAULT NULL COMMENT '类型1教师2学院3学校',
  `check` int(11) NULL DEFAULT NULL COMMENT '0通过1驳回',
  `status` int(11) NULL DEFAULT NULL COMMENT '1立项2中期3结项4延期5变更6证书',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jscheck
-- ----------------------------

-- ----------------------------
-- Table structure for jscompetition
-- ----------------------------
DROP TABLE IF EXISTS `jscompetition`;
CREATE TABLE `jscompetition`  (
  `cuid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目编号',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '竞赛名',
  `level` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对应jslevel',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '竞赛官网链接',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`cuid`, `id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jscompetition
-- ----------------------------

-- ----------------------------
-- Table structure for jslevel
-- ----------------------------
DROP TABLE IF EXISTS `jslevel`;
CREATE TABLE `jslevel`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jslevel
-- ----------------------------
INSERT INTO `jslevel` VALUES (5, '未定级', '2025-03-03 14:24:28', '2025-03-08 08:13:29', 0);
INSERT INTO `jslevel` VALUES (1, '国家级', '2025-03-03 14:24:39', '2025-03-08 08:13:38', 0);
INSERT INTO `jslevel` VALUES (2, '省级', '2025-03-03 14:25:01', '2025-03-08 08:13:42', 0);
INSERT INTO `jslevel` VALUES (3, '校级', '2025-03-08 08:13:47', '2025-03-08 08:14:13', 0);
INSERT INTO `jslevel` VALUES (4, '院级', '2025-03-08 08:14:00', '2025-03-08 08:14:13', 0);

-- ----------------------------
-- Table structure for jsprogress
-- ----------------------------
DROP TABLE IF EXISTS `jsprogress`;
CREATE TABLE `jsprogress`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大创项目编号',
  `action` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件名称',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件详情',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 86 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jsprogress
-- ----------------------------

-- ----------------------------
-- Table structure for jsproject
-- ----------------------------
DROP TABLE IF EXISTS `jsproject`;
CREATE TABLE `jsproject`  (
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目申请编号',
  `cuid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '这是对应的竞赛uid',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `time` date NULL DEFAULT NULL COMMENT '立项时间',
  `introduction` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '项目简介',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(11) NULL DEFAULT NULL COMMENT '项目当前状态，在jsstatus表里面对应',
  `class` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类别，赛道等',
  `certificate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '证书',
  `award` int(11) NULL DEFAULT NULL COMMENT '获奖级别对应jsaward',
  `award_remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`uid`, `id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jsproject
-- ----------------------------

-- ----------------------------
-- Table structure for jssetting
-- ----------------------------
DROP TABLE IF EXISTS `jssetting`;
CREATE TABLE `jssetting`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cuid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `member` int(11) NULL DEFAULT NULL COMMENT '项目支持最多成员（包括负责人）',
  `teacher` int(11) NULL DEFAULT NULL COMMENT '项目支持最多学生',
  `outstu` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否允许校外学生0否1是',
  `outstu_num` int(11) NULL DEFAULT NULL COMMENT '最多允许校外学生数',
  `outtea` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否允许校外教师0否1是',
  `outtea_num` int(11) NULL DEFAULT NULL COMMENT '最多允许校外导师数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `active` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否填报时间0否1是',
  `years` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '当前可填报年份，这里是数组',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jssetting
-- ----------------------------

-- ----------------------------
-- Table structure for jsstatus
-- ----------------------------
DROP TABLE IF EXISTS `jsstatus`;
CREATE TABLE `jsstatus`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jsstatus
-- ----------------------------
INSERT INTO `jsstatus` VALUES (1, '等待指导教师报名审核', '2025-03-07 19:44:28', '2025-03-10 11:43:09', 0);
INSERT INTO `jsstatus` VALUES (2, '等待学院报名审核', '2025-03-07 19:45:06', '2025-03-10 11:43:14', 0);
INSERT INTO `jsstatus` VALUES (3, '等待学校报名审核', '2025-03-07 19:45:23', '2025-03-10 11:43:18', 0);
INSERT INTO `jsstatus` VALUES (4, '等待竞赛结束提交证书', '2025-03-07 19:45:50', '2025-03-10 13:17:41', 0);
INSERT INTO `jsstatus` VALUES (5, '等待指导教师证书审核', '2025-03-07 19:46:17', '2025-03-10 13:17:54', 0);
INSERT INTO `jsstatus` VALUES (6, '等待学院证书审核', '2025-03-07 19:46:28', '2025-03-10 13:18:02', 0);
INSERT INTO `jsstatus` VALUES (14, '学校证书审核驳回', '2025-03-10 13:24:21', '2025-03-10 13:24:21', 0);
INSERT INTO `jsstatus` VALUES (13, '学院证书审核驳回', '2025-03-10 13:24:10', '2025-03-10 13:24:10', 0);
INSERT INTO `jsstatus` VALUES (12, '指导教师证书审核驳回', '2025-03-10 13:24:00', '2025-03-10 13:24:00', 0);
INSERT INTO `jsstatus` VALUES (11, '学校报名审核驳回', '2025-03-10 13:23:43', '2025-03-10 13:23:43', 0);
INSERT INTO `jsstatus` VALUES (10, '学院报名审核驳回', '2025-03-10 13:23:34', '2025-03-10 13:23:34', 0);
INSERT INTO `jsstatus` VALUES (9, '指导教师报名审核驳回', '2025-03-10 13:23:19', '2025-03-10 13:23:25', 0);
INSERT INTO `jsstatus` VALUES (8, '项目完成', '2025-03-10 13:22:05', '2025-03-10 13:22:05', 0);
INSERT INTO `jsstatus` VALUES (7, '等待学校证书审核', '2025-03-10 13:18:12', '2025-03-10 13:18:15', 0);

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `action` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '具体操作内容',
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 9198 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log
-- ----------------------------
INSERT INTO `log` VALUES (8128, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 10:33:50');
INSERT INTO `log` VALUES (8129, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:33:50');
INSERT INTO `log` VALUES (8130, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:33:50');
INSERT INTO `log` VALUES (8131, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:33:50');
INSERT INTO `log` VALUES (8132, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 10:33:56');
INSERT INTO `log` VALUES (8133, 'guest', 'guest', '登录失败，用户名或密码错误', '127.0.0.1', '2025-07-15 10:33:56');
INSERT INTO `log` VALUES (8134, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:34:00');
INSERT INTO `log` VALUES (8135, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 10:35:29');
INSERT INTO `log` VALUES (8136, 'admin', '超级管理员', '登录成功', '127.0.0.1', '2025-07-15 10:35:29');
INSERT INTO `log` VALUES (8137, 'admin', '超级管理员', '进入后台', '127.0.0.1', '2025-07-15 10:35:30');
INSERT INTO `log` VALUES (8138, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 10:35:57');
INSERT INTO `log` VALUES (8139, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:35:57');
INSERT INTO `log` VALUES (8140, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:35:57');
INSERT INTO `log` VALUES (8141, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:35:57');
INSERT INTO `log` VALUES (8142, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 10:35:59');
INSERT INTO `log` VALUES (8143, 'admin', '张三', '登录成功', '127.0.0.1', '2025-07-15 10:35:59');
INSERT INTO `log` VALUES (8144, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:36:00');
INSERT INTO `log` VALUES (8145, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 10:36:05');
INSERT INTO `log` VALUES (8146, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 10:37:07');
INSERT INTO `log` VALUES (8147, 'admin', '张三', '进入大创', '127.0.0.1', '2025-07-15 10:37:08');
INSERT INTO `log` VALUES (8148, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 10:38:02');
INSERT INTO `log` VALUES (8149, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:38:02');
INSERT INTO `log` VALUES (8150, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:38:02');
INSERT INTO `log` VALUES (8151, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:38:02');
INSERT INTO `log` VALUES (8152, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 10:38:09');
INSERT INTO `log` VALUES (8153, '22008027', '宁城嘉', '登录成功', '127.0.0.1', '2025-07-15 10:38:09');
INSERT INTO `log` VALUES (8154, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 10:38:10');
INSERT INTO `log` VALUES (8155, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 10:38:19');
INSERT INTO `log` VALUES (8156, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:41:32');
INSERT INTO `log` VALUES (8157, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:42:01');
INSERT INTO `log` VALUES (8158, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:42:09');
INSERT INTO `log` VALUES (8159, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:42:09');
INSERT INTO `log` VALUES (8160, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:42:09');
INSERT INTO `log` VALUES (8161, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-07-15 10:42:12');
INSERT INTO `log` VALUES (8162, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:43:04');
INSERT INTO `log` VALUES (8163, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:46:39');
INSERT INTO `log` VALUES (8164, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:50:27');
INSERT INTO `log` VALUES (8165, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 10:54:37');
INSERT INTO `log` VALUES (8166, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:54:37');
INSERT INTO `log` VALUES (8167, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:54:38');
INSERT INTO `log` VALUES (8168, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 10:54:38');
INSERT INTO `log` VALUES (8169, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 10:54:39');
INSERT INTO `log` VALUES (8170, 'admin', '张三', '登录成功', '127.0.0.1', '2025-07-15 10:54:39');
INSERT INTO `log` VALUES (8171, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:54:40');
INSERT INTO `log` VALUES (8172, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:57:14');
INSERT INTO `log` VALUES (8173, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 10:58:18');
INSERT INTO `log` VALUES (8174, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 11:05:35');
INSERT INTO `log` VALUES (8175, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:05:35');
INSERT INTO `log` VALUES (8176, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:05:35');
INSERT INTO `log` VALUES (8177, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:05:35');
INSERT INTO `log` VALUES (8178, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 11:05:38');
INSERT INTO `log` VALUES (8179, '22008027', '宁城嘉', '登录成功', '127.0.0.1', '2025-07-15 11:05:38');
INSERT INTO `log` VALUES (8180, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 11:05:39');
INSERT INTO `log` VALUES (8181, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 11:05:42');
INSERT INTO `log` VALUES (8182, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:05:43');
INSERT INTO `log` VALUES (8183, '22008027', '宁城嘉', '进入竞赛管理', '127.0.0.1', '2025-07-15 11:05:45');
INSERT INTO `log` VALUES (8184, '22008027', '宁城嘉', '查看竞赛列表', '127.0.0.1', '2025-07-15 11:05:45');
INSERT INTO `log` VALUES (8185, '22008027', '宁城嘉', '进入竞赛项目列表', '127.0.0.1', '2025-07-15 11:05:46');
INSERT INTO `log` VALUES (8186, '22008027', '宁城嘉', '进入竞赛管理', '127.0.0.1', '2025-07-15 11:05:48');
INSERT INTO `log` VALUES (8187, '22008027', '宁城嘉', '查看竞赛列表', '127.0.0.1', '2025-07-15 11:05:48');
INSERT INTO `log` VALUES (8188, '22008027', '宁城嘉', '进入竞赛项目列表', '127.0.0.1', '2025-07-15 11:05:53');
INSERT INTO `log` VALUES (8189, '22008027', '宁城嘉', '进入竞赛管理', '127.0.0.1', '2025-07-15 11:06:00');
INSERT INTO `log` VALUES (8190, '22008027', '宁城嘉', '查看竞赛列表', '127.0.0.1', '2025-07-15 11:06:00');
INSERT INTO `log` VALUES (8191, '22008027', '宁城嘉', '进入英才库列表', '127.0.0.1', '2025-07-15 11:06:04');
INSERT INTO `log` VALUES (8192, '22008027', '宁城嘉', '22008027进入英才库看22008027', '127.0.0.1', '2025-07-15 11:06:06');
INSERT INTO `log` VALUES (8193, '22008027', '宁城嘉', '22008027进入英才库看22008027用户不是英才', '127.0.0.1', '2025-07-15 11:06:06');
INSERT INTO `log` VALUES (8194, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 11:06:11');
INSERT INTO `log` VALUES (8195, '22008027', '宁城嘉', '22008027修改个人信息失败，插入异常', '127.0.0.1', '2025-07-15 11:06:19');
INSERT INTO `log` VALUES (8196, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 11:06:28');
INSERT INTO `log` VALUES (8197, '22008027', '宁城嘉', '文件上传参数 - class: dc, type: pdf', '127.0.0.1', '2025-07-15 11:07:16');
INSERT INTO `log` VALUES (8198, '22008027', '宁城嘉', '一轮项目需求整理.pdf上传成功至E:\\code\\cxcysys\\public\\static/files/upload/dc/22008027/', '127.0.0.1', '2025-07-15 11:07:16');
INSERT INTO `log` VALUES (8199, '22008027', '宁城嘉', '用户提交项目立项信息', '127.0.0.1', '2025-07-15 11:07:24');
INSERT INTO `log` VALUES (8200, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28立项成功', '127.0.0.1', '2025-07-15 11:07:24');
INSERT INTO `log` VALUES (8201, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:07:25');
INSERT INTO `log` VALUES (8202, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:07:30');
INSERT INTO `log` VALUES (8203, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:09:38');
INSERT INTO `log` VALUES (8204, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:11:37');
INSERT INTO `log` VALUES (8205, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:11:38');
INSERT INTO `log` VALUES (8206, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:13:28');
INSERT INTO `log` VALUES (8207, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 11:13:36');
INSERT INTO `log` VALUES (8208, '22008027', '宁城嘉', '用户修改项目立项信息', '127.0.0.1', '2025-07-15 11:13:48');
INSERT INTO `log` VALUES (8209, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28修改项目成功', '127.0.0.1', '2025-07-15 11:13:48');
INSERT INTO `log` VALUES (8210, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:13:49');
INSERT INTO `log` VALUES (8211, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:13:50');
INSERT INTO `log` VALUES (8212, '22008027', '宁城嘉', '宁城嘉当前不是可填报状态', '127.0.0.1', '2025-07-15 11:14:07');
INSERT INTO `log` VALUES (8213, '22008027', '宁城嘉', '宁城嘉当前不是可填报状态', '127.0.0.1', '2025-07-15 11:14:09');
INSERT INTO `log` VALUES (8214, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 11:14:11');
INSERT INTO `log` VALUES (8215, '22008027', '宁城嘉', '宁城嘉当前不是可申请中期变更状态', '127.0.0.1', '2025-07-15 11:14:12');
INSERT INTO `log` VALUES (8216, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:14:15');
INSERT INTO `log` VALUES (8217, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:14:16');
INSERT INTO `log` VALUES (8218, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:14:29');
INSERT INTO `log` VALUES (8219, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:14:29');
INSERT INTO `log` VALUES (8220, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:14:29');
INSERT INTO `log` VALUES (8221, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 11:14:40');
INSERT INTO `log` VALUES (8222, '220080277', '宁1', '登录成功', '127.0.0.1', '2025-07-15 11:14:40');
INSERT INTO `log` VALUES (8223, '220080277', '宁1', '进入后台', '127.0.0.1', '2025-07-15 11:14:41');
INSERT INTO `log` VALUES (8224, '220080277', '宁1', '进入项目立项页', '127.0.0.1', '2025-07-15 11:14:45');
INSERT INTO `log` VALUES (8225, '220080277', '宁1', '进入大创', '127.0.0.1', '2025-07-15 11:14:46');
INSERT INTO `log` VALUES (8226, '220080277', '宁1', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:14:47');
INSERT INTO `log` VALUES (8227, '220080277', '宁1', '宁1你不是项目的负责人', '127.0.0.1', '2025-07-15 11:15:00');
INSERT INTO `log` VALUES (8228, '220080277', '宁1', '宁1你不是项目的负责人', '127.0.0.1', '2025-07-15 11:15:02');
INSERT INTO `log` VALUES (8229, '220080277', '宁1', '宁1你不是项目的负责人', '127.0.0.1', '2025-07-15 11:15:03');
INSERT INTO `log` VALUES (8230, '220080277', '宁1', '宁1你不是项目的负责人', '127.0.0.1', '2025-07-15 11:15:05');
INSERT INTO `log` VALUES (8231, '220080277', '宁1', '宁1你不是项目的负责人', '127.0.0.1', '2025-07-15 11:15:07');
INSERT INTO `log` VALUES (8232, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:15:15');
INSERT INTO `log` VALUES (8233, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:15:15');
INSERT INTO `log` VALUES (8234, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 11:15:15');
INSERT INTO `log` VALUES (8235, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 11:15:39');
INSERT INTO `log` VALUES (8236, '001', '软件教师', '登录成功', '127.0.0.1', '2025-07-15 11:15:39');
INSERT INTO `log` VALUES (8237, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 11:15:40');
INSERT INTO `log` VALUES (8238, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 11:15:44');
INSERT INTO `log` VALUES (8239, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:15:45');
INSERT INTO `log` VALUES (8240, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 11:15:48');
INSERT INTO `log` VALUES (8241, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:16:12');
INSERT INTO `log` VALUES (8242, '001', '软件教师', '软件教师你不是项目的负责人', '127.0.0.1', '2025-07-15 11:16:16');
INSERT INTO `log` VALUES (8243, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:16:19');
INSERT INTO `log` VALUES (8244, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:16:52');
INSERT INTO `log` VALUES (8245, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:16:55');
INSERT INTO `log` VALUES (8246, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:17:00');
INSERT INTO `log` VALUES (8247, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:17:10');
INSERT INTO `log` VALUES (8248, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:17:18');
INSERT INTO `log` VALUES (8249, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:17:20');
INSERT INTO `log` VALUES (8250, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:21:23');
INSERT INTO `log` VALUES (8251, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:21:32');
INSERT INTO `log` VALUES (8252, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:21:35');
INSERT INTO `log` VALUES (8253, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:21:50');
INSERT INTO `log` VALUES (8254, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:25:24');
INSERT INTO `log` VALUES (8255, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 11:25:37');
INSERT INTO `log` VALUES (8256, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 11:25:40');
INSERT INTO `log` VALUES (8257, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:25:44');
INSERT INTO `log` VALUES (8258, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:25:58');
INSERT INTO `log` VALUES (8259, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:26:24');
INSERT INTO `log` VALUES (8260, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:27:30');
INSERT INTO `log` VALUES (8261, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:27:31');
INSERT INTO `log` VALUES (8262, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 11:27:34');
INSERT INTO `log` VALUES (8263, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 11:27:37');
INSERT INTO `log` VALUES (8264, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 11:27:38');
INSERT INTO `log` VALUES (8265, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:27:39');
INSERT INTO `log` VALUES (8266, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:27:50');
INSERT INTO `log` VALUES (8267, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:28:12');
INSERT INTO `log` VALUES (8268, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:29:11');
INSERT INTO `log` VALUES (8269, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 11:31:34');
INSERT INTO `log` VALUES (8270, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 11:31:36');
INSERT INTO `log` VALUES (8271, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:31:38');
INSERT INTO `log` VALUES (8272, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:32:09');
INSERT INTO `log` VALUES (8273, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:32:40');
INSERT INTO `log` VALUES (8274, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:33:17');
INSERT INTO `log` VALUES (8275, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:33:23');
INSERT INTO `log` VALUES (8276, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:34:00');
INSERT INTO `log` VALUES (8277, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:34:03');
INSERT INTO `log` VALUES (8278, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:36:16');
INSERT INTO `log` VALUES (8279, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:36:25');
INSERT INTO `log` VALUES (8280, '001', '软件教师', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28审核成功', '127.0.0.1', '2025-07-15 11:36:34');
INSERT INTO `log` VALUES (8281, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 11:36:35');
INSERT INTO `log` VALUES (8282, '001', '软件教师', '进入新闻列表', '127.0.0.1', '2025-07-15 12:39:23');
INSERT INTO `log` VALUES (8283, '001', '软件教师', '001不是新闻管理员', '127.0.0.1', '2025-07-15 12:39:23');
INSERT INTO `log` VALUES (8284, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-07-15 12:39:36');
INSERT INTO `log` VALUES (8285, 'admin', '张三', '测试板块1新建新闻板块成功', '127.0.0.1', '2025-07-15 12:39:47');
INSERT INTO `log` VALUES (8286, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 13:05:17');
INSERT INTO `log` VALUES (8287, 'admin', '张三', '进入大创', '127.0.0.1', '2025-07-15 13:05:18');
INSERT INTO `log` VALUES (8288, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-07-15 13:05:21');
INSERT INTO `log` VALUES (8289, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-07-15 13:05:21');
INSERT INTO `log` VALUES (8290, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-07-15 13:05:22');
INSERT INTO `log` VALUES (8291, 'admin', '张三', '进入英才库列表', '127.0.0.1', '2025-07-15 13:05:25');
INSERT INTO `log` VALUES (8292, 'admin', '张三', '进入英才库申请列表', '127.0.0.1', '2025-07-15 13:05:26');
INSERT INTO `log` VALUES (8293, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-07-15 13:05:29');
INSERT INTO `log` VALUES (8294, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:05:31');
INSERT INTO `log` VALUES (8295, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:05:41');
INSERT INTO `log` VALUES (8296, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-07-15 13:05:49');
INSERT INTO `log` VALUES (8297, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-07-15 13:05:50');
INSERT INTO `log` VALUES (8298, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-07-15 13:05:50');
INSERT INTO `log` VALUES (8299, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:06:51');
INSERT INTO `log` VALUES (8300, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:08:46');
INSERT INTO `log` VALUES (8301, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:08:48');
INSERT INTO `log` VALUES (8302, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:08:56');
INSERT INTO `log` VALUES (8303, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:08:57');
INSERT INTO `log` VALUES (8304, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:08:57');
INSERT INTO `log` VALUES (8305, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:10:05');
INSERT INTO `log` VALUES (8306, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:10:06');
INSERT INTO `log` VALUES (8307, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:06');
INSERT INTO `log` VALUES (8308, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:06');
INSERT INTO `log` VALUES (8309, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:10:08');
INSERT INTO `log` VALUES (8310, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:10:09');
INSERT INTO `log` VALUES (8311, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:10:09');
INSERT INTO `log` VALUES (8312, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:09');
INSERT INTO `log` VALUES (8313, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:09');
INSERT INTO `log` VALUES (8314, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:10:10');
INSERT INTO `log` VALUES (8315, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:10:10');
INSERT INTO `log` VALUES (8316, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:10');
INSERT INTO `log` VALUES (8317, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:10');
INSERT INTO `log` VALUES (8318, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:10:11');
INSERT INTO `log` VALUES (8319, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:11');
INSERT INTO `log` VALUES (8320, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:10:11');
INSERT INTO `log` VALUES (8321, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:10:37');
INSERT INTO `log` VALUES (8322, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:12:08');
INSERT INTO `log` VALUES (8323, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:12:09');
INSERT INTO `log` VALUES (8324, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:12:16');
INSERT INTO `log` VALUES (8325, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:12:18');
INSERT INTO `log` VALUES (8326, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:12:27');
INSERT INTO `log` VALUES (8327, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:13:58');
INSERT INTO `log` VALUES (8328, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:14:13');
INSERT INTO `log` VALUES (8329, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:15:03');
INSERT INTO `log` VALUES (8330, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:15:09');
INSERT INTO `log` VALUES (8331, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:15:30');
INSERT INTO `log` VALUES (8332, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:16:07');
INSERT INTO `log` VALUES (8333, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:16:09');
INSERT INTO `log` VALUES (8334, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:16:15');
INSERT INTO `log` VALUES (8335, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:16:19');
INSERT INTO `log` VALUES (8336, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:18:10');
INSERT INTO `log` VALUES (8337, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:18:21');
INSERT INTO `log` VALUES (8338, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:18:33');
INSERT INTO `log` VALUES (8339, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:18:52');
INSERT INTO `log` VALUES (8340, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:18:57');
INSERT INTO `log` VALUES (8341, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:18:58');
INSERT INTO `log` VALUES (8342, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:18:59');
INSERT INTO `log` VALUES (8343, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:20:32');
INSERT INTO `log` VALUES (8344, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:20:34');
INSERT INTO `log` VALUES (8345, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:20:35');
INSERT INTO `log` VALUES (8346, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:20:35');
INSERT INTO `log` VALUES (8347, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:20:41');
INSERT INTO `log` VALUES (8348, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:20:52');
INSERT INTO `log` VALUES (8349, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:20:53');
INSERT INTO `log` VALUES (8350, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:21:09');
INSERT INTO `log` VALUES (8351, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:21:47');
INSERT INTO `log` VALUES (8352, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:21:47');
INSERT INTO `log` VALUES (8353, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:21:47');
INSERT INTO `log` VALUES (8354, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:21:48');
INSERT INTO `log` VALUES (8355, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:21:48');
INSERT INTO `log` VALUES (8356, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:21:48');
INSERT INTO `log` VALUES (8357, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:21:48');
INSERT INTO `log` VALUES (8358, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:21:49');
INSERT INTO `log` VALUES (8359, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-07-15 13:22:25');
INSERT INTO `log` VALUES (8360, 'admin', '张三', '测试板块2新建新闻板块成功', '127.0.0.1', '2025-07-15 13:22:35');
INSERT INTO `log` VALUES (8361, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:22:36');
INSERT INTO `log` VALUES (8362, 'admin', '张三', '进入新闻编辑页新增新闻', '127.0.0.1', '2025-07-15 13:22:37');
INSERT INTO `log` VALUES (8363, 'admin', '张三', '新增新闻测试板块1新闻', '127.0.0.1', '2025-07-15 13:22:51');
INSERT INTO `log` VALUES (8364, 'admin', '张三', '新增新闻测试板块1新闻成功', '127.0.0.1', '2025-07-15 13:22:51');
INSERT INTO `log` VALUES (8365, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:22:52');
INSERT INTO `log` VALUES (8366, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:22:54');
INSERT INTO `log` VALUES (8367, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:23:01');
INSERT INTO `log` VALUES (8368, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:23:01');
INSERT INTO `log` VALUES (8369, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:23:01');
INSERT INTO `log` VALUES (8370, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:23:04');
INSERT INTO `log` VALUES (8371, 'admin', '张三', '进入新闻编辑页新增新闻', '127.0.0.1', '2025-07-15 13:23:05');
INSERT INTO `log` VALUES (8372, 'admin', '张三', '新增新闻测试新闻板块2', '127.0.0.1', '2025-07-15 13:23:18');
INSERT INTO `log` VALUES (8373, 'admin', '张三', '新增新闻测试新闻板块2成功', '127.0.0.1', '2025-07-15 13:23:18');
INSERT INTO `log` VALUES (8374, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-07-15 13:23:19');
INSERT INTO `log` VALUES (8375, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:23:21');
INSERT INTO `log` VALUES (8376, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:23:21');
INSERT INTO `log` VALUES (8377, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:23:21');
INSERT INTO `log` VALUES (8378, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:23:27');
INSERT INTO `log` VALUES (8379, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:24:55');
INSERT INTO `log` VALUES (8380, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:24:55');
INSERT INTO `log` VALUES (8381, '220080277', '宁1', '进入后台', '127.0.0.1', '2025-07-15 13:25:01');
INSERT INTO `log` VALUES (8382, '220080277', '宁1', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:25:01');
INSERT INTO `log` VALUES (8383, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:28:33');
INSERT INTO `log` VALUES (8384, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 13:28:46');
INSERT INTO `log` VALUES (8385, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:28:47');
INSERT INTO `log` VALUES (8386, '220080277', '宁1', '进入后台', '127.0.0.1', '2025-07-15 13:28:51');
INSERT INTO `log` VALUES (8387, '220080277', '宁1', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:28:52');
INSERT INTO `log` VALUES (8388, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:28:55');
INSERT INTO `log` VALUES (8389, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:28:55');
INSERT INTO `log` VALUES (8390, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:30:05');
INSERT INTO `log` VALUES (8391, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:31:17');
INSERT INTO `log` VALUES (8392, 'admin', '张三', '进入后台', '127.0.0.1', '2025-07-15 13:31:38');
INSERT INTO `log` VALUES (8393, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:31:38');
INSERT INTO `log` VALUES (8394, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:31:54');
INSERT INTO `log` VALUES (8395, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:31:55');
INSERT INTO `log` VALUES (8396, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:31:55');
INSERT INTO `log` VALUES (8397, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 13:32:07');
INSERT INTO `log` VALUES (8398, 'rj001', '软件管理', '登录成功', '127.0.0.1', '2025-07-15 13:32:08');
INSERT INTO `log` VALUES (8399, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:32:09');
INSERT INTO `log` VALUES (8400, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:32:09');
INSERT INTO `log` VALUES (8401, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:32:15');
INSERT INTO `log` VALUES (8402, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:32:19');
INSERT INTO `log` VALUES (8403, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:32:20');
INSERT INTO `log` VALUES (8404, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:33:07');
INSERT INTO `log` VALUES (8405, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:33:07');
INSERT INTO `log` VALUES (8406, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:33:07');
INSERT INTO `log` VALUES (8407, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 13:33:17');
INSERT INTO `log` VALUES (8408, 'dc001', '大创校级管理', '登录成功', '127.0.0.1', '2025-07-15 13:33:17');
INSERT INTO `log` VALUES (8409, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:33:18');
INSERT INTO `log` VALUES (8410, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:33:18');
INSERT INTO `log` VALUES (8411, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:33:27');
INSERT INTO `log` VALUES (8412, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:33:42');
INSERT INTO `log` VALUES (8413, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:33:47');
INSERT INTO `log` VALUES (8414, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:33:58');
INSERT INTO `log` VALUES (8415, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:35:39');
INSERT INTO `log` VALUES (8416, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:35:39');
INSERT INTO `log` VALUES (8417, '220080277', '宁1', '进入后台', '127.0.0.1', '2025-07-15 13:35:42');
INSERT INTO `log` VALUES (8418, '220080277', '宁1', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:35:43');
INSERT INTO `log` VALUES (8419, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:35:46');
INSERT INTO `log` VALUES (8420, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:35:46');
INSERT INTO `log` VALUES (8421, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:35:49');
INSERT INTO `log` VALUES (8422, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:35:49');
INSERT INTO `log` VALUES (8423, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:35:53');
INSERT INTO `log` VALUES (8424, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:35:53');
INSERT INTO `log` VALUES (8425, '220080277', '宁1', '进入后台', '127.0.0.1', '2025-07-15 13:38:33');
INSERT INTO `log` VALUES (8426, '220080277', '宁1', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:38:33');
INSERT INTO `log` VALUES (8427, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:38:35');
INSERT INTO `log` VALUES (8428, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:38:35');
INSERT INTO `log` VALUES (8429, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:38:44');
INSERT INTO `log` VALUES (8430, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:38:49');
INSERT INTO `log` VALUES (8431, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:38:51');
INSERT INTO `log` VALUES (8432, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:38:59');
INSERT INTO `log` VALUES (8433, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:38:59');
INSERT INTO `log` VALUES (8434, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:39:14');
INSERT INTO `log` VALUES (8435, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:39:14');
INSERT INTO `log` VALUES (8436, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:39:20');
INSERT INTO `log` VALUES (8437, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:39:21');
INSERT INTO `log` VALUES (8438, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:41:06');
INSERT INTO `log` VALUES (8439, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:41:06');
INSERT INTO `log` VALUES (8440, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:41:08');
INSERT INTO `log` VALUES (8441, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:41:08');
INSERT INTO `log` VALUES (8442, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:41:08');
INSERT INTO `log` VALUES (8443, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:41:08');
INSERT INTO `log` VALUES (8444, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:41:27');
INSERT INTO `log` VALUES (8445, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:41:27');
INSERT INTO `log` VALUES (8446, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:45:59');
INSERT INTO `log` VALUES (8447, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:45:59');
INSERT INTO `log` VALUES (8448, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 13:46:08');
INSERT INTO `log` VALUES (8449, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:46:08');
INSERT INTO `log` VALUES (8450, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 13:46:12');
INSERT INTO `log` VALUES (8451, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:46:13');
INSERT INTO `log` VALUES (8452, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:46:13');
INSERT INTO `log` VALUES (8453, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 13:46:21');
INSERT INTO `log` VALUES (8454, '22008027', '宁城嘉', '用户修改项目立项信息', '127.0.0.1', '2025-07-15 13:46:27');
INSERT INTO `log` VALUES (8455, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28修改项目成功', '127.0.0.1', '2025-07-15 13:46:27');
INSERT INTO `log` VALUES (8456, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:46:29');
INSERT INTO `log` VALUES (8457, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:46:30');
INSERT INTO `log` VALUES (8458, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:46:36');
INSERT INTO `log` VALUES (8459, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:46:37');
INSERT INTO `log` VALUES (8460, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 13:46:39');
INSERT INTO `log` VALUES (8461, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:46:41');
INSERT INTO `log` VALUES (8462, '001', '软件教师', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28审核成功', '127.0.0.1', '2025-07-15 13:46:48');
INSERT INTO `log` VALUES (8463, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:46:49');
INSERT INTO `log` VALUES (8464, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:46:59');
INSERT INTO `log` VALUES (8465, '001', '软件教师', '进入新闻列表', '127.0.0.1', '2025-07-15 13:47:04');
INSERT INTO `log` VALUES (8466, '001', '软件教师', '001不是新闻管理员', '127.0.0.1', '2025-07-15 13:47:05');
INSERT INTO `log` VALUES (8467, '001', '软件教师', '001进入英才库看001', '127.0.0.1', '2025-07-15 13:47:08');
INSERT INTO `log` VALUES (8468, '001', '软件教师', '001进入英才库看001用户不是英才', '127.0.0.1', '2025-07-15 13:47:08');
INSERT INTO `log` VALUES (8469, '001', '软件教师', '进入英才库列表', '127.0.0.1', '2025-07-15 13:47:09');
INSERT INTO `log` VALUES (8470, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:47:17');
INSERT INTO `log` VALUES (8471, '220080277', '宁1', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:47:57');
INSERT INTO `log` VALUES (8472, '220080277', '宁1', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:47:57');
INSERT INTO `log` VALUES (8473, '220080277', '宁1', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:47:57');
INSERT INTO `log` VALUES (8474, '001', '软件教师', '开始获取前台轮播数据', '127.0.0.1', '2025-07-15 13:48:02');
INSERT INTO `log` VALUES (8475, '001', '软件教师', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:48:02');
INSERT INTO `log` VALUES (8476, '001', '软件教师', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-15 13:48:02');
INSERT INTO `log` VALUES (8477, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 13:48:17');
INSERT INTO `log` VALUES (8478, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:48:21');
INSERT INTO `log` VALUES (8479, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:48:41');
INSERT INTO `log` VALUES (8480, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:48:41');
INSERT INTO `log` VALUES (8481, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:48:42');
INSERT INTO `log` VALUES (8482, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:48:49');
INSERT INTO `log` VALUES (8483, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:49:43');
INSERT INTO `log` VALUES (8484, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:49:43');
INSERT INTO `log` VALUES (8485, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:49:47');
INSERT INTO `log` VALUES (8486, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:49:47');
INSERT INTO `log` VALUES (8487, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:49:50');
INSERT INTO `log` VALUES (8488, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:49:50');
INSERT INTO `log` VALUES (8489, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:49:50');
INSERT INTO `log` VALUES (8490, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:49:51');
INSERT INTO `log` VALUES (8491, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:49:51');
INSERT INTO `log` VALUES (8492, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:49:51');
INSERT INTO `log` VALUES (8493, '001', '软件教师', '进入新闻列表', '127.0.0.1', '2025-07-15 13:49:53');
INSERT INTO `log` VALUES (8494, '001', '软件教师', '001不是新闻管理员', '127.0.0.1', '2025-07-15 13:49:53');
INSERT INTO `log` VALUES (8495, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:50:35');
INSERT INTO `log` VALUES (8496, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:50:35');
INSERT INTO `log` VALUES (8497, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:50:36');
INSERT INTO `log` VALUES (8498, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:50:36');
INSERT INTO `log` VALUES (8499, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:50:42');
INSERT INTO `log` VALUES (8500, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:50:42');
INSERT INTO `log` VALUES (8501, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:50:43');
INSERT INTO `log` VALUES (8502, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:50:43');
INSERT INTO `log` VALUES (8503, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:50:43');
INSERT INTO `log` VALUES (8504, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:50:43');
INSERT INTO `log` VALUES (8505, '001', '软件教师', '进入新闻列表', '127.0.0.1', '2025-07-15 13:50:44');
INSERT INTO `log` VALUES (8506, '001', '软件教师', '001不是新闻管理员', '127.0.0.1', '2025-07-15 13:50:44');
INSERT INTO `log` VALUES (8507, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 13:51:08');
INSERT INTO `log` VALUES (8508, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:51:08');
INSERT INTO `log` VALUES (8509, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:51:10');
INSERT INTO `log` VALUES (8510, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:51:10');
INSERT INTO `log` VALUES (8511, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 13:51:14');
INSERT INTO `log` VALUES (8512, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:51:14');
INSERT INTO `log` VALUES (8513, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:53:26');
INSERT INTO `log` VALUES (8514, 'rj001', '软件管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:53:27');
INSERT INTO `log` VALUES (8515, 'rj001', '软件管理', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28审核成功', '127.0.0.1', '2025-07-15 13:53:38');
INSERT INTO `log` VALUES (8516, 'rj001', '软件管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:53:39');
INSERT INTO `log` VALUES (8517, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:53:43');
INSERT INTO `log` VALUES (8518, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 13:53:46');
INSERT INTO `log` VALUES (8519, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:53:47');
INSERT INTO `log` VALUES (8520, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:53:48');
INSERT INTO `log` VALUES (8521, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 13:53:52');
INSERT INTO `log` VALUES (8522, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 13:53:54');
INSERT INTO `log` VALUES (8523, '22008027', '宁城嘉', '用户修改项目立项信息', '127.0.0.1', '2025-07-15 13:53:57');
INSERT INTO `log` VALUES (8524, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28修改项目成功', '127.0.0.1', '2025-07-15 13:53:57');
INSERT INTO `log` VALUES (8525, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:53:58');
INSERT INTO `log` VALUES (8526, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:54:00');
INSERT INTO `log` VALUES (8527, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 13:54:05');
INSERT INTO `log` VALUES (8528, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:54:05');
INSERT INTO `log` VALUES (8529, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:54:08');
INSERT INTO `log` VALUES (8530, 'rj001', '软件管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:54:19');
INSERT INTO `log` VALUES (8531, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 13:54:20');
INSERT INTO `log` VALUES (8532, 'rj001', '软件管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:54:21');
INSERT INTO `log` VALUES (8533, 'rj001', '软件管理', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28审核成功', '127.0.0.1', '2025-07-15 13:54:32');
INSERT INTO `log` VALUES (8534, 'rj001', '软件管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:54:33');
INSERT INTO `log` VALUES (8535, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:54:45');
INSERT INTO `log` VALUES (8536, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:55:04');
INSERT INTO `log` VALUES (8537, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 13:55:10');
INSERT INTO `log` VALUES (8538, 'dc001', '大创校级管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:55:12');
INSERT INTO `log` VALUES (8539, 'dc001', '大创校级管理', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28审核成功', '127.0.0.1', '2025-07-15 13:55:24');
INSERT INTO `log` VALUES (8540, 'dc001', '大创校级管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:55:25');
INSERT INTO `log` VALUES (8541, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:55:28');
INSERT INTO `log` VALUES (8542, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:55:28');
INSERT INTO `log` VALUES (8543, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:55:29');
INSERT INTO `log` VALUES (8544, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 13:55:32');
INSERT INTO `log` VALUES (8545, '22008027', '宁城嘉', '用户修改项目立项信息', '127.0.0.1', '2025-07-15 13:55:34');
INSERT INTO `log` VALUES (8546, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28修改项目成功', '127.0.0.1', '2025-07-15 13:55:34');
INSERT INTO `log` VALUES (8547, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:55:35');
INSERT INTO `log` VALUES (8548, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:55:36');
INSERT INTO `log` VALUES (8549, '22008027', '宁城嘉', '进入项目修改页', '127.0.0.1', '2025-07-15 13:55:46');
INSERT INTO `log` VALUES (8550, '22008027', '宁城嘉', '用户修改项目立项信息', '127.0.0.1', '2025-07-15 13:55:48');
INSERT INTO `log` VALUES (8551, '22008027', '宁城嘉', '当前不是可修改立项信息状态', '127.0.0.1', '2025-07-15 13:55:48');
INSERT INTO `log` VALUES (8552, 'dc001', '大创校级管理', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:56:10');
INSERT INTO `log` VALUES (8553, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 13:56:37');
INSERT INTO `log` VALUES (8554, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:56:37');
INSERT INTO `log` VALUES (8555, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 13:56:39');
INSERT INTO `log` VALUES (8556, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 13:56:39');
INSERT INTO `log` VALUES (8557, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:56:41');
INSERT INTO `log` VALUES (8558, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:57:09');
INSERT INTO `log` VALUES (8559, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:57:09');
INSERT INTO `log` VALUES (8560, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:57:09');
INSERT INTO `log` VALUES (8561, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 13:57:15');
INSERT INTO `log` VALUES (8562, 'guest', 'guest', '登录失败，用户名或密码错误', '127.0.0.1', '2025-07-15 13:57:15');
INSERT INTO `log` VALUES (8563, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 13:57:24');
INSERT INTO `log` VALUES (8564, '002', '财务处教师', '登录成功', '127.0.0.1', '2025-07-15 13:57:24');
INSERT INTO `log` VALUES (8565, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 13:57:25');
INSERT INTO `log` VALUES (8566, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:57:25');
INSERT INTO `log` VALUES (8567, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 13:57:28');
INSERT INTO `log` VALUES (8568, '002', '财务处教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 13:57:38');
INSERT INTO `log` VALUES (8569, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:58:19');
INSERT INTO `log` VALUES (8570, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:58:19');
INSERT INTO `log` VALUES (8571, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 13:58:20');
INSERT INTO `log` VALUES (8572, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 13:59:40');
INSERT INTO `log` VALUES (8573, 'cw001', '财务管理', '登录成功', '127.0.0.1', '2025-07-15 13:59:40');
INSERT INTO `log` VALUES (8574, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 13:59:41');
INSERT INTO `log` VALUES (8575, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 13:59:42');
INSERT INTO `log` VALUES (8576, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 13:59:52');
INSERT INTO `log` VALUES (8577, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:00:21');
INSERT INTO `log` VALUES (8578, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:00:22');
INSERT INTO `log` VALUES (8579, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 14:00:52');
INSERT INTO `log` VALUES (8580, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:00:52');
INSERT INTO `log` VALUES (8581, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:00:52');
INSERT INTO `log` VALUES (8582, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:00:52');
INSERT INTO `log` VALUES (8583, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 14:00:54');
INSERT INTO `log` VALUES (8584, 'cw001', '财务管理', '登录成功', '127.0.0.1', '2025-07-15 14:00:54');
INSERT INTO `log` VALUES (8585, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:00:55');
INSERT INTO `log` VALUES (8586, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:00:55');
INSERT INTO `log` VALUES (8587, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:01');
INSERT INTO `log` VALUES (8588, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:04');
INSERT INTO `log` VALUES (8589, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 14:01:25');
INSERT INTO `log` VALUES (8590, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:01:47');
INSERT INTO `log` VALUES (8591, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:47');
INSERT INTO `log` VALUES (8592, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:01:48');
INSERT INTO `log` VALUES (8593, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:48');
INSERT INTO `log` VALUES (8594, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:01:52');
INSERT INTO `log` VALUES (8595, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:53');
INSERT INTO `log` VALUES (8596, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 14:01:54');
INSERT INTO `log` VALUES (8597, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:01:59');
INSERT INTO `log` VALUES (8598, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 14:02:01');
INSERT INTO `log` VALUES (8599, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:04');
INSERT INTO `log` VALUES (8600, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:04');
INSERT INTO `log` VALUES (8601, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 14:02:05');
INSERT INTO `log` VALUES (8602, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:10');
INSERT INTO `log` VALUES (8603, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:10');
INSERT INTO `log` VALUES (8604, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:11');
INSERT INTO `log` VALUES (8605, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:11');
INSERT INTO `log` VALUES (8606, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:14');
INSERT INTO `log` VALUES (8607, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:14');
INSERT INTO `log` VALUES (8608, 'guest', 'guest', '用户主动退出登录', '127.0.0.1', '2025-07-15 14:02:20');
INSERT INTO `log` VALUES (8609, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:02:20');
INSERT INTO `log` VALUES (8610, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:02:20');
INSERT INTO `log` VALUES (8611, 'guest', 'guest', '进入登录页', '127.0.0.1', '2025-07-15 14:02:20');
INSERT INTO `log` VALUES (8612, 'guest', 'guest', '点击登录按钮', '127.0.0.1', '2025-07-15 14:02:22');
INSERT INTO `log` VALUES (8613, 'cw001', '财务管理', '登录成功', '127.0.0.1', '2025-07-15 14:02:22');
INSERT INTO `log` VALUES (8614, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:23');
INSERT INTO `log` VALUES (8615, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:23');
INSERT INTO `log` VALUES (8616, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 14:02:27');
INSERT INTO `log` VALUES (8617, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:28');
INSERT INTO `log` VALUES (8618, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:31');
INSERT INTO `log` VALUES (8619, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:32');
INSERT INTO `log` VALUES (8620, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 14:02:33');
INSERT INTO `log` VALUES (8621, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:37');
INSERT INTO `log` VALUES (8622, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:37');
INSERT INTO `log` VALUES (8623, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8624, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8625, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8626, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8627, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8628, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:38');
INSERT INTO `log` VALUES (8629, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 14:02:40');
INSERT INTO `log` VALUES (8630, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:42');
INSERT INTO `log` VALUES (8631, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:42');
INSERT INTO `log` VALUES (8632, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:43');
INSERT INTO `log` VALUES (8633, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:43');
INSERT INTO `log` VALUES (8634, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:02:46');
INSERT INTO `log` VALUES (8635, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:02:46');
INSERT INTO `log` VALUES (8636, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 14:02:49');
INSERT INTO `log` VALUES (8637, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 14:08:39');
INSERT INTO `log` VALUES (8638, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:08:39');
INSERT INTO `log` VALUES (8639, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 14:08:42');
INSERT INTO `log` VALUES (8640, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:08:46');
INSERT INTO `log` VALUES (8641, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:08:46');
INSERT INTO `log` VALUES (8642, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 14:08:48');
INSERT INTO `log` VALUES (8643, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:08:51');
INSERT INTO `log` VALUES (8644, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 14:08:54');
INSERT INTO `log` VALUES (8645, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:08:59');
INSERT INTO `log` VALUES (8646, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:08:59');
INSERT INTO `log` VALUES (8647, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 14:09:02');
INSERT INTO `log` VALUES (8648, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:09:04');
INSERT INTO `log` VALUES (8649, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:09:10');
INSERT INTO `log` VALUES (8650, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 14:09:12');
INSERT INTO `log` VALUES (8651, '22008027', '宁城嘉', '宁城嘉中期变更申请提交成功', '127.0.0.1', '2025-07-15 14:09:36');
INSERT INTO `log` VALUES (8652, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:09:43');
INSERT INTO `log` VALUES (8653, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:14:33');
INSERT INTO `log` VALUES (8654, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:19:48');
INSERT INTO `log` VALUES (8655, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:19:50');
INSERT INTO `log` VALUES (8656, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 14:19:51');
INSERT INTO `log` VALUES (8657, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:19:51');
INSERT INTO `log` VALUES (8658, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 14:19:54');
INSERT INTO `log` VALUES (8659, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 14:19:54');
INSERT INTO `log` VALUES (8660, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:19:56');
INSERT INTO `log` VALUES (8661, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:20:22');
INSERT INTO `log` VALUES (8662, '22008027', '宁城嘉', '宁城嘉中期变更申请修改失败：中期变更申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 14:20:48');
INSERT INTO `log` VALUES (8663, '22008027', '宁城嘉', '宁城嘉当前不是可申请中期变更状态', '127.0.0.1', '2025-07-15 14:20:58');
INSERT INTO `log` VALUES (8664, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:21:06');
INSERT INTO `log` VALUES (8665, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:21:25');
INSERT INTO `log` VALUES (8666, '22008027', '宁城嘉', '宁城嘉中期变更申请提交成功', '127.0.0.1', '2025-07-15 14:21:41');
INSERT INTO `log` VALUES (8667, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:21:47');
INSERT INTO `log` VALUES (8668, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:24:05');
INSERT INTO `log` VALUES (8669, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:24:06');
INSERT INTO `log` VALUES (8670, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:24:07');
INSERT INTO `log` VALUES (8671, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:24:07');
INSERT INTO `log` VALUES (8672, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 14:24:09');
INSERT INTO `log` VALUES (8673, '001', '软件教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:24:12');
INSERT INTO `log` VALUES (8674, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 14:24:19');
INSERT INTO `log` VALUES (8675, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:24:19');
INSERT INTO `log` VALUES (8676, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:24:23');
INSERT INTO `log` VALUES (8677, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:24:24');
INSERT INTO `log` VALUES (8678, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:29:44');
INSERT INTO `log` VALUES (8679, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:29:50');
INSERT INTO `log` VALUES (8680, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:29:51');
INSERT INTO `log` VALUES (8681, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 14:29:54');
INSERT INTO `log` VALUES (8682, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 14:30:01');
INSERT INTO `log` VALUES (8683, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:30:01');
INSERT INTO `log` VALUES (8684, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:30:04');
INSERT INTO `log` VALUES (8685, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:30:04');
INSERT INTO `log` VALUES (8686, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:30:05');
INSERT INTO `log` VALUES (8687, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:30:05');
INSERT INTO `log` VALUES (8688, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 14:30:07');
INSERT INTO `log` VALUES (8689, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:33:16');
INSERT INTO `log` VALUES (8690, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:16');
INSERT INTO `log` VALUES (8691, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:33:17');
INSERT INTO `log` VALUES (8692, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:17');
INSERT INTO `log` VALUES (8693, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 14:33:18');
INSERT INTO `log` VALUES (8694, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:18');
INSERT INTO `log` VALUES (8695, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:33:20');
INSERT INTO `log` VALUES (8696, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:20');
INSERT INTO `log` VALUES (8697, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:33:21');
INSERT INTO `log` VALUES (8698, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:21');
INSERT INTO `log` VALUES (8699, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:33:21');
INSERT INTO `log` VALUES (8700, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:21');
INSERT INTO `log` VALUES (8701, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 14:33:22');
INSERT INTO `log` VALUES (8702, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:22');
INSERT INTO `log` VALUES (8703, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 14:33:25');
INSERT INTO `log` VALUES (8704, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:33:25');
INSERT INTO `log` VALUES (8705, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 14:33:27');
INSERT INTO `log` VALUES (8706, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:36:43');
INSERT INTO `log` VALUES (8707, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:37:04');
INSERT INTO `log` VALUES (8708, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:37:11');
INSERT INTO `log` VALUES (8709, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:39:36');
INSERT INTO `log` VALUES (8710, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:40:47');
INSERT INTO `log` VALUES (8711, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:42:48');
INSERT INTO `log` VALUES (8712, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28中期变更数据不存在，无法更新项目数据', '127.0.0.1', '2025-07-15 14:42:48');
INSERT INTO `log` VALUES (8713, '22008027', '宁城嘉', '宁城嘉中期变更申请提交失败：SQLSTATE[42S22]: Column not found: 1054 Unknown column \'old_members\' in \'field list\'', '127.0.0.1', '2025-07-15 14:43:15');
INSERT INTO `log` VALUES (8714, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28中期变更数据不存在，无法更新项目数据', '127.0.0.1', '2025-07-15 14:43:15');
INSERT INTO `log` VALUES (8715, '22008027', '宁城嘉', '宁城嘉中期变更申请提交成功', '127.0.0.1', '2025-07-15 14:44:30');
INSERT INTO `log` VALUES (8716, '22008027', '宁城嘉', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28中期变更数据更新成功', '127.0.0.1', '2025-07-15 14:44:30');
INSERT INTO `log` VALUES (8717, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:44:37');
INSERT INTO `log` VALUES (8718, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:50:51');
INSERT INTO `log` VALUES (8719, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 14:50:55');
INSERT INTO `log` VALUES (8720, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:50:55');
INSERT INTO `log` VALUES (8721, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 14:50:57');
INSERT INTO `log` VALUES (8722, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 14:50:58');
INSERT INTO `log` VALUES (8723, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:50:59');
INSERT INTO `log` VALUES (8724, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:52:31');
INSERT INTO `log` VALUES (8725, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:54:37');
INSERT INTO `log` VALUES (8726, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:54:41');
INSERT INTO `log` VALUES (8727, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 14:54:42');
INSERT INTO `log` VALUES (8728, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:54:42');
INSERT INTO `log` VALUES (8729, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 14:54:44');
INSERT INTO `log` VALUES (8730, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:54:45');
INSERT INTO `log` VALUES (8731, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:57:55');
INSERT INTO `log` VALUES (8732, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 14:57:58');
INSERT INTO `log` VALUES (8733, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 14:57:58');
INSERT INTO `log` VALUES (8734, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 14:58:01');
INSERT INTO `log` VALUES (8735, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 14:58:01');
INSERT INTO `log` VALUES (8736, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 14:58:03');
INSERT INTO `log` VALUES (8737, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 15:01:48');
INSERT INTO `log` VALUES (8738, '22008027', '宁城嘉', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 15:04:09');
INSERT INTO `log` VALUES (8739, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 15:04:24');
INSERT INTO `log` VALUES (8740, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 15:04:24');
INSERT INTO `log` VALUES (8741, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 15:04:26');
INSERT INTO `log` VALUES (8742, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 15:04:28');
INSERT INTO `log` VALUES (8743, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 15:04:29');
INSERT INTO `log` VALUES (8744, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 15:04:29');
INSERT INTO `log` VALUES (8745, '001', '软件教师', '进入竞赛管理', '127.0.0.1', '2025-07-15 15:04:35');
INSERT INTO `log` VALUES (8746, '001', '软件教师', '查看竞赛列表', '127.0.0.1', '2025-07-15 15:04:35');
INSERT INTO `log` VALUES (8747, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 15:04:35');
INSERT INTO `log` VALUES (8748, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 15:04:56');
INSERT INTO `log` VALUES (8749, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 15:04:56');
INSERT INTO `log` VALUES (8750, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 15:04:58');
INSERT INTO `log` VALUES (8751, '002', '财务处教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 15:05:00');
INSERT INTO `log` VALUES (8752, '002', '财务处教师', 'ad39df72-915a-4e9d-9e68-b46e1a68ef28中期变更驳回恢复失败：SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry \'445\' for key \'PRIMARY\'', '127.0.0.1', '2025-07-15 15:05:09');
INSERT INTO `log` VALUES (8753, '002', '财务处教师', '用户尝试审核无权限的项目：ad39df72-915a-4e9d-9e68-b46e1a68ef28', '127.0.0.1', '2025-07-15 15:05:15');
INSERT INTO `log` VALUES (8754, '002', '财务处教师', '查看ad39df72-915a-4e9d-9e68-b46e1a68ef28项目详情', '127.0.0.1', '2025-07-15 15:05:20');
INSERT INTO `log` VALUES (8755, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 19:33:53');
INSERT INTO `log` VALUES (8756, 'admin', '张三', '进入大创', '127.0.0.1', '2025-07-15 19:33:53');
INSERT INTO `log` VALUES (8757, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:34:01');
INSERT INTO `log` VALUES (8758, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:34:02');
INSERT INTO `log` VALUES (8759, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:34:03');
INSERT INTO `log` VALUES (8760, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:34:28');
INSERT INTO `log` VALUES (8761, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 19:34:33');
INSERT INTO `log` VALUES (8762, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:34:34');
INSERT INTO `log` VALUES (8763, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 19:34:46');
INSERT INTO `log` VALUES (8764, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 19:34:50');
INSERT INTO `log` VALUES (8765, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 19:36:08');
INSERT INTO `log` VALUES (8766, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:36:27');
INSERT INTO `log` VALUES (8767, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:36:27');
INSERT INTO `log` VALUES (8768, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:36:28');
INSERT INTO `log` VALUES (8769, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:36:28');
INSERT INTO `log` VALUES (8770, '22008027', '宁城嘉', '文件上传参数 - class: dc, type: pdf', '127.0.0.1', '2025-07-15 19:36:48');
INSERT INTO `log` VALUES (8771, '22008027', '宁城嘉', '一轮项目需求整理.pdf上传成功至E:\\code\\cxcysys\\public\\static/files/upload/dc/22008027/', '127.0.0.1', '2025-07-15 19:36:48');
INSERT INTO `log` VALUES (8772, '22008027', '宁城嘉', '用户提交项目立项信息', '127.0.0.1', '2025-07-15 19:36:51');
INSERT INTO `log` VALUES (8773, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805立项成功', '127.0.0.1', '2025-07-15 19:36:51');
INSERT INTO `log` VALUES (8774, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:36:52');
INSERT INTO `log` VALUES (8775, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:37:01');
INSERT INTO `log` VALUES (8776, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 19:37:03');
INSERT INTO `log` VALUES (8777, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:37:04');
INSERT INTO `log` VALUES (8778, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 19:37:06');
INSERT INTO `log` VALUES (8779, '001', '软件教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:08');
INSERT INTO `log` VALUES (8780, '001', '软件教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 19:37:14');
INSERT INTO `log` VALUES (8781, '001', '软件教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:15');
INSERT INTO `log` VALUES (8782, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 19:37:19');
INSERT INTO `log` VALUES (8783, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:23');
INSERT INTO `log` VALUES (8784, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 19:37:33');
INSERT INTO `log` VALUES (8785, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:34');
INSERT INTO `log` VALUES (8786, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 19:37:38');
INSERT INTO `log` VALUES (8787, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:40');
INSERT INTO `log` VALUES (8788, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据不存在，无法更新项目数据', '127.0.0.1', '2025-07-15 19:37:55');
INSERT INTO `log` VALUES (8789, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 19:37:55');
INSERT INTO `log` VALUES (8790, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:37:56');
INSERT INTO `log` VALUES (8791, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:38:02');
INSERT INTO `log` VALUES (8792, '22008027', '宁城嘉', '宁城嘉中期变更申请提交成功', '127.0.0.1', '2025-07-15 19:38:28');
INSERT INTO `log` VALUES (8793, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 19:38:28');
INSERT INTO `log` VALUES (8794, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:38:31');
INSERT INTO `log` VALUES (8795, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:38:42');
INSERT INTO `log` VALUES (8796, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:38:42');
INSERT INTO `log` VALUES (8797, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:38:43');
INSERT INTO `log` VALUES (8798, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:38:43');
INSERT INTO `log` VALUES (8799, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 19:38:45');
INSERT INTO `log` VALUES (8800, '001', '软件教师', '进入竞赛管理', '127.0.0.1', '2025-07-15 19:38:47');
INSERT INTO `log` VALUES (8801, '001', '软件教师', '查看竞赛列表', '127.0.0.1', '2025-07-15 19:38:47');
INSERT INTO `log` VALUES (8802, '001', '软件教师', '进入竞赛项目列表', '127.0.0.1', '2025-07-15 19:38:48');
INSERT INTO `log` VALUES (8803, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:38:50');
INSERT INTO `log` VALUES (8804, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 19:39:57');
INSERT INTO `log` VALUES (8805, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:39:57');
INSERT INTO `log` VALUES (8806, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 19:40:42');
INSERT INTO `log` VALUES (8807, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:42:29');
INSERT INTO `log` VALUES (8808, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:42:29');
INSERT INTO `log` VALUES (8809, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 19:43:06');
INSERT INTO `log` VALUES (8810, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:43:06');
INSERT INTO `log` VALUES (8811, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 19:43:08');
INSERT INTO `log` VALUES (8812, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:43:09');
INSERT INTO `log` VALUES (8813, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805中期变更驳回已恢复原成员和教师', '127.0.0.1', '2025-07-15 19:43:20');
INSERT INTO `log` VALUES (8814, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 19:43:20');
INSERT INTO `log` VALUES (8815, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:43:21');
INSERT INTO `log` VALUES (8816, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:49:26');
INSERT INTO `log` VALUES (8817, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:49:30');
INSERT INTO `log` VALUES (8818, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:49:33');
INSERT INTO `log` VALUES (8819, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:49:34');
INSERT INTO `log` VALUES (8820, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:50:01');
INSERT INTO `log` VALUES (8821, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:50:03');
INSERT INTO `log` VALUES (8822, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 19:50:52');
INSERT INTO `log` VALUES (8823, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:50:52');
INSERT INTO `log` VALUES (8824, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 19:50:55');
INSERT INTO `log` VALUES (8825, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:50:57');
INSERT INTO `log` VALUES (8826, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805中期变更驳回已恢复原成员和教师', '127.0.0.1', '2025-07-15 19:51:15');
INSERT INTO `log` VALUES (8827, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 19:51:15');
INSERT INTO `log` VALUES (8828, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:51:16');
INSERT INTO `log` VALUES (8829, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:51:18');
INSERT INTO `log` VALUES (8830, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 19:51:27');
INSERT INTO `log` VALUES (8831, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:51:27');
INSERT INTO `log` VALUES (8832, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 19:51:28');
INSERT INTO `log` VALUES (8833, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:51:29');
INSERT INTO `log` VALUES (8834, '001', '软件教师', '进入后台', '127.0.0.1', '2025-07-15 19:51:33');
INSERT INTO `log` VALUES (8835, '001', '软件教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:51:33');
INSERT INTO `log` VALUES (8836, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:53:38');
INSERT INTO `log` VALUES (8837, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:55:12');
INSERT INTO `log` VALUES (8838, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 19:55:40');
INSERT INTO `log` VALUES (8839, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:55:40');
INSERT INTO `log` VALUES (8840, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 19:55:43');
INSERT INTO `log` VALUES (8841, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:55:43');
INSERT INTO `log` VALUES (8842, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:55:45');
INSERT INTO `log` VALUES (8843, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 19:56:24');
INSERT INTO `log` VALUES (8844, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 19:56:25');
INSERT INTO `log` VALUES (8845, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 19:56:27');
INSERT INTO `log` VALUES (8846, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 19:56:32');
INSERT INTO `log` VALUES (8847, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:01:50');
INSERT INTO `log` VALUES (8848, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:05:34');
INSERT INTO `log` VALUES (8849, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:05:36');
INSERT INTO `log` VALUES (8850, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:05:37');
INSERT INTO `log` VALUES (8851, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:05:39');
INSERT INTO `log` VALUES (8852, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:05:41');
INSERT INTO `log` VALUES (8853, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:06:12');
INSERT INTO `log` VALUES (8854, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:06:13');
INSERT INTO `log` VALUES (8855, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:06:16');
INSERT INTO `log` VALUES (8856, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:06:17');
INSERT INTO `log` VALUES (8857, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-07-15 20:09:04');
INSERT INTO `log` VALUES (8858, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:09:12');
INSERT INTO `log` VALUES (8859, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:09:13');
INSERT INTO `log` VALUES (8860, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 20:09:14');
INSERT INTO `log` VALUES (8861, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:09:14');
INSERT INTO `log` VALUES (8862, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:09:16');
INSERT INTO `log` VALUES (8863, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:12:21');
INSERT INTO `log` VALUES (8864, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:12:23');
INSERT INTO `log` VALUES (8865, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:12:23');
INSERT INTO `log` VALUES (8866, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:12:26');
INSERT INTO `log` VALUES (8867, '22008027', '宁城嘉', '宁城嘉中期变更申请修改失败：中期变更申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 20:15:30');
INSERT INTO `log` VALUES (8868, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 20:15:30');
INSERT INTO `log` VALUES (8869, '22008027', '宁城嘉', '宁城嘉当前不是可申请中期变更状态', '127.0.0.1', '2025-07-15 20:15:34');
INSERT INTO `log` VALUES (8870, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:16:49');
INSERT INTO `log` VALUES (8871, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:16:51');
INSERT INTO `log` VALUES (8872, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 20:17:40');
INSERT INTO `log` VALUES (8873, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:17:42');
INSERT INTO `log` VALUES (8874, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805中期变更驳回已恢复原成员和教师', '127.0.0.1', '2025-07-15 20:17:49');
INSERT INTO `log` VALUES (8875, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:17:49');
INSERT INTO `log` VALUES (8876, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:17:50');
INSERT INTO `log` VALUES (8877, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:17:56');
INSERT INTO `log` VALUES (8878, '22008027', '宁城嘉', '宁城嘉中期变更申请修改成功', '127.0.0.1', '2025-07-15 20:18:17');
INSERT INTO `log` VALUES (8879, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 20:18:17');
INSERT INTO `log` VALUES (8880, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:18:22');
INSERT INTO `log` VALUES (8881, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:18:49');
INSERT INTO `log` VALUES (8882, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 20:19:49');
INSERT INTO `log` VALUES (8883, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:19:53');
INSERT INTO `log` VALUES (8884, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:20:05');
INSERT INTO `log` VALUES (8885, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:20:06');
INSERT INTO `log` VALUES (8886, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:20:08');
INSERT INTO `log` VALUES (8887, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:23:25');
INSERT INTO `log` VALUES (8888, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:23:28');
INSERT INTO `log` VALUES (8889, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:23:29');
INSERT INTO `log` VALUES (8890, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 20:23:30');
INSERT INTO `log` VALUES (8891, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:23:31');
INSERT INTO `log` VALUES (8892, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:23:32');
INSERT INTO `log` VALUES (8893, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:42:43');
INSERT INTO `log` VALUES (8894, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:42:47');
INSERT INTO `log` VALUES (8895, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:42:47');
INSERT INTO `log` VALUES (8896, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 20:42:49');
INSERT INTO `log` VALUES (8897, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:42:49');
INSERT INTO `log` VALUES (8898, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:42:51');
INSERT INTO `log` VALUES (8899, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:43:46');
INSERT INTO `log` VALUES (8900, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:44:06');
INSERT INTO `log` VALUES (8901, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:44:42');
INSERT INTO `log` VALUES (8902, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:45:04');
INSERT INTO `log` VALUES (8903, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:45:48');
INSERT INTO `log` VALUES (8904, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:46:25');
INSERT INTO `log` VALUES (8905, 'rj001', '软件管理', '进入后台', '127.0.0.1', '2025-07-15 20:46:41');
INSERT INTO `log` VALUES (8906, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:46:41');
INSERT INTO `log` VALUES (8907, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 20:46:45');
INSERT INTO `log` VALUES (8908, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:46:46');
INSERT INTO `log` VALUES (8909, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 20:46:50');
INSERT INTO `log` VALUES (8910, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:46:50');
INSERT INTO `log` VALUES (8911, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 20:46:52');
INSERT INTO `log` VALUES (8912, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:46:52');
INSERT INTO `log` VALUES (8913, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:47:05');
INSERT INTO `log` VALUES (8914, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:47:05');
INSERT INTO `log` VALUES (8915, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 20:47:08');
INSERT INTO `log` VALUES (8916, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:47:08');
INSERT INTO `log` VALUES (8917, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:47:10');
INSERT INTO `log` VALUES (8918, 'cw001', '财务管理', '进入后台', '127.0.0.1', '2025-07-15 20:47:18');
INSERT INTO `log` VALUES (8919, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:47:18');
INSERT INTO `log` VALUES (8920, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 20:47:20');
INSERT INTO `log` VALUES (8921, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 20:47:27');
INSERT INTO `log` VALUES (8922, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:47:30');
INSERT INTO `log` VALUES (8923, 'rj001', '软件管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:47:35');
INSERT INTO `log` VALUES (8924, 'rj001', '软件管理', '进入大创', '127.0.0.1', '2025-07-15 20:47:36');
INSERT INTO `log` VALUES (8925, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:47:38');
INSERT INTO `log` VALUES (8926, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805中期变更驳回已恢复原成员和教师', '127.0.0.1', '2025-07-15 20:48:11');
INSERT INTO `log` VALUES (8927, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:48:11');
INSERT INTO `log` VALUES (8928, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:48:12');
INSERT INTO `log` VALUES (8929, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:48:18');
INSERT INTO `log` VALUES (8930, '22008027', '宁城嘉', '宁城嘉中期变更申请修改失败：中期变更申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 20:48:24');
INSERT INTO `log` VALUES (8931, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 20:48:24');
INSERT INTO `log` VALUES (8932, '22008027', '宁城嘉', '宁城嘉当前不是可申请中期变更状态', '127.0.0.1', '2025-07-15 20:48:39');
INSERT INTO `log` VALUES (8933, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:48:43');
INSERT INTO `log` VALUES (8934, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:48:56');
INSERT INTO `log` VALUES (8935, '001', '软件教师', '进入大创', '127.0.0.1', '2025-07-15 20:50:43');
INSERT INTO `log` VALUES (8936, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:50:50');
INSERT INTO `log` VALUES (8937, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:51:01');
INSERT INTO `log` VALUES (8938, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:51:37');
INSERT INTO `log` VALUES (8939, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:51:38');
INSERT INTO `log` VALUES (8940, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 20:51:46');
INSERT INTO `log` VALUES (8941, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:51:46');
INSERT INTO `log` VALUES (8942, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 20:51:47');
INSERT INTO `log` VALUES (8943, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:51:49');
INSERT INTO `log` VALUES (8944, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805中期变更驳回已恢复原成员和教师', '127.0.0.1', '2025-07-15 20:51:57');
INSERT INTO `log` VALUES (8945, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:51:57');
INSERT INTO `log` VALUES (8946, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:51:58');
INSERT INTO `log` VALUES (8947, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 20:52:00');
INSERT INTO `log` VALUES (8948, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 20:52:00');
INSERT INTO `log` VALUES (8949, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 20:52:02');
INSERT INTO `log` VALUES (8950, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:52:02');
INSERT INTO `log` VALUES (8951, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:52:04');
INSERT INTO `log` VALUES (8952, '22008027', '宁城嘉', '宁城嘉中期变更申请修改成功', '127.0.0.1', '2025-07-15 20:52:13');
INSERT INTO `log` VALUES (8953, '22008027', '宁城嘉', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 20:52:13');
INSERT INTO `log` VALUES (8954, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 20:52:18');
INSERT INTO `log` VALUES (8955, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:52:24');
INSERT INTO `log` VALUES (8956, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805中期变更数据更新成功', '127.0.0.1', '2025-07-15 20:52:30');
INSERT INTO `log` VALUES (8957, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 20:52:30');
INSERT INTO `log` VALUES (8958, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:52:31');
INSERT INTO `log` VALUES (8959, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:52:33');
INSERT INTO `log` VALUES (8960, '22008027', '宁城嘉', '宁城嘉中期报告提交成功', '127.0.0.1', '2025-07-15 20:53:06');
INSERT INTO `log` VALUES (8961, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:53:10');
INSERT INTO `log` VALUES (8962, '22008027', '宁城嘉', '宁城嘉中期报告修改失败：中期报告修改失败，已回滚事务', '127.0.0.1', '2025-07-15 20:53:24');
INSERT INTO `log` VALUES (8963, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:53:34');
INSERT INTO `log` VALUES (8964, '22008027', '宁城嘉', '宁城嘉当前不是可填报状态', '127.0.0.1', '2025-07-15 20:53:51');
INSERT INTO `log` VALUES (8965, '22008027', '宁城嘉', '宁城嘉中期报告修改失败：中期报告修改失败，已回滚事务', '127.0.0.1', '2025-07-15 20:53:58');
INSERT INTO `log` VALUES (8966, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:54:03');
INSERT INTO `log` VALUES (8967, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:58:54');
INSERT INTO `log` VALUES (8968, '22008027', '宁城嘉', '宁城嘉中期报告修改成功', '127.0.0.1', '2025-07-15 20:59:04');
INSERT INTO `log` VALUES (8969, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:59:12');
INSERT INTO `log` VALUES (8970, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 20:59:42');
INSERT INTO `log` VALUES (8971, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:59:45');
INSERT INTO `log` VALUES (8972, '001', '软件教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 20:59:58');
INSERT INTO `log` VALUES (8973, '001', '软件教师', '用户尝试审核无权限的项目：3537f00b-5842-4120-871a-f52b83f17805', '127.0.0.1', '2025-07-15 21:00:05');
INSERT INTO `log` VALUES (8974, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:00:08');
INSERT INTO `log` VALUES (8975, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 21:00:11');
INSERT INTO `log` VALUES (8976, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 21:00:17');
INSERT INTO `log` VALUES (8977, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:00:18');
INSERT INTO `log` VALUES (8978, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:00:43');
INSERT INTO `log` VALUES (8979, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:00:44');
INSERT INTO `log` VALUES (8980, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 21:00:44');
INSERT INTO `log` VALUES (8981, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 21:00:45');
INSERT INTO `log` VALUES (8982, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:00:46');
INSERT INTO `log` VALUES (8983, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 21:00:46');
INSERT INTO `log` VALUES (8984, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:00:47');
INSERT INTO `log` VALUES (8985, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:00:59');
INSERT INTO `log` VALUES (8986, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:01:00');
INSERT INTO `log` VALUES (8987, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:01:21');
INSERT INTO `log` VALUES (8988, '22008027', '宁城嘉', '宁城嘉当前不是可填报状态', '127.0.0.1', '2025-07-15 21:01:24');
INSERT INTO `log` VALUES (8989, '22008027', '宁城嘉', '宁城嘉中期报告修改成功', '127.0.0.1', '2025-07-15 21:01:29');
INSERT INTO `log` VALUES (8990, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:01:31');
INSERT INTO `log` VALUES (8991, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:01:39');
INSERT INTO `log` VALUES (8992, 'cw001', '财务管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:01:42');
INSERT INTO `log` VALUES (8993, 'cw001', '财务管理', '进入大创', '127.0.0.1', '2025-07-15 21:01:43');
INSERT INTO `log` VALUES (8994, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:02:08');
INSERT INTO `log` VALUES (8995, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:09');
INSERT INTO `log` VALUES (8996, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:19');
INSERT INTO `log` VALUES (8997, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:02:29');
INSERT INTO `log` VALUES (8998, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:30');
INSERT INTO `log` VALUES (8999, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:32');
INSERT INTO `log` VALUES (9000, '22008027', '宁城嘉', '宁城嘉中期报告修改成功', '127.0.0.1', '2025-07-15 21:02:39');
INSERT INTO `log` VALUES (9001, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:43');
INSERT INTO `log` VALUES (9002, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:02:53');
INSERT INTO `log` VALUES (9003, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:02:54');
INSERT INTO `log` VALUES (9004, 'dc001', '大创校级管理', '进入后台', '127.0.0.1', '2025-07-15 21:03:11');
INSERT INTO `log` VALUES (9005, 'dc001', '大创校级管理', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:03:11');
INSERT INTO `log` VALUES (9006, 'dc001', '大创校级管理', '进入大创', '127.0.0.1', '2025-07-15 21:03:15');
INSERT INTO `log` VALUES (9007, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:16');
INSERT INTO `log` VALUES (9008, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:03:25');
INSERT INTO `log` VALUES (9009, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:26');
INSERT INTO `log` VALUES (9010, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 21:03:30');
INSERT INTO `log` VALUES (9011, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:03:30');
INSERT INTO `log` VALUES (9012, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 21:03:31');
INSERT INTO `log` VALUES (9013, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 21:03:31');
INSERT INTO `log` VALUES (9014, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:33');
INSERT INTO `log` VALUES (9015, '22008027', '宁城嘉', '宁城嘉中期报告修改成功', '127.0.0.1', '2025-07-15 21:03:42');
INSERT INTO `log` VALUES (9016, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:46');
INSERT INTO `log` VALUES (9017, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:50');
INSERT INTO `log` VALUES (9018, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:03:54');
INSERT INTO `log` VALUES (9019, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:04:00');
INSERT INTO `log` VALUES (9020, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:04:01');
INSERT INTO `log` VALUES (9021, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:04:03');
INSERT INTO `log` VALUES (9022, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:04:17');
INSERT INTO `log` VALUES (9023, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:04:28');
INSERT INTO `log` VALUES (9024, '22008027', '宁城嘉', '宁城嘉延期结题申请提交成功', '127.0.0.1', '2025-07-15 21:05:39');
INSERT INTO `log` VALUES (9025, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:05:43');
INSERT INTO `log` VALUES (9026, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 21:05:57');
INSERT INTO `log` VALUES (9027, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:10:01');
INSERT INTO `log` VALUES (9028, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 21:10:04');
INSERT INTO `log` VALUES (9029, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:10:08');
INSERT INTO `log` VALUES (9030, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:12:40');
INSERT INTO `log` VALUES (9031, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 21:12:48');
INSERT INTO `log` VALUES (9032, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 21:15:55');
INSERT INTO `log` VALUES (9033, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:15:56');
INSERT INTO `log` VALUES (9034, '22008027', '宁城嘉', '宁城嘉当前不是可申请中期变更状态', '127.0.0.1', '2025-07-15 21:16:01');
INSERT INTO `log` VALUES (9035, '22008027', '宁城嘉', '宁城嘉当前不是可申请延期结题状态', '127.0.0.1', '2025-07-15 21:16:04');
INSERT INTO `log` VALUES (9036, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:17:33');
INSERT INTO `log` VALUES (9037, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:17:57');
INSERT INTO `log` VALUES (9038, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 21:19:18');
INSERT INTO `log` VALUES (9039, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:19:18');
INSERT INTO `log` VALUES (9040, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 21:19:20');
INSERT INTO `log` VALUES (9041, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:19:22');
INSERT INTO `log` VALUES (9042, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:20:32');
INSERT INTO `log` VALUES (9043, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:20:36');
INSERT INTO `log` VALUES (9044, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:20:42');
INSERT INTO `log` VALUES (9045, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:22:00');
INSERT INTO `log` VALUES (9046, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:22:29');
INSERT INTO `log` VALUES (9047, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:25:18');
INSERT INTO `log` VALUES (9048, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:25:21');
INSERT INTO `log` VALUES (9049, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:25:25');
INSERT INTO `log` VALUES (9050, '22008027', '宁城嘉', '宁城嘉延期结题申请修改失败：延期结题申请修改失败，已回滚事务', '127.0.0.1', '2025-07-15 21:30:00');
INSERT INTO `log` VALUES (9051, '22008027', '宁城嘉', '宁城嘉延期结题申请修改成功', '127.0.0.1', '2025-07-15 21:30:03');
INSERT INTO `log` VALUES (9052, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:30:09');
INSERT INTO `log` VALUES (9053, '001', '软件教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:31:51');
INSERT INTO `log` VALUES (9054, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:31:56');
INSERT INTO `log` VALUES (9055, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:32:03');
INSERT INTO `log` VALUES (9056, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:32:05');
INSERT INTO `log` VALUES (9057, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:32:32');
INSERT INTO `log` VALUES (9058, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 21:32:39');
INSERT INTO `log` VALUES (9059, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:32:39');
INSERT INTO `log` VALUES (9060, '22008027', '宁城嘉', '进入项目立项页', '127.0.0.1', '2025-07-15 21:32:43');
INSERT INTO `log` VALUES (9061, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 21:32:43');
INSERT INTO `log` VALUES (9062, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:32:45');
INSERT INTO `log` VALUES (9063, '22008027', '宁城嘉', '宁城嘉延期结题申请修改成功', '127.0.0.1', '2025-07-15 21:33:08');
INSERT INTO `log` VALUES (9064, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:15');
INSERT INTO `log` VALUES (9065, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:21');
INSERT INTO `log` VALUES (9066, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:33:26');
INSERT INTO `log` VALUES (9067, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:27');
INSERT INTO `log` VALUES (9068, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:31');
INSERT INTO `log` VALUES (9069, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:33:40');
INSERT INTO `log` VALUES (9070, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:41');
INSERT INTO `log` VALUES (9071, '22008027', '宁城嘉', '进入后台', '127.0.0.1', '2025-07-15 21:33:44');
INSERT INTO `log` VALUES (9072, '22008027', '宁城嘉', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:33:44');
INSERT INTO `log` VALUES (9073, '22008027', '宁城嘉', '进入大创', '127.0.0.1', '2025-07-15 21:33:46');
INSERT INTO `log` VALUES (9074, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:33:47');
INSERT INTO `log` VALUES (9075, '22008027', '宁城嘉', '宁城嘉延期结题申请修改成功', '127.0.0.1', '2025-07-15 21:34:01');
INSERT INTO `log` VALUES (9076, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:06');
INSERT INTO `log` VALUES (9077, 'rj001', '软件管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:34:13');
INSERT INTO `log` VALUES (9078, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:15');
INSERT INTO `log` VALUES (9079, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:30');
INSERT INTO `log` VALUES (9080, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:34:36');
INSERT INTO `log` VALUES (9081, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:37');
INSERT INTO `log` VALUES (9082, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:40');
INSERT INTO `log` VALUES (9083, '22008027', '宁城嘉', '宁城嘉延期结题申请修改成功', '127.0.0.1', '2025-07-15 21:34:45');
INSERT INTO `log` VALUES (9084, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:48');
INSERT INTO `log` VALUES (9085, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:34:51');
INSERT INTO `log` VALUES (9086, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:05');
INSERT INTO `log` VALUES (9087, 'rj001', '软件管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:12');
INSERT INTO `log` VALUES (9088, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:14');
INSERT INTO `log` VALUES (9089, 'dc001', '大创校级管理', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:35:23');
INSERT INTO `log` VALUES (9090, 'dc001', '大创校级管理', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:24');
INSERT INTO `log` VALUES (9091, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:32');
INSERT INTO `log` VALUES (9092, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:35:52');
INSERT INTO `log` VALUES (9093, '22008027', '宁城嘉', '文件上传参数 - class: dc, type: conclude', '127.0.0.1', '2025-07-15 21:36:24');
INSERT INTO `log` VALUES (9094, '22008027', '宁城嘉', 'Doc1.docx上传成功至E:\\code\\cxcysys\\public\\static/files/upload/dc/22008027/', '127.0.0.1', '2025-07-15 21:36:24');
INSERT INTO `log` VALUES (9095, '22008027', '宁城嘉', '宁城嘉结题报告修改失败：SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect date value: \'\' for column \'time\' at row 1', '127.0.0.1', '2025-07-15 21:36:32');
INSERT INTO `log` VALUES (9096, '22008027', '宁城嘉', '宁城嘉结题报告修改失败：SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect date value: \'\' for column \'time\' at row 1', '127.0.0.1', '2025-07-15 21:36:39');
INSERT INTO `log` VALUES (9097, '22008027', '宁城嘉', '宁城嘉结题报告修改失败：SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect date value: \'\' for column \'time\' at row 1', '127.0.0.1', '2025-07-15 21:39:31');
INSERT INTO `log` VALUES (9098, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:42:24');
INSERT INTO `log` VALUES (9099, '22008027', '宁城嘉', '宁城嘉结题报告修改成功', '127.0.0.1', '2025-07-15 21:42:49');
INSERT INTO `log` VALUES (9100, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:43:04');
INSERT INTO `log` VALUES (9101, '002', '财务处教师', '进入后台', '127.0.0.1', '2025-07-15 21:43:21');
INSERT INTO `log` VALUES (9102, '002', '财务处教师', '进入仪表盘页面', '127.0.0.1', '2025-07-15 21:43:21');
INSERT INTO `log` VALUES (9103, '002', '财务处教师', '进入大创', '127.0.0.1', '2025-07-15 21:43:23');
INSERT INTO `log` VALUES (9104, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:43:25');
INSERT INTO `log` VALUES (9105, '002', '财务处教师', '3537f00b-5842-4120-871a-f52b83f17805审核成功', '127.0.0.1', '2025-07-15 21:43:39');
INSERT INTO `log` VALUES (9106, '002', '财务处教师', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:43:40');
INSERT INTO `log` VALUES (9107, '22008027', '宁城嘉', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-07-15 21:43:41');
INSERT INTO `log` VALUES (9108, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-07-16 07:42:20');
INSERT INTO `log` VALUES (9109, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-07-16 07:42:20');
INSERT INTO `log` VALUES (9110, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-07-16 07:42:20');
INSERT INTO `log` VALUES (9111, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-08-02 10:05:19');
INSERT INTO `log` VALUES (9112, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-08-02 10:05:19');
INSERT INTO `log` VALUES (9113, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-08-02 10:05:19');
INSERT INTO `log` VALUES (9114, 'admin', '张三', '进入后台', '127.0.0.1', '2025-08-02 10:05:21');
INSERT INTO `log` VALUES (9115, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-02 10:05:21');
INSERT INTO `log` VALUES (9116, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-02 10:05:21');
INSERT INTO `log` VALUES (9117, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:05:24');
INSERT INTO `log` VALUES (9118, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:05:28');
INSERT INTO `log` VALUES (9119, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:05:29');
INSERT INTO `log` VALUES (9120, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:05:29');
INSERT INTO `log` VALUES (9121, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:12:01');
INSERT INTO `log` VALUES (9122, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-02 10:12:03');
INSERT INTO `log` VALUES (9123, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:12:05');
INSERT INTO `log` VALUES (9124, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:12:05');
INSERT INTO `log` VALUES (9125, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:12:06');
INSERT INTO `log` VALUES (9126, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:12:07');
INSERT INTO `log` VALUES (9127, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-02 10:12:09');
INSERT INTO `log` VALUES (9128, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:12:11');
INSERT INTO `log` VALUES (9129, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:12:21');
INSERT INTO `log` VALUES (9130, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:12:21');
INSERT INTO `log` VALUES (9131, 'admin', '张三', '进入英才库列表', '127.0.0.1', '2025-08-02 10:12:24');
INSERT INTO `log` VALUES (9132, 'admin', '张三', '进入英才库申请列表', '127.0.0.1', '2025-08-02 10:12:26');
INSERT INTO `log` VALUES (9133, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:12:28');
INSERT INTO `log` VALUES (9134, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:12:28');
INSERT INTO `log` VALUES (9135, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:12:29');
INSERT INTO `log` VALUES (9136, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:12:31');
INSERT INTO `log` VALUES (9137, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:12:31');
INSERT INTO `log` VALUES (9138, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:12:36');
INSERT INTO `log` VALUES (9139, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:15:53');
INSERT INTO `log` VALUES (9140, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:15:54');
INSERT INTO `log` VALUES (9141, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:15:55');
INSERT INTO `log` VALUES (9142, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:18:49');
INSERT INTO `log` VALUES (9143, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-02 10:18:52');
INSERT INTO `log` VALUES (9144, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:18:54');
INSERT INTO `log` VALUES (9145, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:18:54');
INSERT INTO `log` VALUES (9146, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:18:56');
INSERT INTO `log` VALUES (9147, 'admin', '张三', '进入英才库列表', '127.0.0.1', '2025-08-02 10:18:58');
INSERT INTO `log` VALUES (9148, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-02 10:24:35');
INSERT INTO `log` VALUES (9149, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-02 10:24:44');
INSERT INTO `log` VALUES (9150, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-02 10:24:44');
INSERT INTO `log` VALUES (9151, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-02 10:24:49');
INSERT INTO `log` VALUES (9152, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-08-02 10:24:57');
INSERT INTO `log` VALUES (9153, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-08-02 10:25:01');
INSERT INTO `log` VALUES (9154, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-08-02 10:25:48');
INSERT INTO `log` VALUES (9155, 'admin', '张三', '进入英才库申请列表', '127.0.0.1', '2025-08-02 10:25:50');
INSERT INTO `log` VALUES (9156, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-02 10:25:52');
INSERT INTO `log` VALUES (9157, 'admin', '张三', '查看竞赛列表', '127.0.0.1', '2025-08-02 10:25:52');
INSERT INTO `log` VALUES (9158, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-02 10:25:57');
INSERT INTO `log` VALUES (9159, 'admin', '张三', '开始获取前台轮播数据', '127.0.0.1', '2025-08-28 16:16:31');
INSERT INTO `log` VALUES (9160, 'admin', '张三', '查询到轮播数据数量: 0', '127.0.0.1', '2025-08-28 16:16:31');
INSERT INTO `log` VALUES (9161, 'admin', '张三', '最终轮播数据数量: 0', '127.0.0.1', '2025-08-28 16:16:31');
INSERT INTO `log` VALUES (9162, 'admin', '张三', '进入后台', '127.0.0.1', '2025-08-28 16:16:33');
INSERT INTO `log` VALUES (9163, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-28 16:16:33');
INSERT INTO `log` VALUES (9164, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:16:33');
INSERT INTO `log` VALUES (9165, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-28 16:16:36');
INSERT INTO `log` VALUES (9166, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:16:38');
INSERT INTO `log` VALUES (9167, 'admin', '张三', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-08-28 16:16:40');
INSERT INTO `log` VALUES (9168, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-28 16:16:57');
INSERT INTO `log` VALUES (9169, 'admin', '张三', '进入英才库列表', '127.0.0.1', '2025-08-28 16:17:00');
INSERT INTO `log` VALUES (9170, 'admin', '张三', '进入英才库申请列表', '127.0.0.1', '2025-08-28 16:17:01');
INSERT INTO `log` VALUES (9171, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-08-28 16:17:06');
INSERT INTO `log` VALUES (9172, 'admin', '张三', '进入新闻列表', '127.0.0.1', '2025-08-28 16:17:08');
INSERT INTO `log` VALUES (9173, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-28 16:17:46');
INSERT INTO `log` VALUES (9174, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-08-28 16:18:55');
INSERT INTO `log` VALUES (9175, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-28 16:18:57');
INSERT INTO `log` VALUES (9176, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-28 16:19:02');
INSERT INTO `log` VALUES (9177, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:19:02');
INSERT INTO `log` VALUES (9178, 'admin', '张三', '查看3537f00b-5842-4120-871a-f52b83f17805项目详情', '127.0.0.1', '2025-08-28 16:19:04');
INSERT INTO `log` VALUES (9179, 'admin', '张三', '张三你不是项目的负责人', '127.0.0.1', '2025-08-28 16:19:16');
INSERT INTO `log` VALUES (9180, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-28 16:19:19');
INSERT INTO `log` VALUES (9181, 'admin', '张三', '进入轮播管理', '127.0.0.1', '2025-08-28 16:19:35');
INSERT INTO `log` VALUES (9182, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-28 16:19:40');
INSERT INTO `log` VALUES (9183, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-28 16:19:43');
INSERT INTO `log` VALUES (9184, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-28 16:19:43');
INSERT INTO `log` VALUES (9185, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-28 16:19:59');
INSERT INTO `log` VALUES (9186, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:20:01');
INSERT INTO `log` VALUES (9187, 'admin', '张三', '进入后台', '127.0.0.1', '2025-08-28 16:22:43');
INSERT INTO `log` VALUES (9188, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-28 16:22:43');
INSERT INTO `log` VALUES (9189, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:22:43');
INSERT INTO `log` VALUES (9190, 'admin', '张三', '超级管理员进入项目立项页', '127.0.0.1', '2025-08-28 16:22:45');
INSERT INTO `log` VALUES (9191, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-28 16:22:46');
INSERT INTO `log` VALUES (9192, 'admin', '张三', '进入竞赛项目列表', '127.0.0.1', '2025-08-28 16:22:47');
INSERT INTO `log` VALUES (9193, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-28 16:22:48');
INSERT INTO `log` VALUES (9194, 'admin', '张三', '进入后台', '127.0.0.1', '2025-08-28 16:22:53');
INSERT INTO `log` VALUES (9195, 'admin', '张三', '进入仪表盘页面', '127.0.0.1', '2025-08-28 16:22:53');
INSERT INTO `log` VALUES (9196, 'admin', '张三', '进入大创', '127.0.0.1', '2025-08-28 16:22:54');
INSERT INTO `log` VALUES (9197, 'admin', '张三', '进入竞赛管理', '127.0.0.1', '2025-08-28 16:22:58');

-- ----------------------------
-- Table structure for major
-- ----------------------------
DROP TABLE IF EXISTS `major`;
CREATE TABLE `major`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '专业名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of major
-- ----------------------------
INSERT INTO `major` VALUES (3, 50, '软件工程', '2025-07-15 10:36:35', '2025-07-15 10:36:35', 0);
INSERT INTO `major` VALUES (4, 50, '计算机科学与技术', '2025-07-15 10:36:42', '2025-07-15 10:36:42', 0);
INSERT INTO `major` VALUES (5, 50, '网络工程', '2025-07-15 10:37:02', '2025-07-15 10:37:02', 0);

-- ----------------------------
-- Table structure for member
-- ----------------------------
DROP TABLE IF EXISTS `member`;
CREATE TABLE `member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目成员',
  `rank` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `dc` int(11) NULL DEFAULT 0 COMMENT '是否大创项目0否1是',
  `type` int(11) NULL DEFAULT 0 COMMENT '0校内1校外',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '以下仅校外指导教师有',
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 487 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of member
-- ----------------------------
INSERT INTO `member` VALUES (451, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 19:36:51', '2025-07-15 19:38:28', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (452, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '2', '2025-07-15 19:36:51', '2025-07-15 19:38:28', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (453, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '3', '2025-07-15 19:36:51', '2025-07-15 19:38:28', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (454, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 19:38:28', '2025-07-15 19:51:15', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (455, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 19:38:28', '2025-07-15 19:51:15', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (456, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 19:38:28', '2025-07-15 19:51:15', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (460, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 19:51:15', '2025-07-15 20:15:30', 1, 1, 0, '', NULL, NULL, NULL);
INSERT INTO `member` VALUES (461, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '2', '2025-07-15 19:51:15', '2025-07-15 20:15:30', 1, 1, 0, '', NULL, NULL, NULL);
INSERT INTO `member` VALUES (462, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '3', '2025-07-15 19:51:15', '2025-07-15 20:15:30', 1, 1, 0, '', NULL, NULL, NULL);
INSERT INTO `member` VALUES (463, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:15:30', '2025-07-15 20:17:49', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (464, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 20:15:30', '2025-07-15 20:17:49', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (465, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 20:15:30', '2025-07-15 20:17:49', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (466, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:17:49', '2025-07-15 20:18:17', 1, 1, 0, '宁城嘉', NULL, NULL, NULL);
INSERT INTO `member` VALUES (467, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '2', '2025-07-15 20:17:49', '2025-07-15 20:18:17', 1, 1, 0, '宁1', NULL, NULL, NULL);
INSERT INTO `member` VALUES (468, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '3', '2025-07-15 20:17:49', '2025-07-15 20:18:17', 1, 1, 0, '宁2', NULL, NULL, NULL);
INSERT INTO `member` VALUES (469, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:18:17', '2025-07-15 20:48:11', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (470, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 20:18:17', '2025-07-15 20:48:11', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (471, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 20:18:17', '2025-07-15 20:48:11', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (472, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:48:11', '2025-07-15 20:48:24', 1, 1, 0, '宁城嘉', NULL, NULL, NULL);
INSERT INTO `member` VALUES (473, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '2', '2025-07-15 20:48:11', '2025-07-15 20:48:24', 1, 1, 0, '宁1', NULL, NULL, NULL);
INSERT INTO `member` VALUES (474, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '3', '2025-07-15 20:48:11', '2025-07-15 20:48:24', 1, 1, 0, '宁2', NULL, NULL, NULL);
INSERT INTO `member` VALUES (475, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:48:24', '2025-07-15 20:51:57', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (476, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 20:48:24', '2025-07-15 20:51:57', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (477, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 20:48:24', '2025-07-15 20:51:57', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (478, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:51:57', '2025-07-15 20:52:13', 1, 1, 0, '宁城嘉', NULL, NULL, NULL);
INSERT INTO `member` VALUES (479, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '2', '2025-07-15 20:51:57', '2025-07-15 20:52:13', 1, 1, 0, '宁1', NULL, NULL, NULL);
INSERT INTO `member` VALUES (480, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '3', '2025-07-15 20:51:57', '2025-07-15 20:52:13', 1, 1, 0, '宁2', NULL, NULL, NULL);
INSERT INTO `member` VALUES (481, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:52:13', '2025-07-15 20:52:30', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (482, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 20:52:13', '2025-07-15 20:52:30', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (483, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 20:52:13', '2025-07-15 20:52:30', 1, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (484, '3537f00b-5842-4120-871a-f52b83f17805', '22008027', '1', '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (485, '3537f00b-5842-4120-871a-f52b83f17805', '2200802777', '2', '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `member` VALUES (486, '3537f00b-5842-4120-871a-f52b83f17805', '220080277', '3', '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0, 1, 0, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for newsclass
-- ----------------------------
DROP TABLE IF EXISTS `newsclass`;
CREATE TABLE `newsclass`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '板块名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `rank` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排序',
  `usermode` int(11) NULL DEFAULT NULL COMMENT '对应用户组',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of newsclass
-- ----------------------------
INSERT INTO `newsclass` VALUES (6, '测试板块1', '2025-07-15 12:39:47', '2025-07-15 12:39:47', 0, '1', 14);
INSERT INTO `newsclass` VALUES (7, '测试板块2', '2025-07-15 13:22:35', '2025-07-15 13:22:35', 0, '2', 15);

-- ----------------------------
-- Table structure for newsdetail
-- ----------------------------
DROP TABLE IF EXISTS `newsdetail`;
CREATE TABLE `newsdetail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class` int(11) NULL DEFAULT NULL COMMENT '新闻板块对应newsclass',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '新闻标题',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '富文本内容',
  `auth` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发布机构/发布人',
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发布人username',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1015 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of newsdetail
-- ----------------------------
INSERT INTO `newsdetail` VALUES (1013, 6, '测试板块1新闻', 'JTI2bHQlM0JwJTI2Z3QlM0IlRTYlOTYlQjAlRTklOTclQkIlRTUlODYlODUlRTUlQUUlQjklMjZsdCUzQiUyRnAlMjZndCUzQg==', 'a', 'admin', '2025-07-15 13:22:51', '2025-07-15 13:22:51', 0);
INSERT INTO `newsdetail` VALUES (1014, 7, '测试新闻板块2', 'JTI2bHQlM0JwJTI2Z3QlM0IlRTYlQjUlOEIlRTglQUYlOTUlRTYlOTYlQjAlRTklOTclQkIlRTYlOUQlQkYlRTUlOUQlOTcyJTI2bHQlM0IlMkZwJTI2Z3QlM0I=', '', 'admin', '2025-07-15 13:23:18', '2025-07-15 13:23:18', 0);

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '校内指导教师直接是username',
  `rank` int(11) NULL DEFAULT NULL COMMENT '排位',
  `type` int(11) NULL DEFAULT NULL COMMENT '0校内1校外',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '以下仅校外指导教师有',
  `unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位',
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `job` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '职务/职称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `dc` int(11) NULL DEFAULT 0 COMMENT '是否大创项目0否1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 191 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of teacher
-- ----------------------------
INSERT INTO `teacher` VALUES (175, '3537f00b-5842-4120-871a-f52b83f17805', '001', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 19:36:51', '2025-07-15 19:38:28', 1, 1);
INSERT INTO `teacher` VALUES (176, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 19:38:28', '2025-07-15 19:51:15', 1, 0);
INSERT INTO `teacher` VALUES (178, '3537f00b-5842-4120-871a-f52b83f17805', '001', 1, 0, '', NULL, NULL, NULL, NULL, '2025-07-15 19:51:15', '2025-07-15 20:15:30', 1, 1);
INSERT INTO `teacher` VALUES (179, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:15:30', '2025-07-15 20:17:49', 1, 0);
INSERT INTO `teacher` VALUES (180, '3537f00b-5842-4120-871a-f52b83f17805', '001', 1, 0, '软件教师', NULL, NULL, NULL, NULL, '2025-07-15 20:17:49', '2025-07-15 20:18:17', 1, 1);
INSERT INTO `teacher` VALUES (181, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:18:17', '2025-07-15 20:48:11', 1, 0);
INSERT INTO `teacher` VALUES (182, '3537f00b-5842-4120-871a-f52b83f17805', '001', 2, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:18:17', '2025-07-15 20:48:11', 1, 0);
INSERT INTO `teacher` VALUES (183, '3537f00b-5842-4120-871a-f52b83f17805', '001', 1, 0, '软件教师', NULL, NULL, NULL, NULL, '2025-07-15 20:48:11', '2025-07-15 20:48:24', 1, 1);
INSERT INTO `teacher` VALUES (184, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:48:24', '2025-07-15 20:51:57', 1, 0);
INSERT INTO `teacher` VALUES (185, '3537f00b-5842-4120-871a-f52b83f17805', '001', 2, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:48:24', '2025-07-15 20:51:57', 1, 0);
INSERT INTO `teacher` VALUES (186, '3537f00b-5842-4120-871a-f52b83f17805', '001', 1, 0, '软件教师', NULL, NULL, NULL, NULL, '2025-07-15 20:51:57', '2025-07-15 20:52:13', 1, 1);
INSERT INTO `teacher` VALUES (187, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:52:13', '2025-07-15 20:52:30', 1, 0);
INSERT INTO `teacher` VALUES (188, '3537f00b-5842-4120-871a-f52b83f17805', '001', 2, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:52:13', '2025-07-15 20:52:30', 1, 0);
INSERT INTO `teacher` VALUES (189, '3537f00b-5842-4120-871a-f52b83f17805', '002', 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0, 0);
INSERT INTO `teacher` VALUES (190, '3537f00b-5842-4120-871a-f52b83f17805', '001', 2, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-15 20:52:30', '2025-07-15 20:52:30', 0, 0);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `username` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '学号',
  `college` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '学院',
  `major` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '专业',
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话号',
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `status` int(11) NULL DEFAULT 0 COMMENT '状态0正常1禁用',
  `usermode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `job` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '技术职称',
  `grade` int(255) NULL DEFAULT NULL COMMENT '年级',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `student_id`(`username`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 111 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, '张三', 'admin', '', '', '', '', '$2y$10$dS7Ceikbt7ps0Ufzvx7cHeH26XDSkUhPx3opoKDWJp/mMzJHejtHW', '2024-11-30 18:11:09', '2025-07-15 10:35:54', '', 0, '11', NULL, NULL);
INSERT INTO `user` VALUES (103, '宁城嘉', '22008027', '50', '3', '<EMAIL>', '15555070516', '$2y$10$pgI22nbx6Ywd6JLVfYx4h.JQCkDxy4NxJMzw72BZpEGl11y506f.C', '2025-07-15 10:37:56', '2025-07-15 11:06:19', NULL, 0, '1', '本科', 2022);
INSERT INTO `user` VALUES (104, '宁1', '220080277', '50', '5', '<EMAIL>', '18888888888', '$2y$10$U70k/dSWKOjCDSpDeZh6meQriWXfR0pgj9Yf0lQoOtt.lN6Hg3ope', '2025-07-15 10:59:14', '2025-07-15 10:59:14', NULL, 0, '1', '本科', 2022);
INSERT INTO `user` VALUES (105, '宁2', '2200802777', '50', '5', '<EMAIL>', '12345678998', '$2y$10$pwS9kMggFMRx1SPQsJYd4Oc95W7PvLdLXhT2YrDC7zcXLTGygZ9hS', '2025-07-15 11:01:19', '2025-07-15 11:01:19', NULL, 0, '1', '本科', 2024);
INSERT INTO `user` VALUES (106, '软件教师', '001', '50', NULL, '<EMAIL>', '1234569878', '$2y$10$xsYs1u0KcMInpU6DaG91vetvJ5WHgyNmMeg7jGZLJK.wrpBdRIql6', '2025-07-15 11:02:16', '2025-07-15 11:02:36', NULL, 0, '2', '教授', 0);
INSERT INTO `user` VALUES (107, '财务处教师', '002', '1', NULL, '<EMAIL>', '1234569878', '$2y$10$Ogt3HchLlc.vAgr9xiGK/O8ZilqVTXNfigpX.9x3j29He3Dql4aFC', '2025-07-15 11:03:05', '2025-07-15 11:03:05', NULL, 0, '2', '讲师', 0);
INSERT INTO `user` VALUES (108, '软件管理', 'rj001', '50', NULL, '<EMAIL>', '1234569878', '$2y$10$JqDv1WLtvo2lXPP9aZKyWO8Y816P14lyeylLOMA1FWMGB3SMPbPI2', '2025-07-15 11:04:30', '2025-07-15 11:04:30', NULL, 0, '3', '讲师', 0);
INSERT INTO `user` VALUES (109, '大创校级管理', 'dc001', '60', NULL, '<EMAIL>', '1234569878', '$2y$10$2HaU8sFgOlV6oWJ.ZxCV3OAQ8zvAB2wYp/IPPXIhJh5iS4Pudj6zC', '2025-07-15 11:05:12', '2025-07-15 11:05:12', NULL, 0, '4', '讲师', 0);
INSERT INTO `user` VALUES (110, '财务管理', 'cw001', '1', NULL, '<EMAIL>', '15556', '$2y$10$8O5mQJrrToQrxn1M7iGs/.5n8.ory/Ma8Vqf0mwzajn1qMUxRN9KS', '2025-07-15 13:59:29', '2025-07-15 14:00:50', NULL, 0, '3', '', 0);

-- ----------------------------
-- Table structure for usermode
-- ----------------------------
DROP TABLE IF EXISTS `usermode`;
CREATE TABLE `usermode`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `group_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户组名',
  `permission` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '二级权限（新闻管理员）',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `group_name`(`group_name`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of usermode
-- ----------------------------
INSERT INTO `usermode` VALUES (1, '学生', ' ', 0, '2025-03-07 21:37:32', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (2, '教师', ' ', 0, '2025-03-07 21:37:32', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (3, '大创院级管理员', ' ', 0, '2025-03-07 21:52:03', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (4, '大创校级管理员', ' ', 0, '2025-03-07 21:52:18', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (5, '竞赛院级管理员', ' ', 0, '2025-03-11 20:38:51', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (6, '竞赛校级管理员', ' ', 0, '2025-03-11 20:39:05', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (7, '英才库校级管理员', ' ', 0, '2025-03-11 20:39:31', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (11, '超级管理员', ' ', 0, '2025-03-20 07:02:13', '2025-07-15 10:33:17');
INSERT INTO `usermode` VALUES (15, '测试板块2新闻管理员', NULL, 0, '2025-07-15 13:22:35', '2025-07-15 13:22:35');
INSERT INTO `usermode` VALUES (14, '测试板块1新闻管理员', NULL, 0, '2025-07-15 12:39:47', '2025-07-15 12:39:47');

-- ----------------------------
-- Table structure for yckblacklist
-- ----------------------------
DROP TABLE IF EXISTS `yckblacklist`;
CREATE TABLE `yckblacklist`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `target` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '被拉黑人uid',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckblacklist
-- ----------------------------

-- ----------------------------
-- Table structure for yckcheck
-- ----------------------------
DROP TABLE IF EXISTS `yckcheck`;
CREATE TABLE `yckcheck`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` int(11) NULL DEFAULT 3 COMMENT '类型1教师2学院3学校',
  `check` int(11) NULL DEFAULT NULL COMMENT '0通过1驳回',
  `status` int(11) NULL DEFAULT 1 COMMENT '1申请',
  `mark` double NULL DEFAULT NULL COMMENT '评分',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckcheck
-- ----------------------------

-- ----------------------------
-- Table structure for yckcontactlog
-- ----------------------------
DROP TABLE IF EXISTS `yckcontactlog`;
CREATE TABLE `yckcontactlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '查看人',
  `target` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '被查看人username',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckcontactlog
-- ----------------------------

-- ----------------------------
-- Table structure for yckcurriculum
-- ----------------------------
DROP TABLE IF EXISTS `yckcurriculum`;
CREATE TABLE `yckcurriculum`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job` int(11) NULL DEFAULT 0 COMMENT '展示学位职称',
  `intro` int(11) NULL DEFAULT 0,
  `phone` int(11) NULL DEFAULT NULL,
  `email` int(11) NULL DEFAULT 1,
  `qq` int(11) NULL DEFAULT NULL,
  `qywx` int(11) NULL DEFAULT NULL,
  `wx` int(11) NULL DEFAULT NULL,
  `dd` int(11) NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `domains` int(11) NULL DEFAULT NULL,
  `tags` int(11) NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckcurriculum
-- ----------------------------

-- ----------------------------
-- Table structure for yckdomain
-- ----------------------------
DROP TABLE IF EXISTS `yckdomain`;
CREATE TABLE `yckdomain`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckdomain
-- ----------------------------

-- ----------------------------
-- Table structure for yckjob
-- ----------------------------
DROP TABLE IF EXISTS `yckjob`;
CREATE TABLE `yckjob`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` int(11) NULL DEFAULT NULL COMMENT '1学生2教师',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckjob
-- ----------------------------
INSERT INTO `yckjob` VALUES (1, '本科', 1, '2025-03-11 16:24:10', '2025-03-11 16:24:10', 0);
INSERT INTO `yckjob` VALUES (2, '研究生', 1, '2025-03-11 16:24:18', '2025-03-11 16:24:18', 0);
INSERT INTO `yckjob` VALUES (3, '教授', 2, '2025-03-11 16:24:25', '2025-03-11 16:24:25', 0);
INSERT INTO `yckjob` VALUES (4, '副教授', 2, '2025-03-11 16:24:32', '2025-03-11 16:24:32', 0);
INSERT INTO `yckjob` VALUES (5, '讲师', 2, '2025-03-11 16:24:37', '2025-03-11 16:24:37', 0);

-- ----------------------------
-- Table structure for yckprogress
-- ----------------------------
DROP TABLE IF EXISTS `yckprogress`;
CREATE TABLE `yckprogress`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大创项目编号',
  `action` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件名称',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件详情',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckprogress
-- ----------------------------

-- ----------------------------
-- Table structure for yckproject
-- ----------------------------
DROP TABLE IF EXISTS `yckproject`;
CREATE TABLE `yckproject`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大创or竞赛',
  `uid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `show` int(11) NULL DEFAULT 0 COMMENT '是否展示再简历里',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckproject
-- ----------------------------

-- ----------------------------
-- Table structure for yckstatus
-- ----------------------------
DROP TABLE IF EXISTS `yckstatus`;
CREATE TABLE `yckstatus`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckstatus
-- ----------------------------
INSERT INTO `yckstatus` VALUES (1, '等待学校审核', '2025-03-07 19:44:28', '2025-03-11 18:33:35', 0);
INSERT INTO `yckstatus` VALUES (2, '已通过', '2025-03-07 19:45:06', '2025-03-11 18:33:40', 0);
INSERT INTO `yckstatus` VALUES (3, '学校申请审核驳回', '2025-03-07 19:45:23', '2025-03-11 22:47:49', 0);

-- ----------------------------
-- Table structure for ycktag
-- ----------------------------
DROP TABLE IF EXISTS `ycktag`;
CREATE TABLE `ycktag`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of ycktag
-- ----------------------------

-- ----------------------------
-- Table structure for yckuser
-- ----------------------------
DROP TABLE IF EXISTS `yckuser`;
CREATE TABLE `yckuser`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户卡号',
  `intro` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '个人简介',
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '照片地址',
  `wx` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信二维码',
  `qq` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'qq二维码',
  `qywx` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业微信二维码',
  `dd` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '钉钉二维码',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '意见',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_delete` int(11) NULL DEFAULT 0 COMMENT '0',
  `job` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '教师职称，学生学历',
  `gender` enum('male','female') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
  `status` int(11) NULL DEFAULT NULL COMMENT '对应yckstatus',
  `mark` double NULL DEFAULT NULL COMMENT '评分',
  `index` int(11) NULL DEFAULT NULL COMMENT '排位',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yckuser
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
