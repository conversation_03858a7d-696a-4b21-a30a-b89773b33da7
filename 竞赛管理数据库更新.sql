-- 竞赛管理数据库更新脚本
-- 为竞赛表添加学院字段，实现院级管理员数据隔离

-- 1. 为 jscompetition 表添加 college 字段
ALTER TABLE `jscompetition` 
ADD COLUMN `college` int DEFAULT NULL COMMENT '所属学院ID' AFTER `remark`;

-- 2. 添加外键约束（可选）
-- ALTER TABLE `jscompetition` 
-- ADD CONSTRAINT `fk_jscompetition_college` 
-- FOREIGN KEY (`college`) REFERENCES `department` (`id`) ON DELETE SET NULL;

-- 3. 为现有数据设置默认学院（需要根据实际情况调整）
-- 假设所有现有竞赛都属于第一个学院（ID=1）
-- UPDATE `jscompetition` SET `college` = 1 WHERE `college` IS NULL;

-- 4. 创建索引以提高查询性能
CREATE INDEX `idx_jscompetition_college` ON `jscompetition` (`college`);
CREATE INDEX `idx_jscompetition_college_delete` ON `jscompetition` (`college`, `is_delete`);

-- 5. 更新竞赛管理相关视图（如果有的话）
-- 这里可以添加创建视图的SQL语句

-- 6. 验证更新结果
-- SELECT c.cuid, c.name, c.college, d.name as college_name 
-- FROM jscompetition c 
-- LEFT JOIN department d ON c.college = d.id 
-- WHERE c.is_delete = 0; 