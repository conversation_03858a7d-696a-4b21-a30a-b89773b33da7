# 英才库滚动播放功能实现说明

## 功能概述

实现了官网首页英才库信息的滚动播放功能，包括：
- 英才学生/精英教师切换
- 自动轮播展示（每3秒切换一次）
- 手动点击指示器切换
- 美观的卡片式展示界面

## 文件结构

### 后端文件
- `app/controller/yckmanage/Lists.php` - 英才库控制器，新增 `get_homepage_talents()` 方法
- `route/app.php` - 路由配置，新增 `yck-homepage-talents` 路由

### 前端文件
- `app/view/index/index.html` - 官网首页模板，修改英才库展示区域
- `static/js/index/index.js` - 官网首页JavaScript逻辑
- `static/css/index/index.css` - 官网首页样式文件
- `test_talents.html` - 功能测试页面

## 功能特点

### 1. 数据获取
- 使用专门的API接口 `/yck-homepage-talents`
- 支持GET请求，参数：`type`（英才学生/精英教师）、`limit`（限制数量）
- 返回前10名排序靠前的英才信息

### 2. 界面展示
- 卡片式设计，渐变背景
- 显示姓名、学院、专业、标签、评分
- 星级评分展示
- 轮播指示器

### 3. 交互功能
- 自动轮播：每3秒自动切换
- 手动切换：点击指示器切换
- 类型切换：英才学生/精英教师切换时自动刷新数据

## API接口说明

### 获取官网首页英才库数据
```
GET /yck-homepage-talents
```

**参数：**
- `type` (可选): 英才类型，"英才学生" 或 "精英教师"，默认"英才学生"
- `limit` (可选): 返回数量限制，默认10

**返回格式：**
```json
{
    "status": "success",
    "message": {
        "total": 10,
        "data": [
            {
                "username": "2021001",
                "name": "张三",
                "department": "计算机学院",
                "major_name": "软件工程",
                "mark": 4.5,
                "tags": "编程,算法,创新"
            }
        ]
    }
}
```

## 使用方法

### 1. 访问官网首页
直接访问官网首页，右侧英才库区域会自动加载并开始轮播。

### 2. 测试功能
访问 `test_talents.html` 可以单独测试英才库滚动播放功能。

### 3. 切换类型
点击"英才学生"或"精英教师"按钮可以切换显示不同类型的英才。

## 样式定制

可以通过修改 `static/css/index/index.css` 文件来定制样式：

- `.talent-card` - 英才卡片样式
- `.talent-name` - 姓名样式
- `.talent-college` - 学院样式
- `.talent-score` - 评分样式
- `.indicator` - 轮播指示器样式

## 注意事项

1. 确保数据库中有英才库数据
2. 英才状态必须为已审核通过（status=2）
3. 用户状态必须为正常（status=0）
4. 按index、mark、created_at排序显示

## 扩展功能

如需添加更多功能，可以考虑：
- 添加点击跳转到英才详情页
- 增加更多筛选条件
- 添加动画效果
- 支持自定义轮播间隔时间 