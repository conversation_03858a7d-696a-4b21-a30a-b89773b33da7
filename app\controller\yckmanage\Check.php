<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\Jscheck;
use app\model\Jsprogress;
use app\model\Jsproject;
use app\model\Member;
use app\model\Teacher;
use app\model\Yckcheck;
use app\model\Yckprogress;
use app\model\Yckuser;
use think\facade\Db;

class Check extends BaseController
{
    public function checkyck($username){
        $check=input('post.check.check');
        $remark=input('post.check.remark');
        $mark=input('post.check.mark');
        if (!$remark||!$mark){
            LogExecution($username.'审核失败，请输入审核意见和评分');
            return ['status'=>'error','message'=>'审核失败，请输入审核意见和评分'];
        }
        $yckuser=Yckuser::where('is_delete',0)->where('username',$username)->find();

        $new_status=$yckuser['status'];
        $checks=[
            'username'=>$username,
            'check'=>'',
            'status'=>'',
            'mark'=>'',
            'remark'=>'',
        ];
        $progess=[
            'username'=>$username,
            'action'=>'',
            'remark'=>'',
        ];
        if (!$check){
            //通过
            $checks['check']=0;
            if (session('user.usermode')==7){
                if ($yckuser['status']==1){
                    $checks['status']=1;
                    $new_status=2;
                    $progess['action']='学校申请审核';
                    $progess['remark']='学校申请审核通过，评分'.$mark;
                }
            }
        }else{
            //驳回
            $checks['check']=1;
            if (session('user.usermode')==7){
                if ($yckuser['status']==1){
                    $checks['status']=1;
                    $new_status=3;
                    $progess['action']='学校申请审核';
                    $progess['remark']='学校申请审核驳回，评分'.$mark;
                }
            }
        }
        $checks['remark']=$remark;
        $checks['mark']=$mark;

        //插入三个表
        // 开启事务
        Db::startTrans();
        $status=Yckuser::where('is_delete',0)->where('username',$username)->update(['status'=>$new_status,'mark'=>$mark]);
        $checks=Yckcheck::insert($checks);
        $progess=Yckprogress::insert($progess);
        if ($checks&&$progess&&$status){
            // 如果所有操作成功，提交事务
            Db::commit();
            LogExecution($username.'审核成功，评分'.$mark);
            return ['status'=>'success','message'=>'审核成功'];
        }else{
            // 如果前面的操作中有任何一个失败，回滚事务
            Db::rollback();
            LogExecution($username.'审核失败，插入数据表错误');
            return ['status'=>'error','message'=>'审核失败，数据异常'];
        }

    }
}