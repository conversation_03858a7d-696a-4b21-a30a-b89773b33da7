{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/yck/applylist.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">英才库</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="yck-applylist">申请列表</a>
</p>
<el-row :gutter="5">
  <el-col :span="3">
    <el-button plain type="primary" @click="handleApply">立即申请</el-button>
  </el-col>
  <el-col :span="3">

    <el-button plain type="primary" @click="">导出当前{{ totalData.length }}个人员</el-button>
  </el-col>

      <el-col :span="3">
        <el-select v-model="search.department" placeholder="请选择部门" style="width: 100%">
          <el-option value="all" label="全部部门"></el-option>

          {foreach $search.department as $department}
          <el-option value="{$department.id}" label="{$department.name}"></el-option>
          {/foreach}
        </el-select>
      </el-col>
      <el-col :span="3">
        <el-select
        style="width: 100%;"
        v-model="search.user"
        filterable
        remote
        clearable
        reserve-keyword
        placeholder="全部成员"
        :remote-method="remoteusers"
        :loading="loading">
        <el-option
          v-for="item in users"
          :key="item.value"
          :label="item.label"
          :value="item.value">
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
        </el-option>
      </el-select>
      </el-col>
      <el-col :span="3">
        <el-select v-model="search.status" placeholder="请选择状态" style="width: 100%">
          <el-option value="all" label="全部状态"></el-option>
              {foreach $search.status as $status}
          <el-option value="{$status.id}" label="{$status.name}"></el-option>
          {/foreach}
        </el-select>
      </el-col>
      <el-col :span="3">
        <el-button plain type="primary" @click="select">搜索</el-button>
      </el-col>
</el-row>
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
          <el-table-column
            prop="username"
            label="卡号"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="name"
          label="姓名"
          min-width="180"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="department"
            label="部门"
            min-width="120"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="major"
          label="专业"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="job"
          label="学历/职称"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
        prop="status"
        label="状态"
        min-width="120"
        align="center"
      ></el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="detail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
</div>
<script src="../../static/js/yck/applylist.js"></script>
</body>
</html>