/* 官网首页样式 */

/* 英才库滚动展示样式 */
.talent-showcase {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.talent-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 15px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.talent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.talent-info {
    flex: 1;
}

.talent-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.talent-college {
    font-size: 16px;
    margin-bottom: 6px;
    opacity: 0.9;
}

.talent-major {
    font-size: 14px;
    margin-bottom: 6px;
    opacity: 0.8;
}

.talent-tags {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    display: inline-block;
}

.talent-score {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
}

.score-text {
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
}

/* 轮播指示器样式 */
.talent-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background-color: #409EFF;
    transform: scale(1.2);
}

.indicator:hover {
    background-color: #409EFF;
    transform: scale(1.1);
}

/* 英才库标题样式 */
.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.more-link {
    font-size: 12px;
    color: #409EFF;
    text-decoration: none;
}

.more-link:hover {
    color: #66b1ff;
}

/* 新闻卡片样式 */
.news-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.card-content {
    padding: 15px;
}

.news-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.news-item:hover {
    background-color: #f5f7fa;
}

.news-item:last-child {
    margin-bottom: 0;
}

.date-box {
    text-align: center;
    margin-right: 12px;
    min-width: 50px;
}

.date-day {
    font-size: 20px;
    font-weight: bold;
    color: #409EFF;
    line-height: 1;
}

.date-month {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

.news-info {
    flex: 1;
}

.news-title {
    font-size: 14px;
    color: #333;
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 登录对话框样式 */
.login-dialog .el-dialog__body {
    padding: 20px;
}

.login-dialog .el-form-item {
    margin-bottom: 20px;
}

.login-dialog .lgoin {
    width: 100%;
}

.login-btn {
    margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .talent-card {
        padding: 15px;
        min-height: 150px;
    }
    
    .talent-name {
        font-size: 20px;
    }
    
    .talent-college {
        font-size: 14px;
    }
    
    .news-item {
        flex-direction: column;
    }
    
    .date-box {
        margin-right: 0;
        margin-bottom: 8px;
        text-align: left;
    }
}

/* 轮播覆盖层样式 */
.banner-box,
.banner-box .el-carousel,
.banner-box .el-carousel__container,
.banner-box .el-carousel__item,
.banner-box .el-carousel-item,
.banner-box .carousel-img-wrap {
    height: 100% !important;
    min-height: 100%;
}

.banner-box {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    background: #fff;
    position: relative;
}

.banner-box .el-carousel__item {
    display: flex !important;
    align-items: stretch !important;
    height: 100% !important;
    padding: 0;
    background: #000;
}

.banner-box .carousel-img-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    align-items: center;     /* 水平居中 */
}

.banner-box .carousel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    flex: 1 1 auto;
    transition: transform 0.4s cubic-bezier(.4,0,.2,1);
    object-position: center center; /* 居中显示 */
}

.carousel-overlay {
    position: absolute;
    left: 0; right: 0; bottom: 0;
    background: linear-gradient(0deg,rgba(60,60,60,0.75) 0%,rgba(0,0,0,0.0) 80%);
    color: #fff;
    padding: 32px 32px 20px 32px;
    z-index: 2;
    pointer-events: none;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.carousel-overlay h3 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 2px 8px rgba(0,0,0,0.5);
    line-height: 1.2;
}

.carousel-overlay p {
    margin: 0;
    font-size: 1rem;
    opacity: 0.92;
    text-shadow: 0 2px 8px rgba(0,0,0,0.4);
}

.banner-box .el-carousel__indicators {
    bottom: 18px !important;
    z-index: 10;
}

.banner-box .el-carousel__indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    margin: 0 6px;
    transition: background 0.3s;
}

.banner-box .el-carousel__indicator.is-active {
    background: #409EFF;
    box-shadow: 0 2px 8px rgba(64,158,255,0.2);
}

.talent-highlight {
  background: #f0f9eb !important;
  font-weight: bold;
  transition: background 0.3s;
}