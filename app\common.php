<?php
// 应用公共文件
use think\facade\Db;
use think\facade\Session;
use think\facade\Request;

if (!function_exists('LogExecution')) {
    function LogExecution($action = null)
    {
        // 获取当前用户信息
        $username = Session::get('user.username', 'guest');
        $name = Session::get('user.name', 'guest');

        // 如果没有提供 action，则尝试从请求中获取（注意：这通常不是最佳实践，因为函数应该尽量独立）
        if (is_null($action)) {
            $controller = Request::controller();
            $method = Request::action();
            $action = $controller ? ($controller . '/' . $method) : 'unknown/action';
        }

        // 记录日志到数据库
        Db::name('log')->insert([
            'username' => $username,
            'name'     => $name,
            'ip'       => Request::ip(),
            'action'   => $action,
            'create_time' => date('Y-m-d H:i:s'),
        ]);
    }
}