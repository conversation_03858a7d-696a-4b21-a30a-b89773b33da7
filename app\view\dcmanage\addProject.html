{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/dc/addproject.css">
<style>
  body{
    overflow-x: hidden;
    /* padding: 0 3rem; */
  }
</style>
<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">大创平台</a>
    >
    {if !isset($project)}
    <a style="text-decoration: none;color: #00000097;" href="dc-addProject">项目立项</a>
    {else}
    <a style="text-decoration: none;color: #00000097;" href="dc-lists">项目管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="dc-addProject?uid={$project.uid}">修改项目：{$project.name}</a>
    {/if}
  </p>

  <el-row :gutter="40">
    <h2>“大学生创新创业训练计划”项目申报书</h2>
  <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="10rem">
    <el-col  :sm="24" :md="12" >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名称"></el-input>
        </el-form-item>
        <el-form-item label="立项时间" prop="time">
            <el-date-picker
            style="width: 100%;"
            v-model="form.time"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目成员" prop="members">
          <el-select
          style="width: 100%;"
          v-model="form.members"
          multiple
          :multiple-limit='4'
          filterable
          remote
          reserve-keyword
          placeholder="顺序选择,无需选择队长"
          :remote-method="remotestudents"
          :loading="loading">
          <el-option
            v-for="item in options_s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            >
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="指导教师" prop="teachers">
        <el-select
        style="width: 100%;"
        v-model="form.teachers"
        multiple
        :multiple-limit='2'
        filterable
        remote
        reserve-keyword
        placeholder="顺序选择"
        :remote-method="remoteteachers"
        :loading="loading">
        <el-option
          v-for="item in options_t"
          :key="item.value"
          :label="item.label"
          :value="item.value">
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="校外指导教师" prop="outteacher">
      <el-input v-model="form.outteacher.name" placeholder="请输入姓名"></el-input>
      <el-input v-model="form.outteacher.job" placeholder="请输入职务/职称"></el-input>
      <el-input v-model="form.outteacher.unit" placeholder="请输入所在单位"></el-input>
      <el-input v-model="form.outteacher.phone" placeholder="请输入手机号"></el-input>
      <el-input v-model="form.outteacher.email" placeholder="请输入邮箱"></el-input>
    </el-form-item>
    
    <el-form-item label="项目类型" prop="type">
      <el-select v-model="form.type" style="width: 100%;" placeholder="请选择项目类型">
        {foreach $types as $type}
        <el-option label="{$type.label}" value="{$type.value}"></el-option>
        {/foreach}
      </el-select>
    </el-form-item>
    <el-form-item label="项目周期" prop="period">
      <el-select v-model="form.period" style="width: 100%;" placeholder="请选择项目类型">
        {foreach $periods as $period}
        <el-option label="{$period.label}" value="{$period.value}"></el-option>
        {/foreach}
      </el-select>
    </el-form-item>
    <el-form-item label="项目简介" prop="introduction">
      <el-input
      type="textarea"
      maxlength="500"
      show-word-limit
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入项目简介"
      v-model="form.introduction">
     </el-input>
    </el-form-item>
    <el-form-item label="申请理由" prop="reason">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入申请理由"
      v-model="form.reason">
     </el-input>
    </el-form-item>
    <el-form-item label="项目特色及创新点" prop="innovation">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入项目特色及创新点"
      v-model="form.innovation">
     </el-input>
    </el-form-item>


    </el-col>
    <el-col  :sm="24" :md="12" >
      <el-form-item label="项目进度安排" prop="schedule">
        <el-input
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 20}"
        placeholder="请输入项目进度安排"
        v-model="form.schedule">
       </el-input>
      </el-form-item>
      <el-form-item label="项目经费" prop="budget">
        <el-input-number v-model="form.budget" :min="0" :max="10000" label="请输入项目经费"></el-input-number>
      </el-form-item>
      <el-form-item label="经费使用计划" prop="plan">
        <el-input
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 20}"
        placeholder="请输入经费使用计划"
        v-model="form.plan">
       </el-input>
      </el-form-item>
      <el-form-item label="项目方案" prop="fileurl">
        {if isset($project) && $project.fileurl}
        <!-- 修改项目时显示原有文件 -->
        <div style="margin-bottom: 10px;" v-if="!showUploadArea">
          <el-alert
            title="当前项目已有上传文件"
            type="info"
            :closable="false"
            show-icon>
            <template #description>
              <p>文件名：{$project.filename|default='项目方案.pdf'}</p>
              <p>文件路径：{$project.fileurl}</p>
              <el-button size="mini" type="primary" @click="downloadFile">下载查看</el-button>
              <el-button size="mini" type="warning" @click="showUploadArea = true">重新上传</el-button>
            </template>
          </el-alert>
        </div>
        {/if}
        <el-upload
        class="upload-demo"
        drag
        :limit="1" 
        action="bs-addfile?class=dc&type=pdf"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-exceed="handleExceed"
        v-show="showUploadArea || !hasExistingFile"
        ref="upload"
        >
        <i  class="el-icon-upload"></i>
        <div  class="el-upload__text">将PDF文件拖到此处，或<em>点击上传</em></div>
        {if isset($project)}
        <div class="el-upload__tip" slot="tip">重新上传将覆盖原有文件</div>
        {/if}
        <div class="el-upload__tip" slot="tip">只能上传pdf文件，且不超过20MB</div>
      </el-upload>
      </el-form-item>
  
      <el-form-item label="项目预期成果" prop="expected">
        <el-checkbox-group v-model="form.expected">
          <el-checkbox label="论文"></el-checkbox>
          <div v-if="form.expected.includes('论文')">
            <el-select
            v-model="form.extras.lunwen.level"
            placeholder="论文级别"
            >
            <el-option  label="省级" value="省级"></el-option>
            <el-option  label="国家级" value="国家级"></el-option>
          </el-select>
            <el-input-number v-model="form.extras.lunwen.num" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="专利"></el-checkbox>
          <div v-if="form.expected.includes('专利')">
            <el-input-number v-model="form.extras.zhuanli" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="调查报告"></el-checkbox>
          <div v-if="form.expected.includes('调查报告')">
            <el-input-number v-model="form.extras.diaochabaogao" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="商业计划书"></el-checkbox>
          <div v-if="form.expected.includes('商业计划书')">
            <el-input-number v-model="form.extras.shangyejihuashu" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="著作"></el-checkbox>
          <div v-if="form.expected.includes('著作')">
            <el-input-number v-model="form.extras.zhuzuo" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="作品"></el-checkbox>
          <div v-if="form.expected.includes('作品')">
            <el-select
            v-model="form.extras.zuopin.class"
            placeholder="作品类别"
            >
            <el-option  lable="软件" value="软件"></el-option>
            <el-option  lable="课件" value="课件"></el-option>
            <el-option  lable="视频" value="视频"></el-option>
            <el-option  lable="微课" value="微课"></el-option>
            <el-option  lable="其他" value="其他"></el-option>
          </el-select>
            <el-input-number v-model="form.extras.zuopin.num" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="公众号"></el-checkbox>
          <div v-if="form.expected.includes('公众号')">
            <el-input-number v-model="form.extras.gzh" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="网站"></el-checkbox>
          <div v-if="form.expected.includes('网站')">
            <el-input-number v-model="form.extras.wz" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="软件"></el-checkbox>
          <div v-if="form.expected.includes('软件')">
            <el-input-number v-model="form.extras.rj" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="小程序"></el-checkbox>
          <div v-if="form.expected.includes('小程序')">
            <el-input-number v-model="form.extras.xcx" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="APP"></el-checkbox>
          <div v-if="form.expected.includes('APP')">
            <el-input-number v-model="form.extras.app" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="运营号"></el-checkbox>
          <div v-if="form.expected.includes('运营号')">
            <el-input-number v-model="form.extras.yyh" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="微课"></el-checkbox>
          <div v-if="form.expected.includes('微课')">
            <el-input-number v-model="form.extras.wk" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="视频"></el-checkbox>
          <div v-if="form.expected.includes('视频')">
            <el-input-number v-model="form.extras.sp" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="绘本（图册）"></el-checkbox>
          <div v-if="form.expected.includes('绘本（图册）')">
            <el-input-number v-model="form.extras.hb" :min="0" :max="100"></el-input-number>
          </div>
          <el-checkbox label="其他"></el-checkbox>
          <div v-if="form.expected.includes('其他')">
            <el-input v-model="form.extras.other" maxlength="10" show-word-limit  placeholder="请输入预期成果"></el-input>
        </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否商业化" prop="shangyehua">
        <el-radio v-model="form.shangyehua" label="0">否</el-radio>
        <el-radio v-model="form.shangyehua" label="1">是</el-radio>
      </el-form-item>
    </el-col>
    <el-col  :sm="24"  style="text-align: center;">
  <el-button type="primary " plain @click="onSubmit">{if isset($project)}立即修改{else}立即创建{/if}</el-button>

    </el-col>

  </el-form>
  
  </el-row>




</div>
<script src="../../static/js/dc/addproject.js"></script>
<script>
{if isset($project)}
app.updateform({:json_encode($project)});
{/if}
</script>
</body>
</html>