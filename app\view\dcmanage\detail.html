{include file="public/_header"}
  <script>
    window.userinfo = {
      username: '{:session(\'user.username\')}',
      usermode: '{:session(\'user.usermode\')}'
    };
  </script>
  <link rel="stylesheet" href="../../static/css/dc/detail.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">大创平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="dc-lists">项目管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="dc-detail?uid={$project.uid}">{$project.name}</a>
</p>

  <!-- 审核框 -->
  <el-dialog
  :title="checktitle"
  :visible.sync="checkVisible"
  width="30%"
  >
  <el-form ref="checkform" :rules="check_rules" label-position="left" :model="checkform" label-width="6rem">
    <el-form-item label="审核结果" prop="check">
      <el-radio v-model="checkform.check" label="0">通过</el-radio>
      <el-radio v-model="checkform.check" label="1">驳回</el-radio>
    </el-form-item>
    <el-form-item label="审核意见" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入审核意见"
      v-model="checkform.remark">
     </el-input>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="checkVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_check">确 定</el-button>
  </span>
</el-dialog>

    <!-- 悬浮按钮 -->
    <el-button
      class="floating-button"
      type="primary"
      icon="el-icon-more"
      circle
      @click="toggleMenu"
  v-if="hasAnyOperation"
    ></el-button>

     <!-- 悬浮菜单 -->
<div v-if="isMenuVisible" class="floating-menu" style="max-height: 60vh; overflow-y: auto; overflow-x: hidden;">
 <el-menu>
   <!-- 学生操作 - 只有学生或超级管理员可见 -->
   <el-menu-item-group v-if="isStudentOperation">
        <template slot="title">学生操作</template>
        <el-menu-item index="1" @click="handleMenuClick(1)">修改项目立项信息</el-menu-item>
        <el-menu-item index="2" @click="handleMenuClick(2)">提交中期报告</el-menu-item>
        <el-menu-item index="3" @click="handleMenuClick(3)">提交结题报告</el-menu-item>
   <el-menu-item index="4" @click="handleMenuClick(4)">申请延期结题</el-menu-item>
   <el-menu-item index="5" @click="handleMenuClick(5)">申请中期变更</el-menu-item>
      </el-menu-item-group>
   
   <!-- 教师操作 - 只有教师或超级管理员可见 -->
   <el-menu-item-group v-if="isTeacherOperation">
        <template slot="title">教师操作</template>
        <el-menu-item index="6" @click="handleMenuClick(6)">项目立项审核</el-menu-item>
        <el-menu-item index="7" @click="handleMenuClick(7)">中期报告审核</el-menu-item>
        <el-menu-item index="8" @click="handleMenuClick(8)">结项报告审核</el-menu-item>
        <el-menu-item index="9" @click="handleMenuClick(9)">延期结题审核</el-menu-item>
        <el-menu-item index="10" @click="handleMenuClick(10)">中期变更审核</el-menu-item>
      </el-menu-item-group>
   
   <!-- 学院操作 - 只有大创院级管理员或超级管理员可见 -->
   <el-menu-item-group v-if="isCollegeOperation">
        <template slot="title">学院操作</template>
        <el-menu-item index="11" @click="handleMenuClick(6)">项目立项审核</el-menu-item>
        <el-menu-item index="12" @click="handleMenuClick(7)">中期报告审核</el-menu-item>
        <el-menu-item index="13" @click="handleMenuClick(8)">结项报告审核</el-menu-item>
        <el-menu-item index="14" @click="handleMenuClick(9)">延期结题审核</el-menu-item>
        <el-menu-item index="15" @click="handleMenuClick(10)">中期变更审核</el-menu-item>
      </el-menu-item-group>
   
   <!-- 学校操作 - 只有大创校级管理员或超级管理员可见 -->
   <el-menu-item-group v-if="isSchoolOperation">
        <template slot="title">学校操作</template>
        <el-menu-item index="16" @click="handleMenuClick(6)">项目立项审核</el-menu-item>
        <el-menu-item index="17" @click="handleMenuClick(7)">中期报告审核</el-menu-item>
        <el-menu-item index="18" @click="handleMenuClick(8)">结项报告审核</el-menu-item>
        <el-menu-item index="19" @click="handleMenuClick(9)">延期结题审核</el-menu-item>
        <el-menu-item index="20" @click="handleMenuClick(10)">中期变更审核</el-menu-item>
      </el-menu-item-group>
   
   <!-- 超级管理员操作 - 只有超级管理员可见 -->
   <el-menu-item-group v-if="isSuperAdmin">
        <template slot="title">超级管理员操作</template>
        <el-menu-item index="21" @click="handleMenuClick(21)">修改项目</el-menu-item>
      </el-menu-item-group>
      </el-menu>
    </div>

<el-row>
  <el-col :xs="24" :sm="20">
    <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="一.项目概况" :column="6" border>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目名称
        </template>
        {$project.name}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目状态
        </template>
        {$project.status_name}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          项目编号
        </template>
        {$project.proid}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          立项时间
        </template>
        {$project.time}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          项目类型
        </template>
        {$project.type_name}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          项目周期
        </template>
        {$project.period_name}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          项目级别
        </template>
        {$project.level_name}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          所属单位
        </template>
        {$member[0]['college_name']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          项目负责人
        </template>
        {$member[0]['name']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          学号
        </template>
        {$member[0]['username']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          联系电话
        </template>
        {$member[0]['phone']}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          电子邮箱
        </template>
        {$member[0]['email']}
      </el-descriptions-item>
    </el-descriptions>
    <el-table
    :data="{$member}"
    style="width: 100%">
    <el-table-column label="团队成员信息" >
      <el-table-column
      prop="username"
      label="学号"
      min-width="100">
      </el-table-column>
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="college_name"
      label="学院"
      min-width="120">
      </el-table-column>
      <el-table-column
      prop="major_name"
      label="专业"
      min-width="120">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    
    <el-table
    :data="{$teacher}"
    style="width: 100%">
    <el-table-column label="指导教师信息" >
    
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="job"
      label="技术职称"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    {if count($outteacher) > 0}
    <el-table
    :data="{$outteacher}"
    style="width: 100%">
    <el-table-column label="校外指导教师信息" >
    
      <el-table-column
        prop="name"
        label="姓名"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="phone"
      label="联系电话"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="email"
      label="邮件"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="job"
      label="职务/职称"
      min-width="180">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
    {/if}
    <el-descriptions :label-style="label_style1" direction="vertical" :content-style="content_style1" class="margin-top" title="二.立项报告" :column="1" border>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目简介
      </template>
      {$project.introduction}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目申请理由（包括/自身/团队具备的知识、条件、特长、兴趣、前期准备等）
      </template>
      {$project.reason}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目方案（计划、技术路线、人员分工等）
      </template>
      <iframe src="/static/files/upload/{$file1_path}" height="800rem;" width="100%"></iframe>
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目特色与创新点
      </template>
      {$project.innovation}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目进度安排
      </template>
      {$project.schedule}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目经费
      </template>
      {$project.budget}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label" >
        <i class="el-icon-user"></i>
        项目经费使用计划（说明经费用途）
      </template>
      {$project.plan}
    </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :label-style="label_style"  :content-style="content_style" class="margin-top" title="项目预期成果" :column="2" border>
      {if $expected.lunwen_num}
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          论文数量
        </template>
        {$expected.lunwen_num}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          论文级别
        </template>
        {$expected.lunwen_level}
      </el-descriptions-item>
      {/if}
      {if $expected.zuopin_num}
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          作品数量
        </template>
        {$expected.zuopin_num}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          作品类别
        </template>
        {$expected.zuopin_class}
      </el-descriptions-item>
      {/if}
      {if $expected.zhuanli_num}
      <el-descriptions-item span="2"> 
        <template slot="label" >
          <i class="el-icon-user"></i>
          专利数量
        </template>
        {$expected.zhuanli}
      </el-descriptions-item>
    {/if}
    {if $expected.diaochabaogao}
    
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          调查报告数量
        </template>
        {$expected.lunwen_level}
      </el-descriptions-item>
      {/if}
      {if $expected.shangyejihuashu}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          商业计划书数量
        </template>
        {$expected.shangyejihuashu}
      </el-descriptions-item>
      {/if}
    {if $expected.zhuzuo}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          著作数量
        </template>
        {$expected.zhuzuo}
      </el-descriptions-item>
      {/if}
    {if $expected.gzh}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          公众号数量
        </template>
        {$expected.gzh}
      </el-descriptions-item>
      {/if}
    {if $expected.wz}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          网站数量
        </template>
        {$expected.wz}
      </el-descriptions-item>
      {/if}
    {if $expected.rj}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          软件数量
        </template>
        {$expected.rj}
      </el-descriptions-item>
      {/if}
    {if $expected.xcx}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          小程序数量
        </template>
        {$expected.xcx}
      </el-descriptions-item>
      {/if}
    {if $expected.app}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          APP数量
        </template>
        {$expected.app}
      </el-descriptions-item>
      {/if}
    {if $expected.yyh}
      <el-descriptions-item span="2"> 
        <template slot="label" 
          <i class="el-icon-user"></i>
          运营号数量
        </template>
        {$expected.yyh}
      </el-descriptions-item>
      {/if}
    {if $expected.wk}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          微课数量
        </template>
        {$expected.wk}
      </el-descriptions-item>
      {/if}
    {if $expected.sp}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          视频数量
        </template>
        {$expected.sp}
      </el-descriptions-item>
      {/if}
    {if $expected.hb}
      <el-descriptions-item span="2">
        <template slot="label" >
          <i class="el-icon-user"></i>
          绘本（图册）数量
        </template>
        {$expected.hb}
      </el-descriptions-item>
      {/if}
    {if $project.shangyehua != null}
    <el-descriptions-item span="2">
      <template slot="label" >
        <i class="el-icon-user"></i>
        是否商业化
      </template>
      {if $project.shangyehua == 1}是{else}否{/if}
    </el-descriptions-item>
    {/if}
    {if $expected.other}
    <el-descriptions-item span="2">
      <template slot="label" >
        <i class="el-icon-user"></i>
        其他
      </template>
      {$expected.other}
    </el-descriptions-item>
    {/if}
    </el-descriptions>
    
    <el-descriptions :label-style="label_style"  direction="vertical"  :content-style="content_style" class="margin-top" :column="1" border>
    {if count($checks['status1']['teacher']) neq 0}
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          教师立项意见
        </template>
        {foreach $checks['status1']['teacher'] as $check}
        <p>
        {if !$check['check']}
        <span style="color: #1fd11f97;">通过</span>
        {else}
        <span style="color: #ff4400;">驳回</span>
        {/if}
        <span style="color: #00000097;">{$check['created_at']}</span>
        <span>{$check['remark']}</span>
      </p>
        {/foreach}
      </el-descriptions-item>
    {/if}
    {if  count($checks['status1']['college']) neq 0}
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          学院立项意见
        </template>
        {foreach $checks['status1']['college'] as $check}
        <p>
          {if !$check['check']}
          <span style="color: #1fd11f97;">通过</span>
          {else}
          <span style="color: #ff4400;">驳回</span>
          {/if}
          <span style="color: #00000097;">{$check['created_at']}</span>
          <span>{$check['remark']}</span>
        </p>
        {/foreach}
      </el-descriptions-item>
    {/if}
    {if count($checks['status1']['school']) neq 0}
      <el-descriptions-item>
        <template slot="label" >
          <i class="el-icon-user"></i>
          学校立项意见
        </template>
        {foreach $checks['status1']['school'] as $check}
        <p>
          {if !$check['check']}
          <span style="color: #1fd11f97;">通过</span>
          {else}
          <span style="color: #ff4400;">驳回</span>
          {/if}
          <span style="color: #00000097;">{$check['created_at']}</span>
          <span>{$check['remark']}</span>
        </p>
        {/foreach}
      </el-descriptions-item>
    {/if}
    
    </el-descriptions>
    {if $interm['interm']}
    <el-descriptions :label-style="label_style"  :content-style="content_style" class="margin-top" title="二.中期报告" :column="2" border>
    </el-descriptions>
    <el-table
    :data="{$interm['achievement']}"
    style="width: 100%">
    <el-table-column label="阶段性成果" >
      <el-table-column
      prop="序号"
      type="index"
      min-width="50">
    </el-table-column>
      <el-table-column
        prop="name"
        label="成果名称"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="form"
      label="成果形式"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="date"
      label="完成时间"
      min-width="180">
      </el-table-column>
      <el-table-column
      prop="remark"
      label="备注"
      min-width="180">
      </el-table-column>
      </el-table-column>
    </el-table-column>
    </el-table>
    <el-table
    show-summary
    :data="{$interm['expenditure']}"
    style="width: 100%">
    <el-table-column label="项目中期经费支出" >
      <el-table-column
      prop="序号"
      type="index"
      min-width="100">
    </el-table-column>
      <el-table-column
        prop="detail"
        label="支出明细"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="amount"
      label="金额"
      min-width="100">
      </el-table-column>
     
    </el-table>

    <el-descriptions :label-style="label_style"  direction="vertical"  :content-style="content_style" class="margin-top" :column="1" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          资金补充说明
        </template>
        {$interm.interm.statement}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          研究工作进展情况
        </template>
        {$interm.interm.progress}
      </el-descriptions-item>
    </el-descriptions>
    {/if}



    <el-descriptions :label-style="label_style"  direction="vertical"  :content-style="content_style" class="margin-top" :column="1" border>
      {if count($checks['status2']['teacher']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            教师中期报告意见
          </template>
          {foreach $checks['status2']['teacher'] as $check}
          <p>
          {if !$check['check']}
          <span style="color: #1fd11f97;">通过</span>
          {else}
          <span style="color: #ff4400;">驳回</span>
          {/if}
          <span style="color: #00000097;">{$check['created_at']}</span>
          <span>{$check['remark']}</span>
        </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if  count($checks['status2']['college']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学院中期报告意见
          </template>
          {foreach $checks['status2']['college'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status2']['school']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学校中期报告意见
          </template>
          {foreach $checks['status2']['school'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      
      </el-descriptions>

    {if $extension}
    <el-descriptions :label-style="label_style" direction="vertical"  :content-style="content_style" class="margin-top" title="四.延期结题申请" :column="1" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          延期时间
        </template>
        {if $extension.extension_time == 6}6个月{elseif $extension.extension_time == 12}1年{else}{$extension.extension_time}个月{/if}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          延期理由
        </template>
        {$extension.reason}
      </el-descriptions-item>
          <!-- 新增：延期结题审核意见 -->
      {if count($checks['status4']['teacher']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            教师延期结题审核意见
          </template>
          {foreach $checks['status4']['teacher'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status4']['college']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学院延期结题审核意见
          </template>
          {foreach $checks['status4']['college'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status4']['school']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学校延期结题审核意见
          </template>
          {foreach $checks['status4']['school'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
    </el-descriptions>
    {/if}

    {if $midchange}
    <el-descriptions :label-style="label_style" direction="vertical"  :content-style="content_style" class="margin-top" title="五.中期变更申请" :column="1" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          变更事项
        </template>
        {$midchange.change_items}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          变更原因
        </template>
        {$midchange.change_reason}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          项目成员变更
        </template>
        <div>
          <div style="margin-bottom: 10px;">
            <strong style="color: #F56C6C;">原项目成员：</strong>
            {if $old_members && count($old_members) > 0}
              {foreach $old_members as $m}
                <el-tag type="danger" style="margin-right: 5px; margin-bottom: 5px;">
                  {if $m.name}{$m.name}{else}--{/if} ({if $m.username}{$m.username}{else}--{/if})
                </el-tag>
              {/foreach}
            {else}
              {foreach $member as $m}
                <el-tag type="danger" style="margin-right: 5px; margin-bottom: 5px;">
                  {$m.name} ({$m.username})
                </el-tag>
              {/foreach}
            {/if}
          </div>
          {if $midchange.new_members_array}
            <div>
              <strong style="color: #67C23A;">新项目成员：</strong>
              {php}
                $leader = $member[0];
                $final_members = array_merge([
                  [
                    'label' => $leader['name'],
                    'value' => $leader['username']
                  ]
                ], $midchange['new_members_array']);
                // 去重
                $seen = [];
                $final_members_unique = [];
                foreach($final_members as $m){
                  $key = $m['value'];
                  if(!in_array($key, $seen)){
                    $final_members_unique[] = $m;
                    $seen[] = $key;
                  }
                }
              {/php}
              {foreach $final_members_unique as $member}
                <el-tag type="success" style="margin-right: 5px; margin-bottom: 5px;">
                  {$member.label} ({$member.value})
                </el-tag>
              {/foreach}
            </div>
          {else}
            <div>
              <strong style="color: #67C23A;">新项目成员：</strong>
              <span style="color: #909399;">无变更</span>
            </div>
          {/if}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          指导教师变更
        </template>
        <div>
          <div style="margin-bottom: 10px;">
            <strong style="color: #F56C6C;">原指导教师：</strong>
            {if $old_teachers && count($old_teachers) > 0}
              {foreach $old_teachers as $t}
                <el-tag type="danger" style="margin-right: 5px; margin-bottom: 5px;">
                  {if $t.name}{$t.name}{else}--{/if} ({if $t.username}{$t.username}{else}--{/if})
                </el-tag>
              {/foreach}
            {else}
              {foreach $teacher as $t}
                <el-tag type="danger" style="margin-right: 5px; margin-bottom: 5px;">
                  {$t.name} ({$t.username})
                </el-tag>
              {/foreach}
            {/if}
          </div>
          {if $midchange.new_teachers_array}
            <div>
              <strong style="color: #67C23A;">新指导教师：</strong>
              {foreach $midchange.new_teachers_array as $teacher}
                <el-tag type="success" style="margin-right: 5px; margin-bottom: 5px;">
                  {$teacher.label} ({$teacher.value})
                </el-tag>
              {/foreach}
            </div>
          {else}
            <div>
              <strong style="color: #67C23A;">新指导教师：</strong>
              <span style="color: #909399;">无变更</span>
            </div>
          {/if}
        </div>
      </el-descriptions-item>
          <!-- 新增：中期变更申请审核意见 -->
      {if count($checks['status5']['teacher']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            教师中期变更审核意见
          </template>
          {foreach $checks['status5']['teacher'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status5']['college']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学院中期变更审核意见
          </template>
          {foreach $checks['status5']['college'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status5']['school']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学校中期变更审核意见
          </template>
          {foreach $checks['status5']['school'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
    </el-descriptions>
    {/if}

    {if $interm['interm']&&$conclude['conclude']}
    <el-descriptions :label-style="label_style" direction="vertical"  :content-style="content_style" class="margin-top" title="三.结题报告" :column="1" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          项目成果简介
        </template>
        {$conclude.conclude.results}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          项目总结报告
        </template>
        {$conclude.conclude.conclusion}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          项目实施过程中存在的问题和建议
        </template>
        {$conclude.conclude.problem}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          是否申请优秀项目
        </template>
        {if $conclude.conclude.excellent_project}
        是
        {else}
        否
        {/if}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user"></i>
          是否申请优秀论文
        </template>
        {if $conclude.conclude.excellent_lunwen}
        是
        {else}
        否
        {/if}
      </el-descriptions-item>
    </el-descriptions>
    <el-table
    show-summary
    :data="{$conclude['expenditure']}"
    style="width: 100%">
    <el-table-column label="项目结题经费支出" >
      <el-table-column
      prop="序号"
      type="index"
      min-width="100">
    </el-table-column>
      <el-table-column
        prop="detail"
        label="支出明细"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="amount"
      label="金额"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="type"
      label="阶段"
      min-width="100">
      </el-table-column>
     
    </el-table>

    <el-table
    show-summary
    :data="{$conclude['achievement']}"
    style="width: 100%">
    <el-table-column label="项目结题成果" >
      <el-table-column
      prop="序号"
      type="index"
      min-width="100">
    </el-table-column>
      <el-table-column
        prop="type"
        label="类型"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="title"
      label="主题"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="main"
      label="主要内容"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="num"
      label="数量"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="author"
      label="作者/注册人"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="remark"
      label="备注"
      min-width="100">
      </el-table-column>
      <el-table-column
      prop="time"
      label="时间"
      min-width="100">
      </el-table-column>
     
    </el-table>

    <el-table
    :data="{$conclude['files']}"
    style="width: 100%">
    <el-table-column
    label="序号"
    type="index"
    min-width="50">
    
    </el-table-column>
    <el-table-column
      prop="name"
      label="结题相关材料"
      min-width="200"
    >
      <template v-slot="scope">
        <a style="text-decoration: none; color: #000;" :href="`/static/files/upload/${scope.row.path}`">{{ scope.row.name }}</a>
      </template>
    </el-table-column>
    </el-table>


    <el-descriptions :label-style="label_style"  direction="vertical"  :content-style="content_style" class="margin-top" :column="1" border>
      {if count($checks['status3']['teacher']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            教师结题报告意见
          </template>
          {foreach $checks['status3']['teacher'] as $check}
          <p>
          {if !$check['check']}
          <span style="color: #1fd11f97;">通过</span>
          {else}
          <span style="color: #ff4400;">驳回</span>
          {/if}
          <span style="color: #00000097;">{$check['created_at']}</span>
          <span>{$check['remark']}</span>
        </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if  count($checks['status3']['college']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学院结题报告意见
          </template>
          {foreach $checks['status3']['college'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      {if count($checks['status3']['school']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学校结题报告意见
          </template>
          {foreach $checks['status3']['school'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">通过</span>
            {else}
            <span style="color: #ff4400;">驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
      
      </el-descriptions>





    {/if}


  </el-col>
  <el-col :xs="24" :sm="4">
    <el-timeline :reverse="false">
      {foreach $progesses as $progess}
      <el-timeline-item
      timestamp="{$progess.created_at}">
      {$progess.action}
      <br>
      {$progess.remark}
    </el-timeline-item>
    {/foreach}
    </el-timeline>
  </el-col>

</el-row>


</div>
<script src="../../static/js/dc/detail.js"></script>
</body>
</html>