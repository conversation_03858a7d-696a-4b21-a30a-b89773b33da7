<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Jscompetition;
use app\model\Jslevel;
use app\model\Jssetting;
use app\model\Department;

class Lists extends BaseController
{
    public function index()
    {
        LogExecution('进入竞赛列表页面');
        
        $search = [
            'level' => Jslevel::where('is_delete', 0)->field('id, name')->select(),
            'department' => Department::where('is_delete', 0)->select(),
        ];
        
        return view('jsmanage/lists', ['search' => $search]);
    }
    
    /**
     * 查询可申请的竞赛列表
     */
    public function select_competition()
    {
        $search = input('post.search');
        $where = [];
        
        // 根据用户权限过滤数据
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        // 学生只能看到本学院或全校的竞赛
        if ($userMode == 1) {
            $where[] = ['c.college', 'in', [$userCollege, null]]; // null表示全校竞赛
        }
        
        // 教师可以看到本学院或全校的竞赛
        if ($userMode == 2) {
            $where[] = ['c.college', 'in', [$userCollege, null]];
        }
        
        // 院级管理员只能看到本学院的竞赛
        if ($userMode == 5) {
            $where[] = ['c.college', '=', $userCollege];
        }
        
        // 校级管理员和超级管理员可以看到所有竞赛
        
        // 只显示启用的竞赛
        $where[] = ['s.active', '=', 1];
        
        // 搜索条件
        if ($search['level'] != 'all') {
            $where[] = ['c.level', '=', $search['level']];
        }
        
        if ($search['texts']) {
            foreach ($search['texts'] as $text) {
                $where[] = ['c.cuid|c.name|c.url', 'like', '%' . $text . '%'];
            }
        }
        
        if ($search['department'] != 'all') {
            $where[] = ['c.college', '=', $search['department']];
        }
        
        $data = Jscompetition::alias('c')
            ->field('
                c.cuid, c.name, c.url, c.remark, c.level, c.college,
                s.active, s.years, s.member, s.teacher, s.outstu, s.outstu_num, s.outtea, s.outtea_num,
                l.name as l_level,
                d.name as college_name
            ')
            ->join('jslevel l', 'l.id=c.level', 'LEFT')
            ->join('jssetting s', 's.cuid=c.cuid', 'LEFT')
            ->join('department d', 'd.id=c.college', 'LEFT')
            ->where($where)
            ->where('c.is_delete', 0)
            ->order('s.active', 'desc')
            ->order('c.updated_at')
            ->select();
            
        LogExecution('查看竞赛列表');
        
        return json(['status' => 'success', 'message' => [
            'total' => sizeof($data),
            'data' => $data
        ]]);
    }
    
    /**
     * 获取竞赛详情
     */
    public function getCompetitionDetail($cuid)
    {
        if (empty($cuid)) {
            return json(['status' => 'error', 'message' => '竞赛ID不能为空']);
        }
        
        // 检查用户权限
        $userMode = session('user.usermode');
        $userCollege = session('user.college');
        
        $competition = Jscompetition::alias('c')
            ->field('
                c.cuid, c.name, c.url, c.remark, c.level, c.college,
                s.active, s.years, s.member, s.teacher, s.outstu, s.outstu_num, s.outtea, s.outtea_num,
                l.name as l_level,
                d.name as college_name
            ')
            ->join('jslevel l', 'l.id=c.level', 'LEFT')
            ->join('jssetting s', 's.cuid=c.cuid', 'LEFT')
            ->join('department d', 'd.id=c.college', 'LEFT')
            ->where('c.cuid', $cuid)
            ->where('c.is_delete', 0)
            ->find();
            
        if (!$competition) {
            return json(['status' => 'error', 'message' => '竞赛不存在']);
        }
        
        // 检查用户是否有权限查看该竞赛
        if ($userMode == 1 || $userMode == 2) {
            // 学生和教师只能查看本学院或全校的竞赛
            if ($competition['college'] !== null && $competition['college'] != $userCollege) {
                return json(['status' => 'error', 'message' => '您没有权限查看该竞赛']);
            }
        } elseif ($userMode == 5) {
            // 院级管理员只能查看本学院的竞赛
            if ($competition['college'] != $userCollege) {
                return json(['status' => 'error', 'message' => '您没有权限查看该竞赛']);
            }
        }
        
        return json(['status' => 'success', 'message' => $competition]);
    }
} 