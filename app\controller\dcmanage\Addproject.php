<?php

namespace app\controller\dcmanage;

use app\BaseController;
use app\model\Dcexpected;
use app\model\Dcperiod;
use app\model\Dcprogress;
use app\model\Dcproject;
use app\model\Dctype;
use app\model\File;
use app\model\Member;
use app\model\Teacher;
use app\validate\CheckAddDc;
use Ramsey\Uuid\Uuid;
use think\facade\Db;

class Addproject extends BaseController
{
    public function addProject($uid='')
    {
        $data=input('post.data');
        if (!$data){
            //这里是打开这个页面
            $types = Dctype::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            $periods = Dcperiod::
            where('is_delete',0)
                ->field('id as value,name as label')
                ->select();
            if ($uid){
                // 检查是否为超级管理员
                if (session('user.usermode') == 11) {
                    // 超级管理员权限验证
                    LogExecution('超级管理员进入项目修改页');
                } else {
                    // 普通用户需要验证权限
                    $old_member=Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
                    if (!$old_member || $old_member['username']!=session('user.username')){
                        LogExecution(session('user.name').'你不是项目的负责人');
                        return json(['status'=>'error','message' => '你不是项目的负责人']);
                    }
                }

                //修改项目要先输出原来信息
                $project=Dcproject::where('is_delete',0)->where('uid',$uid)->find();

                // 检查项目状态是否允许修改
                if (!$this->canEditProject($project)) {
                    LogExecution(session('user.name').'项目状态不允许修改，当前状态：'.$project['status']);
                    return json(['status'=>'error','message' => '当前项目状态不允许修改']);
                }

                $member=Member::alias('m')
                    ->join('user u', 'u.username=m.username','LEFT')
                    ->where('m.is_delete',0)
                    ->where('u.status',0)
                    ->where('m.uid',$uid)
                    ->where('m.rank','!=',1)
                    ->field('u.name as label,m.username as value')
//                    ->join('department d','d.id=u.college','LEFT')
//                    ->join('major mj','mj.id=u.major','LEFT')
                    ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
                    ->select();
//                    ->column('m.username');
                $teacher=Teacher::alias('t')
                    ->join('user u', 'u.username=t.username','LEFT')
                    ->where('t.is_delete',0)
                    ->where('u.status',0)
                    ->where('t.uid',$uid)
                    ->where('t.type',0)
                    ->field('t.username as value,u.name as label')
                    ->join('department d','d.id=u.college','LEFT')
                    ->join('major m','m.id=u.major','LEFT')
                    ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
//                    ->column('t.username');
                    ->select();
//                $outstudent=Member::alias('m')
//                    ->where('m.is_delete',0)
//                    ->where('m.uid',$uid)
//                    ->where('m.type',1)
//                    ->field('m.name,m.unit,m.phone,m.email')
//                    ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
//                    ->select();
                $outteacher=Teacher::alias('t')
                    ->where('t.is_delete',0)
                    ->where('t.uid',$uid)
                    ->where('t.type',1)
                    ->field('t.name,t.unit,t.job,t.phone,t.email')
                    ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
                    ->find();
                //项目成果和额外内容
                $query=Dcexpected::where('is_delete',0)->where('uid',$uid)->find();
                $expected=[];
                if ($query) {
                    $lists = [
                        'lunwen_num',
                        'zhuanli',
                        'diaochabaogao',
                        'shangyejihuashu',
                        'zhuzuo',
                        'zuopin_num',
                        'other',
                        'gzh',
                        'wz',
                        'rj',
                        'xcx',
                        'app',
                        'yyh',
                        'wk',
                        'sp',
                        'hb'
                    ];
                    foreach ($lists as $i) {
                        if ($query->$i) {
                            if ($i=='lunwen_num'){
                                array_push($expected, "论文");
                            }elseif($i=='zhuanli'){
                                array_push($expected, "专利");
                            }elseif($i=='diaochabaogao'){
                                array_push($expected, "调查报告");
                            }elseif($i=='shangyejihuashu'){
                                array_push($expected, "商业计划书");
                            }elseif($i=='zhuzuo'){
                                array_push($expected, "著作");
                            }elseif($i=='zuopin_num'){
                                array_push($expected, "作品");
                            }elseif($i=='other'){
                                array_push($expected, "其他");
                            }elseif($i=='gzh'){
                                array_push($expected, "公众号");
                            }elseif($i=='wz'){
                                array_push($expected, "网站");
                            }elseif($i=='rj'){
                                array_push($expected, "软件");
                            }elseif($i=='xcx'){
                                array_push($expected, "小程序");
                            }elseif($i=='app'){
                                array_push($expected, "APP");
                            }elseif($i=='yyh'){
                                array_push($expected, "运营号");
                            }elseif($i=='wk'){
                                array_push($expected, "微课");
                            }elseif($i=='sp'){
                                array_push($expected, "视频");
                            }elseif($i=='hb'){
                                array_push($expected, "绘本");
                            }
                        }
                    }
    
                }

                // 撤销：不再在addProject中读取Dcmidchange的new_members、new_teachers
                // 只保留原有成员、教师的回显逻辑
                $project['members'] = array_column($member->toArray(), 'value');
                $project['teachers'] = array_column($teacher->toArray(), 'value');
                $project['outteacher'] = [
                    'name' => isset($outteacher['name']) ? $outteacher['name'] : '',
                    'phone' => isset($outteacher['phone']) ? $outteacher['phone'] : '',
                    'email' => isset($outteacher['email']) ? $outteacher['email'] : '',
                    'job' => isset($outteacher['job']) ? $outteacher['job'] : '',
                    'unit' => isset($outteacher['unit']) ? $outteacher['unit'] : '',
                ];
                $project['teachers']=$teacher;
                
                // 获取原有的立项文件信息
                $file1=File::where('is_delete',0)->where('uid',$uid)->where('type',1)->find();
                $project['fileurl']=$file1 ? $file1['path'] : '';
                $project['filename']=$file1 ? $file1['name'] : '';
                
                $project['expected']=$expected;//预期成功
                $project['extras']=[
                    'lunwen' => ['level' => $query['lunwen_level'], 'num' =>$query['lunwen_num']],
                    'zuopin' => ['class' => $query['zuopin_class'], 'num' => $query['zuopin_num']],
                    'zhuanli' => $query['zhuanli'],
                    'diaochabaogao' => $query['diaochabaogao'],
                    'shangyejihuashu' => $query['shangyejihuashu'],
                    'zhuzuo' => $query['zhuzuo'],
                    'gzh' => $query['gzh'],
                    'wz' => $query['wz'],
                    'rj' => $query['rj'],
                    'xcx' => $query['xcx'],
                    'app' => $query['app'],
                    'yyh' => $query['yyh'],
                    'wk' => $query['wk'],
                    'sp' => $query['sp'],
                    'hb' => $query['hb'],
                    'other' => $query['other'],
                ];
//                $project['class']=(string)$project['class'];
//                $project['proid']=(string)$project['proid'];


//                return json($project);




                // 检查是否为超级管理员
                $is_admin = (session('user.usermode') == 11);
                if ($is_admin) {
                    LogExecution('超级管理员进入项目修改页');
                } else {
                    LogExecution('进入项目修改页');
                }
                return view('dcmanage/addProject',['types'=>$types,'periods'=>$periods,'project'=>$project,'admin'=>$is_admin]);

            }else{
                // 检查是否为超级管理员
                $is_admin = (session('user.usermode') == 11);
                if ($is_admin) {
                    LogExecution('超级管理员进入项目立项页');
                } else {
                    LogExecution('进入项目立项页');
                }
                return view('dcmanage/addProject',['types'=>$types,'periods'=>$periods,'admin'=>$is_admin]);
            }
        }else{
            $types='';
            $validate = new CheckAddDc();
            
            // 判断是新增还是修改项目
            if (isset($data['uid'])) {
                // 修改项目时使用不同的验证规则
                $validate->rule([
                    'name'          => 'require',
                    'time'          => 'require',
                    'members'       => 'require',
                    'teachers'      => 'require',
                    'type'          => 'require',
                    'period'        => 'require',
                    'introduction'  => 'require',
                    'reason'        => 'require',
                    'innovation'    => 'require',
                    'schedule'      => 'require',
                    'budget'        => 'require',
                    'plan'          => 'require',
                    // 'fileurl'    => 'require', // 修改时不必填
                    'expected'      => 'require',
                    'shangyehua'    => 'require',
                ]);
            }
            
            // 进行验证
            if (!$validate->check($data)) {
                // 验证失败，返回错误信息
                return json(['status'=>'error','message' => $validate->getError()]);
            }
            //            return $data;
            //这里是用户提交
            //验证数据
            // 实例化验证器

            // 验证成功，处理数据，整理数据
            $project=[
                'uid'=>Uuid::uuid4()->toString(),
                'level' => 0, // 待定级
                'status' => 1,
                'name' => $data['name'],
                'time' => $data['time'], // 假设 $data 中有 'time' 键
                'type' => $data['type'], // 假设 $data 中有 'type' 键
                'period' => $data['period'], // 假设 $data 中有 'period' 键
                'introduction' => $data['introduction'], // 假设 $data 中有 'introduction' 键
                'reason' => $data['reason'], // 假设 $data 中有 'reason' 键
                'innovation' => $data['innovation'], // 假设 $data 中有 'innovation' 键
                'schedule' => $data['schedule'], // 假设 $data 中有 'schedule' 键
                'budget' => $data['budget'], // 假设 $data 中有 'budget' 键
                'plan' => $data['plan'], // 假设 $data 中有 'plan' 键
                'shangyehua'=>$data['shangyehua'],
            ];
            if(isset($data['uid'])){
                $types='update';
                //修改项目
                $uid=$data['uid'];
                // 检查是否为超级管理员
                if (session('user.usermode') == 11) {
                    LogExecution('超级管理员修改项目立项信息');
                } else {
                    LogExecution('用户修改项目立项信息');
                    $old_member=Member::where('is_delete',0)->where('uid',$uid)->where('rank',1)->where('dc',1)->find();
                    if (!$old_member || $old_member['username']!=session('user.username')){
                        LogExecution(session('user.name').'你不是项目的负责人');
                        return json(['status'=>'error','message' => '你不是项目的负责人']);
                    }
                }
                $old_project=Dcproject::where('is_delete',0)->where('uid',$uid)->find();
                if (!$old_project){
                    if (session('user.usermode') == 11) {
                        LogExecution('超级管理员大创项目不存在');
                        return json(['status'=>'error','message' => '超级管理员大创项目不存在']);
                    } else {
                        LogExecution('大创项目不存在');
                        return json(['status'=>'error','message' => '大创项目不存在']);
                    }
                }
                // 只有普通用户需要检查项目状态，超级管理员可以修改任意状态的项目
                if (session('user.usermode') != 11) {
                    if ($data['status']!=1&&$data['status']!=12&&$data['status']!=13&&$data['status']!=14){
                        LogExecution('当前不是可修改立项信息状态');
                        return json(['status'=>'error','message' => '当前不是可修改立项信息状态']);
                    }
                } else {
                    LogExecution('超级管理员可以修改任意状态的项目');
                }
                $project['uid']=$uid;


            }elseif(!isset($data['uid'])){
                $types='add';

                //新增项目
                if (session('user.usermode') == 11) {
                    LogExecution('超级管理员提交项目立项信息');
                } else {
                    LogExecution('用户提交项目立项信息');
                }

            }
        }
            //上传的文件
            if (isset($data['uid'])) {
                // 修改项目时，如果用户没有上传新文件，则保持原有文件
                if (empty($data['fileurl']) || $data['fileurl'] == '') {
                    // 获取原有文件信息
                    $old_file = File::where('is_delete',0)->where('uid',$project['uid'])->where('type',1)->find();
                    if ($old_file) {
                        $file = [
                            'uid'=>$project['uid'],
                            'path'=>$old_file['path'],
                            'type'=>1
                        ];
                    } else {
                        // 如果没有原有文件，返回错误
                        return json(['status'=>'error','message' => '请上传项目方案文件']);
                    }
                } else {
                    // 用户上传了新文件
                    $file=[
                        'uid'=>$project['uid'],
                        'path'=>$data['fileurl'],
                        'type'=>1
                    ];
                }
            } else {
                // 新增项目时，文件是必填的
                $file=[
                    'uid'=>$project['uid'],
                    'path'=>$data['fileurl'],
                    'type'=>1
                ];
            }
            //项目预期成果
            //逻辑是$data['expected']数组里面作为索引
            $expected=[
                'uid'=>$project['uid'],
            ];
            foreach ($data['expected'] as $type){
                if ($type=='论文'){
                    $expected['lunwen_level']=$data['extras']['lunwen']['level'];
                    $expected['lunwen_num']=$data['extras']['lunwen']['num'];
                }elseif ($type=='专利'){
                    $expected['zhuanli']=$data['extras']['zhuanli'];
                }elseif ($type=='调查报告'){
                    $expected['diaochabaogao']=$data['extras']['diaochabaogao'];
                }elseif ($type=='商业计划书'){
                    $expected['shangyejihuashu']=$data['extras']['shangyejihuashu'];

                }elseif ($type=='著作'){
                    $expected['zhuzuo']=$data['extras']['zhuzuo'];

                }elseif ($type=='作品'){
                    $expected['zuopin_class']=$data['extras']['zuopin']['class'];
                    $expected['zuopin_num']=$data['extras']['zuopin']['num'];
                }elseif ($type=='公众号'){
                    $expected['gzh']=$data['extras']['gzh'];

                }elseif ($type=='网站'){
                    $expected['wz']=$data['extras']['wz'];

                }elseif ($type=='软件'){
                    $expected['rj']=$data['extras']['rj'];

                }elseif ($type=='小程序'){
                    $expected['xcx']=$data['extras']['xcx'];

                }elseif ($type=='APP'){
                    $expected['app']=$data['extras']['app'];

                }elseif ($type=='运营号'){
                    $expected['yyh']=$data['extras']['yyh'];

                }elseif ($type=='微课'){
                    $expected['wk']=$data['extras']['wk'];

                }elseif ($type=='视频'){
                    $expected['sp']=$data['extras']['sp'];

                }elseif ($type=='绘本（图册）'){
                    $expected['hb']=$data['extras']['hb'];

                }elseif ($type=='其他'){
                    $expected['other']=$data['extras']['other'];

                }
            }
            //项目成员
            $members=[];
            //添加负责人
            $rank=1;
            if (session('user.usermode') == 11) {
                // 超级管理员修改时，保持原有的项目负责人
                $old_member=Member::where('is_delete',0)->where('uid',$project['uid'])->where('rank',1)->where('dc',1)->find();
                if ($old_member) {
                    $members[]=[
                        'uid'=>$project['uid'],
                        'username'=>$old_member['username'],
                        'rank'=>$rank,
                        'type'=>0,
                        'dc'=>1
                    ];
                } else {
                    // 如果没有找到原有负责人，使用第一个成员作为负责人
                    if (!empty($data['members'])) {
                        $members[]=[
                            'uid'=>$project['uid'],
                            'username'=>$data['members'][0],
                            'rank'=>$rank,
                            'type'=>0,
                            'dc'=>1
                        ];
                    }
                }
            } else {
                // 普通用户修改时，使用当前登录用户作为负责人
                $members[]=[
                    'uid'=>$project['uid'],
                    'username'=>session('user.username'),
                    'rank'=>$rank,
                    'type'=>0,
                    'dc'=>1
                ];
            }
            
            foreach ($data['members'] as $member){
                if (session('user.usermode') == 11) {
                    // 超级管理员修改时，跳过负责人（已经在上面添加了）
                    $old_member=Member::where('is_delete',0)->where('uid',$project['uid'])->where('rank',1)->where('dc',1)->find();
                    if ($old_member && $member==$old_member['username']){
                        continue;
                    }
                } else {
                    // 普通用户修改时，跳过当前登录用户
                    if ($member==session('user.username')){
                        continue;
                    }
                }
                $rank++;
                $members[]=[
                    'uid'=>$project['uid'],
                    'username'=>$member,
                    'rank'=>$rank,
                    'type'=>0,
                    'dc'=>1
                ];
            }



            //指导教师
            if (count($data['teachers'])==2&&$data['outteacher']['name']){
                return json(['status'=>'error','message' => '校内指导教师和校外指导教师总和最多为2']);
            }else{
                $teachers=[];
                $rank=0;
                //添加校内指导教师
                foreach ($data['teachers'] as $teacher){
                    $rank++;
                    $teachers[]=[
                        'uid'=>$project['uid'],
                        'username'=>$teacher,
                        'rank'=>$rank,
                        'type'=>0,
                        'dc'=>1

                    ];
                }
                $outteachers=[];
                if ($data['outteacher']['name']){
                    //添加校外指导教师，校外指导教师排位一定在校内指导教师后面
//                    foreach ($data['outteacher'] as $teacher){
                    $rank++;
                    $outteachers[]=[
                        'uid'=>$project['uid'],
                        'username'=>'',
                        'rank'=>$rank,
                        'name'=>$data['outteacher']['name'],
                        'unit'=>$data['outteacher']['unit'],
                        'job'=>$data['outteacher']['job'],
                        'phone'=>$data['outteacher']['phone'],
                        'email'=>$data['outteacher']['email'],
                        'type'=>1,
                        'dc'=>1


                    ];
//                    }
                }

            }

            if ($types=='update'){
                // 判断是否为中期变更（假设$data['midchange']存在且为true时为中期变更）
                if (!empty($data['midchange'])) {
                    // 中期变更时，先软删除原有成员和教师
                    Member::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                        ->where('dc', 1)
                        ->update(['is_delete' => 1]);
                    Teacher::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                        ->where('dc', 1)
                        ->update(['is_delete' => 1]);
                }

                if ($old_project['status']==1){
                    $project['status']=1;
                }elseif ($old_project['status']==12){
                    $project['status']=1;
                }elseif ($old_project['status']==13){
                    $project['status']=2;
                }elseif ($old_project['status']==14){
                    $project['status']=3;
                }
                //添加项目进度
                if (session('user.usermode') == 11) {
                    $progress=[
                        'uid'=>$project['uid'],
                        'action'=>'超级管理员修改立项申请',
                        'remark'=>'超级管理员'.session('user.name').'修改立项申请'
                    ];
                } else {
                    $progress=[
                        'uid'=>$project['uid'],
                        'action'=>'修改立项申请',
                        'remark'=>'学生提交修改立项申请'
                    ];
                }

                // 开启数据库事务
                Db::startTrans();

                try {
                    // 删除教师和成员重新添加（无论是立项修改还是中期变更都统一处理）
                    Member::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                        ->where('dc', 1)
                        ->update(['is_delete' => 1]);
                    Teacher::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                        ->where('dc', 1)
                        ->update(['is_delete' => 1]);

                    // 删除预期成果
                    Dcexpected::where('is_delete', 0)
                        ->where('uid', $project['uid'])
                        ->update(['is_delete' => 1]);

                    // 插入新的数据
                    if (
                        Dcexpected::insert($expected) &&
                        Member::insertAll($members) &&
                        Teacher::insertAll($teachers) &&
                        Dcprogress::insert($progress)
                    ) {
                        Teacher::insertAll($outteachers) ;
                        // 更新项目记录和文件记录
                        Dcproject::where('is_delete', 0)
                            ->where('uid', $project['uid'])
                            ->update($project);
                        // 文件处理略
                        Db::commit();
                        LogExecution($project['uid'].'项目修改成功');
                        return ['status'=>'success','message'=>'项目修改成功'];
                    } else {
                        Db::rollback();
                        LogExecution($project['uid'].'项目修改失败，数据插入异常');
                        return ['status'=>'error','message'=>'项目修改失败，数据插入异常'];
                    }
                } catch (\Exception $e) {
                    Db::rollback();
                    LogExecution($project['uid'].'项目修改失败：'.$e->getMessage());
                    return ['status'=>'error','message'=>'项目修改失败，数据表插入异常'];
                }
            }elseif ($types=='add'){
                //添加项目进度
                if (session('user.usermode') == 11) {
                    $progress=[
                        'uid'=>$project['uid'],
                        'action'=>'超级管理员立项申请',
                        'remark'=>'超级管理员'.session('user.name').'提交立项申请'
                    ];
                } else {
                    $progress=[
                        'uid'=>$project['uid'],
                        'action'=>'立项申请',
                        'remark'=>'学生提交立项申请'
                    ];
                }
                //插入之前检查项目是否已经存在
                if (Dcproject::where('is_delete',0)->where('name',$project['name'])->find()){
                    if (session('user.usermode') == 11) {
                        LogExecution($project['uid'].'超级管理员立项失败，项目名称已存在');
                        return ['status'=>'error','message'=>'超级管理员立项失败，项目名称已存在'];
                    } else {
                        LogExecution($project['uid'].'立项失败，项目名称已存在');
                        return ['status'=>'error','message'=>'立项失败，项目名称已存在'];
                    }
                }
                try {
                    // 开启事务
                    Db::startTrans();

                    // 执行数据库操作
                    $projectResult = Dcproject::insert($project);
                    $fileResult = File::insert($file);
                    $expectedResult = Dcexpected::insert($expected);
                    $memberResult = Member::insertAll($members);
                    $teacherResult = Teacher::insertAll($teachers);
                    $outteacherResult = Teacher::insertAll($outteachers);

                    $progressResult = Dcprogress::insert($progress);

                    // 检查所有操作是否成功
                    if ($projectResult && $fileResult && $expectedResult && $memberResult && $teacherResult && $progressResult) {
                        // 提交事务
                        Db::commit();

                        // 记录日志
                        if (session('user.usermode') == 11) {
                            LogExecution($project['uid'].'超级管理员立项成功');
                        } else {
                            LogExecution($project['uid'].'立项成功');
                        }

                        // 返回成功响应
                        return ['status' => 'success', 'message' => '立项成功'];
                    } else {
                        // 回滚事务
                        Db::rollback();

                        // 记录日志
                        if (session('user.usermode') == 11) {
                            LogExecution($project['uid'].'超级管理员立项失败，请检查环境及填写内容');
                        } else {
                            LogExecution($project['uid'].'立项失败，请检查环境及填写内容');
                        }

                        // 返回错误响应
                        return ['status' => 'error', 'message' => '立项失败，请检查环境及填写内容'];
                    }
                } catch (\Exception $e) {
                    // 捕获异常，回滚事务
                    Db::rollback();

                    // 记录异常日志
                    if (session('user.usermode') == 11) {
                        LogExecution($project['uid'].'超级管理员立项失败，系统异常，请稍后再试');
                    } else {
                        LogExecution($project['uid'].'立项失败，系统异常，请稍后再试');
                    }

                    // 返回错误响应
                    return ['status' => 'error', 'message' => '立项失败，系统异常，请稍后再试'];
                }
            }





            //查询成员是否已经参加>三次大创或者做过>1次负责人
//            foreach ($members as $member){
//                if (Member::)
//            }



    }
    
    /**
     * 检查项目是否可以修改
     */
    private function canEditProject($project) {
        // 超级管理员可以修改任何状态的项目
        if (session('user.usermode') == 11) {
            return true;
        }
        
        // 学生只能修改特定状态的项目
        // 状态1：等待指导教师立项审核
        // 状态2：等待学院立项审核  
        // 状态3：等待学校立项审核
        // 状态12-20：各种驳回状态
        $allowedStatus = [1, 2, 3, 12, 13, 14, 15, 16, 17, 18, 19, 20];
        return in_array($project['status'], $allowedStatus);
    }
}