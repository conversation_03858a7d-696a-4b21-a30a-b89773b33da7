<?php
// 权限测试页面
require_once 'vendor/autoload.php';

// 初始化ThinkPHP框架
$app = new \think\App();
$app->initialize();

use app\service\PermissionService;

echo "<h1>权限控制测试页面</h1>";

// 模拟不同用户组的权限测试
$testUsers = [
    ['usermode' => 1, 'name' => '学生'],
    ['usermode' => 2, 'name' => '教师'],
    ['usermode' => 3, 'name' => '大创院级管理员'],
    ['usermode' => 4, 'name' => '大创校级管理员'],
    ['usermode' => 5, 'name' => '竞赛院级管理员'],
    ['usermode' => 6, 'name' => '竞赛校级管理员'],
    ['usermode' => 7, 'name' => '英才库校级管理员'],
    ['usermode' => 8, 'name' => '新闻管理员1'],
    ['usermode' => 11, 'name' => '超级管理员'],
];

$testPermissions = [
    'dc_view' => '查看大创项目',
    'dc_apply' => '申请大创项目',
    'dc_check_teacher' => '教师审核大创项目',
    'dc_check_college' => '学院审核大创项目',
    'js_view' => '查看竞赛项目',
    'js_apply' => '申请竞赛项目',
    'js_check_teacher' => '教师审核竞赛项目',
    'js_check_college' => '学院审核竞赛项目',
    'yck_view' => '查看英才库',
    'yck_check' => '审核英才库',
    'news_view' => '查看新闻',
    'news_edit' => '编辑新闻',
    'user_manage' => '管理用户',
    'system_manage' => '系统管理',
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>用户组</th>";

foreach ($testPermissions as $permission => $description) {
    echo "<th>$description</th>";
}
echo "</tr>";

foreach ($testUsers as $user) {
    echo "<tr>";
    echo "<td>{$user['name']} ({$user['usermode']})</td>";
    
    // 模拟设置用户组
    session('user.usermode', $user['usermode']);
    
    foreach ($testPermissions as $permission => $description) {
        $hasPermission = PermissionService::hasPermission($permission);
        $color = $hasPermission ? 'green' : 'red';
        $text = $hasPermission ? '✓' : '✗';
        echo "<td style='text-align: center; color: $color;'>$text</td>";
    }
    echo "</tr>";
}

echo "</table>";

echo "<h2>权限服务方法测试</h2>";

// 测试学院权限
echo "<h3>学院权限测试</h3>";
session('user.usermode', 3);
session('user.college', '计算机学院');

$collegePermission = PermissionService::canOperateCollege('计算机学院');
echo "<p>院级管理员操作本学院: " . ($collegePermission ? '✓ 允许' : '✗ 拒绝') . "</p>";

$collegePermission = PermissionService::canOperateCollege('其他学院');
echo "<p>院级管理员操作其他学院: " . ($collegePermission ? '✓ 允许' : '✗ 拒绝') . "</p>";

// 测试项目权限
echo "<h3>项目权限测试</h3>";
$projectPermission = PermissionService::canOperateProject('test123', 'dc');
echo "<p>大创项目操作权限: " . ($projectPermission ? '✓ 允许' : '✗ 拒绝') . "</p>";

$projectPermission = PermissionService::canOperateProject('test123', 'js');
echo "<p>竞赛项目操作权限: " . ($projectPermission ? '✓ 允许' : '✗ 拒绝') . "</p>";

echo "<h2>测试完成</h2>";
echo "<p>如果所有测试都显示正确的权限结果，说明权限控制功能正常工作。</p>";
?> 