{include file="public_index/_header"}

<link rel="stylesheet" href="../../static/css/index/newsdetail.css">

<div id="app">
  <p style="margin: 1.5rem 0 1.5rem 5%;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="/index">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/news-class">{$detail.c_name}</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="news-detail?news={$detail.id}">{$detail.title}</a>
</p>
    <div class="news-container">
      <!-- 动态背景粒子效果 -->
      <!-- <div class="particles-bg"></div> -->
      

      <!-- 新闻卡片 -->
      <el-card 
        class="news-card"
        :body-style="{ padding: '24px', borderRadius: '16px' }"
        shadow="hover"
      >
        <!-- 标题区域 -->
        <div class="header-section">
          <h1 class="news-title">{$detail.title}</h1>
          <div class="meta-info">
            <el-tag class="category-tag">{$detail.c_name}</el-tag>
            <span class="author">发布者：{$detail.auth}</span>
            <span class="time">{$detail.time}</span>
          </div>
        </div>
   
        <!-- 内容区域 -->
        <div class="content-section">
          <div id="content" class="news-content">
          </div>
        </div>
      </el-card>
    </div>

  </div>
    <script src="../../static/js/index/newsdetail.js"></script>
    <script>
// 先解码Base64
const decodedContent = decodeURIComponent(atob("{$detail.content}"));
 
// 再解码HTML实体
const parser = new DOMParser();
const doc = parser.parseFromString(decodedContent, 'text/html');
const finalContent = doc.documentElement.textContent;
      const container = document.getElementById('content');
      
      // 使用beforeend位置插入解析后的HTML
      container.insertAdjacentHTML('beforeend', finalContent);
      </script>
    {include file="public_index/_footer"}
