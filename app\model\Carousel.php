<?php

namespace app\model;

use think\Model;

class Carousel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'carousel';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 关联新闻
    public function news()
    {
        return $this->belongsTo(Newsdetail::class, 'news_id', 'id');
    }
    
    // 获取启用状态的轮播
    public function scopeActive($query)
    {
        return $query->where('status', 1)->where('is_delete', 0);
    }
    
    // 按排序获取轮播
    public function scopeOrdered($query)
    {
        return $query->order('sort_order', 'asc')->order('id', 'desc');
    }
} 