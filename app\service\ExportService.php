<?php

namespace app\service;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use app\model\Department;
use app\model\Major;
use app\model\Usermode;
use app\model\Dclevel;
use app\model\Dcstatus;
use app\model\Jsstatus;
use app\model\Jsaward;

class ExportService
{
    /**
     * 生成Excel文件
     * @param array $data 数据数组
     * @param array $headers 表头数组
     * @param string $filename 文件名
     * @return string 文件路径
     */
    public function generateExcel($data, $headers, $filename)
    {
        // 确保导出目录存在
        $exportDir = root_path() . 'public/exports/';
        if (!is_dir($exportDir)) {
            mkdir($exportDir, 0755, true);
        }
        
        // 创建新的Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }
        
        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];
        
        $sheet->getStyle('A1:' . $this->getColumnLetter(count($headers)) . '1')->applyFromArray($headerStyle);
        
        // 填充数据
        $row = 2;
        foreach ($data as $item) {
            $col = 'A';
            foreach ($item as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }
        
        // 设置数据区域样式
        $dataStyle = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];
        
        if (count($data) > 0) {
            $sheet->getStyle('A2:' . $this->getColumnLetter(count($headers)) . (count($data) + 1))->applyFromArray($dataStyle);
        }
        
        // 自动调整列宽
        foreach (range('A', $this->getColumnLetter(count($headers))) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // 生成文件
        $writer = new Xlsx($spreadsheet);
        $filepath = root_path() . 'public/exports/' . $filename;
        $writer->save($filepath);
        
        return $filepath;
    }
    
    /**
     * 获取列字母
     * @param int $index
     * @return string
     */
    private function getColumnLetter($index)
    {
        $letter = '';
        while ($index > 0) {
            $index--;
            $letter = chr(65 + ($index % 26)) . $letter;
            $index = intval($index / 26);
        }
        return $letter;
    }
    
    /**
     * 格式化用户数据
     * @param array $data
     * @return array
     */
    public function formatUserData($data)
    {
        $formattedData = [];
        
        foreach ($data as $user) {
            $formattedData[] = [
                $user['username'] ?? '',
                $user['name'] ?? '',
                $user['phone'] ?? '',
                $user['email'] ?? '',
                $this->convertIdToText($user['usermode'], 'usermode'),
                $this->convertIdToText($user['college'], 'department'),
                $this->convertIdToText($user['major'], 'major'),
                $user['job'] ?? '',
                $user['grade'] ?? '',
                $this->getStatusText($user['status'] ?? 0)
            ];
        }
        
        return $formattedData;
    }
    
    /**
     * 格式化大创项目数据
     * @param array $data
     * @return array
     */
    public function formatDcProjectData($data)
    {
        $formattedData = [];
        
        foreach ($data as $project) {
            $formattedData[] = [
                $project['uid'] ?? '',
                $project['proid'] ?? '',
                $project['name'] ?? '',
                $project['l_level'] ?? '',
                $project['s_status'] ?? '',
                $project['m_names'] ?? '',
                $project['t_names'] ?? ''
            ];
        }
        
        return $formattedData;
    }
    
    /**
     * 格式化竞赛项目数据
     * @param array $data
     * @return array
     */
    public function formatJsProjectData($data)
    {
        $formattedData = [];
        
        foreach ($data as $project) {
            $formattedData[] = [
                $project['c_name'] ?? '',
                $project['name'] ?? '',
                $project['class'] ?? '',
                $project['m_names'] ?? '',
                $project['t_names'] ?? '',
                $project['time'] ?? '',
                $project['award'] ?? '',
                $project['s_status'] ?? ''
            ];
        }
        
        return $formattedData;
    }
    
    /**
     * 格式化部门数据
     * @param array $data
     * @return array
     */
    public function formatDepartmentData($data)
    {
        $formattedData = [];
        foreach ($data as $dept) {
            $majorNames = [];
            if (isset($dept['majors'])) {
                // 兼容 Collection 和数组
                if ($dept['majors'] instanceof \think\model\Collection) {
                    foreach ($dept['majors'] as $major) {
                        $majorNames[] = $major->name;
                    }
                } elseif (is_array($dept['majors'])) {
                    foreach ($dept['majors'] as $major) {
                        $majorNames[] = is_object($major) ? $major->name : $major['name'];
                    }
                }
            }
            $formattedData[] = [
                $dept['name'] ?? '',
                implode(', ', $majorNames),
                $dept['created_at'] ?? '',
                $dept['updated_at'] ?? ''
            ];
        }
        return $formattedData;
    }
    
    /**
     * 格式化英才列表数据
     * @param array $data
     * @return array
     */
    public function formatYckListData($data)
    {
        $formattedData = [];
        
        foreach ($data as $user) {
            $formattedData[] = [
                $user['index'] ?? '',
                $user['name'] ?? '',
                $user['department_name'] ?? '',
                $user['major_name'] ?? '',
                $user['tags'] ?? '',
                $user['mark'] ?? ''
            ];
        }
        
        return $formattedData;
    }
    
    /**
     * 格式化新闻数据
     * @param array $data
     * @return array
     */
    public function formatNewsData($data)
    {
        $formattedData = [];
        
        foreach ($data as $news) {
            $formattedData[] = [
                $news['c_name'] ?? '',
                $news['title'] ?? '',
                $news['auth'] ?? '',
                $news['created_at'] ?? '',
                $news['updated_at'] ?? ''
            ];
        }
        
        return $formattedData;
    }
    
    /**
     * 数字转文字
     * @param mixed $id
     * @param string $type
     * @return string
     */
    public function convertIdToText($id, $type)
    {
        if (empty($id)) {
            return '';
        }
        
        switch ($type) {
            case 'department':
                $department = Department::where('id', $id)->where('is_delete', 0)->find();
                return $department ? $department->name : '';
                
            case 'major':
                $major = Major::where('id', $id)->where('is_delete', 0)->find();
                return $major ? $major->name : '';
                
            case 'usermode':
                $usermode = Usermode::where('id', $id)->where('is_delete', 0)->find();
                return $usermode ? $usermode->group_name : '';
                
            case 'dclevel':
                $level = Dclevel::where('id', $id)->where('is_delete', 0)->find();
                return $level ? $level->name : '';
                
            case 'dcstatus':
                $status = Dcstatus::where('id', $id)->where('is_delete', 0)->find();
                return $status ? $status->name : '';
                
            case 'jsstatus':
                $status = Jsstatus::where('id', $id)->where('is_delete', 0)->find();
                return $status ? $status->name : '';
                
            case 'jsaward':
                $award = Jsaward::where('id', $id)->where('is_delete', 0)->find();
                return $award ? $award->type . ' - ' . $award->level : '';
                
            default:
                return $id;
        }
    }
    
    /**
     * 获取状态文字
     * @param int $status
     * @return string
     */
    public function getStatusText($status)
    {
        switch ($status) {
            case 0:
                return '正常';
            case 1:
                return '停用';
            default:
                return '未知';
        }
    }
    
    /**
     * 生成文件名
     * @param string $prefix
     * @return string
     */
    public function generateFilename($prefix)
    {
        $date = date('Ymd');
        return $prefix . '_' . $date . '.xlsx';
    }
    
    /**
     * 下载文件
     * @param string $filepath
     * @param string $filename
     */
    public function downloadFile($filepath, $filename)
    {
        if (file_exists($filepath)) {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($filepath));
            header('Cache-Control: max-age=0');
            
            readfile($filepath);
            exit;
        }
    }
    
    /**
     * 获取文件相对路径
     * @param string $filename
     * @return string
     */
    public function getFileUrl($filename)
    {
        return '/exports/' . $filename;
    }
} 