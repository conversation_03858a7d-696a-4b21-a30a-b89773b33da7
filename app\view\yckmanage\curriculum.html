{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/yck/curriculum.css">
<div id="app">
    <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
      <a style="text-decoration: none;color: #00000097;" href="">首页</a>
      >
      <a style="text-decoration: none;color: #00000097;" href="">英才库</a>
      {if $username neq session('user.username')}
      >
      <a style="text-decoration: none;color: #00000097;" href="yck-lists">英才列表</a>
      {/if}  
      >
      <a style="text-decoration: none;color: #00000097;" href="yck-curriculum/{$username}">简历</a>
  </p>


  <!-- 悬浮按钮 -->
  {if $username eq session('user.username')}
 <!-- 悬浮按钮 -->
 <el-button
 class="floating-button"
 type="primary"
 icon="el-icon-more"
 circle
 @click="toggleMenu"
></el-button>
<!-- 悬浮菜单 -->
<div v-if="isMenuVisible" class="floating-menu">
 <el-menu>
   <el-menu-item-group>
   <template slot="title">操作</template>
   <el-menu-item index="1" @click="handleMenuClick(1)">修改简历信息</el-menu-item>
   <el-menu-item index="2" @click="handleMenuClick(2)">联系方式统计</el-menu-item>
 </el-menu-item-group>
 </el-menu>
</div>
  {/if}  


  <!-- 修改简历信息 -->
  <el-dialog
  title="修改简历信息"
  :visible.sync="updateVisible"
  width="50%"
>
<el-form ref="updateform"   :model="updateform"  >
  <el-form-item label="简历展示信息" prop="show">
    <el-transfer v-model="updateform.show" :data="shows" :titles="['不展示', '展示']"></el-transfer>
  </el-form-item>
</el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="updateVisible = false">关 闭</el-button>
    <el-button type="primary" @click="submit_update">确 定</el-button>

  </span>
</el-dialog>

  <!-- 联系方式统计 -->
  <el-dialog
  title="联系方式统计"
  :visible.sync="contactlogVisible"
  width="30%"
>
<el-statistic :value="500" title="被查看次数">
  <template slot="suffix">
    <span  class="like">
      <i
        class="el-icon-star-on"
        style="color:red"
      ></i>
    </span>
  </template>
</el-statistic>
<el-form>
<el-form-item label="黑名单">
  <el-select
  style="width: 100%;"
  v-model="blacklist"
  multiple
  filterable
  remote
  reserve-keyword
  placeholder="请选择黑名单成员"
  :remote-method="remoteusers"
  :loading="loading">
  <el-option
    v-for="item in users"
    :key="item.value"
    :label="item.label"
    :value="item.value">
    <span style="float: left">{{ item.label }}</span>
    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
  </el-option>
</el-select>
</el-form-item>
</el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="contactlogVisible = false">关 闭</el-button>
    <el-button type="primary" @click="submit_contact">确 定</el-button>

  </span>
</el-dialog>


  <!-- 联系方式 -->
<el-dialog
  title="联系方式"
  :visible.sync="contactVisible"
  width="30%"
>
<h3>请尽快保存联系方式，今日查看不再消耗次数</h3>
  <el-form label-width="100px">
    <el-form-item  v-if="contact.phone" label="号码">
      <span>{{ contact.phone }}</span>
    </el-form-item>
    <el-form-item  v-if="contact.email" label="电子邮箱">
      <span>{{ contact.email }}</span>
    </el-form-item>
    <el-form-item v-if="contact.qq" label="QQ">
      <img  :src="`/static/files/upload/${contact.qq}`" class="avatar">
    </el-form-item>
    <el-form-item v-if="contact.wx" label="微信">
      <img  :src="`/static/files/upload/${contact.wx}`" class="avatar">
    </el-form-item>
    <el-form-item v-if="contact.dd" label="钉钉">
      <img  :src="`/static/files/upload/${contact.dd}`" class="avatar">
    </el-form-item>
    <el-form-item  v-if="contact.qywx" label="企业微信">
      <img :src="`/static/files/upload/${contact.qywx}`" class="avatar">
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="contactVisible = false">关 闭</el-button>
  </span>
</el-dialog>



  <el-container>
    <!-- 左侧 -->
    <el-aside class="left-menu">
        <div class="profile">
            <h1>{{user.name}}</h1>
            <el-tag type="success" effect="dark">{{user.job}}</el-tag>
            <div style="margin: 1rem 0">
                <el-rate
                    v-model="user.mark"
                    disabled
                    text-color="#ffd700"
                    >
                </el-rate>
            </div>
        </div>

        <el-divider></el-divider>
        <div class="skills">
            <div v-if="user.grade" class="skill-item">
                <i class="fas fa-code"></i> {{user.grade}}
            </div>
            <div class="skill-item">
                <i class="fas fa-code"></i> {{user.college}}
            </div>
            <div v-if="user.major" class="skill-item">
                <i class="fas fa-code"></i> {{user.major}}
            </div>
      </div>
      <el-divider></el-divider>

        <div class="skills">
            
            <div class="skill-item">
              <h3 >个人简介</h3>
              <i class="fas fa-code"></i> {{user.intro}}
          </div>
            <!-- <div class="skill-item" v-for="skill in skills" :key="skill">
                <i class="fas fa-code"></i> {{ skill }}
            </div> -->
        </div>
    </el-aside>

    <!-- 中间 -->
    <el-main class="center-menu">
        <h3 class="animated-text">🏆 大创项目</h3>
        <el-card class="project-card" v-for="project in dcs" :key="project.name">
            <h4>{{ project.name }}</h4>
            <p><i class="fas fa-calendar-alt"></i> {{ project.time }} | {{ project.l_level }}</p>
            <p><i class="fas fa-users"></i> 团队排位：{{ project.rank }}</p>
        </el-card>

        <h3 class="animated-text" style="margin-top: 2rem">🚀 竞赛项目</h3>
        <el-card class="project-card" v-for="competition in jss" :key="competition.name">
            <h4>{{ competition.c_name }}</h4>
            <h5>{{ competition.name }}</h5>
            <p><i class="fas fa-flag"></i> {{ competition.l_level }} | {{ competition.time }}</p>
            <p><i class="fas fa-award"></i> {{ competition.award }}</p>
            <p><i class="fas fa-users"></i> 团队排位：{{ competition.rank }}</p>

        </el-card>
    </el-main>

    <!-- 右侧 -->
    <el-main class="right-menu">
      <div class="contact-image">
        <img :src="`/static/files/upload/${user.avatar}`"
             alt="个人形象" 
             class="profile-img">
      </div>
        <h3 class="animated-text" style="margin-top: 2rem">📱 联系方式</h3>
        <el-popconfirm
            confirm-button-text='确定'
            cancel-button-text='取消'
            icon="el-icon-info"
            icon-color="red"
            title="每人每天最多可查看三位英才联系方式，是否继续查看？"
            @confirm="seecontact"
            >
            <div class="contact-item" slot="reference">
                <p><i class="fas fa-envelope"></i>点击查看联系方式</p>
            </div>
            </el-popconfirm>

        <h3 class="animated-text" style="margin-top: 2rem">🔖 个人标签</h3>
        <div>
            <span class="tag" v-for="tag in domains" :key="tag">{{ tag }}</span>
        </div>
        <h3 class="animated-text" style="margin-top: 2rem">🔖 擅长领域</h3>
        <div>
            <span class="tag" v-for="tag in tags" :key="tag">{{ tag }}</span>
        </div>
    </el-main>
</el-container>


</div> 
<script src="../../static/js/yck/curriculum.js"></script>
</body>
</html>