{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/js/competition.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">竞赛平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="js-competition">竞赛管理</a>
</p>
<el-row style="margin: auto;" :gutter="5">

  <el-col :span="3">
  
  <el-button plain type="primary" @click="exportProjects()">导出当前{{ totalData.length }}个竞赛</el-button>
  </el-col>
  
  <el-col :span="3">
    <el-button type="primary" @click="addCompetition">添加竞赛</el-button>
  </el-col>
  



    <el-col :span="3">
      <el-select v-model="search.level" placeholder="请选择竞赛级别" style="width: 100%">
        <el-option value="all" label="全部级别"></el-option>
            {foreach $search.level as $level}
        <el-option value="{$level.id}" label="{$level.name}"></el-option>
        {/foreach}
      </el-select>
    </el-col>
    <el-col :span="3">
      <el-select
      style="width: 100%"
      v-model="search.texts"
      multiple
      filterable
      allow-create
      default-first-option
      placeholder="以回车分割(竞赛名称，官网)">
    </el-select>
    </el-col>
    <el-col :span="3">
      <el-button plain type="primary" @click="select_competition">搜索</el-button>
    </el-col>
  </el-row>
<el-dialog
  :title="detailData.name || '修改竞赛'"
  :visible.sync="detailVisible"
  width="40%"
  >
  <el-form ref="detailForm" :rules="detail_rules" label-position="left" :model="detailData" label-width="15rem" >
    <el-form-item label="竞赛名称" prop="name">
      <el-input v-model="detailData.name" placeholder="请输入竞赛名称"></el-input>
    </el-form-item>
    <el-form-item label="竞赛级别" prop="l_level">
      <el-select v-model="detailData.l_level" style="width: 100%;" placeholder="请选择竞赛级别">
        {foreach $l_level as $level}
        <el-option label="{$level.label}" value="{$level.value}"></el-option>
        {/foreach}
      </el-select>
    </el-form-item>
    <el-form-item label="官网链接" prop="url">
      <el-input v-model="detailData.url" placeholder="请输入竞赛官网链接"></el-input>
    </el-form-item>
    <el-form-item  label="当前状态" prop="active">
      <el-radio v-model="detailData.active" :label="0">不可填报</el-radio>
      <el-radio v-model="detailData.active" :label="1">可填报</el-radio>
    </el-form-item>
    <el-form-item v-if="detailData.active==1" label="可填报年份" prop="years">
        <el-select
        v-model="detailData.years"
        multiple
        placeholder="选择年份"
      >
        <el-option
          v-for="year in years"
          :key="year"
          :label="year"
          :value="year"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="竞赛支持最多成员（包括负责人）" prop="member">
      <el-input-number :min="0" :max="30" v-model="detailData.member" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="竞赛支持最多指导教师" prop="teacher">
      <el-input-number :min="0" :max="30" v-model="detailData.teacher" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>

    <el-form-item label="是否支持校外成员" prop="outstu">
      <el-radio v-model="detailData.outstu" :label="0">否</el-radio>
      <el-radio v-model="detailData.outstu" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item v-if="detailData.outstu==1" label="竞赛支持最多校外成员" prop="outstu_num">
      <el-input-number :min="0" :max="30" v-model="detailData.outstu_num" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="是否支持校外指导教师" prop="outtea">
      <el-radio v-model="detailData.outtea" :label="0">否</el-radio>
      <el-radio v-model="detailData.outtea" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item v-if="detailData.outtea==1" label="竞赛支持最多校外指导教师" prop="outtea_num">
      <el-input-number :min="0" :max="30" v-model="detailData.outtea_num" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="备注"
      v-model="detailData.remark">
     </el-input>
    </el-form-item>
  </el-form>


  <span slot="footer" class="dialog-footer">
    <el-button @click="detailVisible = false">关 闭</el-button>
    <!-- 管理员显示 -->
    <el-button type="primary" @click="submit_competition">确 定 修 改</el-button>
  </span>
</el-dialog>

<!-- 添加竞赛对话框 -->
<el-dialog
  title="添加竞赛"
  :visible.sync="addVisible"
  width="40%"
  >
  <el-form ref="addForm" :rules="add_rules" label-position="left" :model="addData" label-width="15rem" >
    <el-form-item label="竞赛名称" prop="name">
      <el-input v-model="addData.name" placeholder="请输入竞赛名称"></el-input>
    </el-form-item>
    <el-form-item label="竞赛级别" prop="l_level">
      <el-select v-model="addData.l_level" style="width: 100%;" placeholder="请选择竞赛级别">
        {foreach $l_level as $level}
        <el-option label="{$level.label}" value="{$level.value}"></el-option>
        {/foreach}
      </el-select>
    </el-form-item>
    <el-form-item label="官网链接" prop="url">
      <el-input v-model="addData.url" placeholder="请输入竞赛官网链接"></el-input>
    </el-form-item>
    <el-form-item  label="当前状态" prop="active">
      <el-radio v-model="addData.active" :label="0">不可填报</el-radio>
      <el-radio v-model="addData.active" :label="1">可填报</el-radio>
    </el-form-item>
    <el-form-item v-if="addData.active==1" label="可填报年份" prop="years">
        <el-select
        v-model="addData.years"
        multiple
        placeholder="选择年份"
      >
        <el-option
          v-for="year in years"
          :key="year"
          :label="year"
          :value="year"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="竞赛支持最多成员（包括负责人）" prop="member">
      <el-input-number :min="0" :max="30" v-model="addData.member" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="竞赛支持最多指导教师" prop="teacher">
      <el-input-number :min="0" :max="30" v-model="addData.teacher" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>

    <el-form-item label="是否支持校外成员" prop="outstu">
      <el-radio v-model="addData.outstu" :label="0">否</el-radio>
      <el-radio v-model="addData.outstu" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item v-if="addData.outstu==1" label="竞赛支持最多校外成员" prop="outstu_num">
      <el-input-number :min="0" :max="30" v-model="addData.outstu_num" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="是否支持校外指导教师" prop="outtea">
      <el-radio v-model="addData.outtea" :label="0">否</el-radio>
      <el-radio v-model="addData.outtea" :label="1">是</el-radio>
    </el-form-item>
    <el-form-item v-if="addData.outtea==1" label="竞赛支持最多校外指导教师" prop="outtea_num">
      <el-input-number :min="0" :max="30" v-model="addData.outtea_num" style="width: 200px; margin-left: 20px;"></el-input-number>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="备注"
      v-model="addData.remarks">
     </el-input>
    </el-form-item>
  </el-form>

  <span slot="footer" class="dialog-footer">
    <el-button @click="addVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_add_competition">确 定 添 加</el-button>
  </span>
</el-dialog>
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
          <!-- <el-table-column
            prop="uid"
            label="申请编号"
            min-width="120"
            align="center"
          ></el-table-column> -->
          <!-- <el-table-column
            prop="proid"
            label="竞赛编号"
            min-width="120"
            align="center"
          ></el-table-column> -->
          <el-table-column
            prop="name"
            label="竞赛名称"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="l_level"
            label="竞赛级别"
            min-width="120"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="active"
            label="当前状态"
            min-width="120"
            align="center"
          >
          <template slot-scope="scope">
            <el-tag v-if='scope.row.active==0' type="danger">未开始</el-tag>
            <el-tag v-if='scope.row.active==1' type="success">可填报</el-tag>
          </template>
        </el-table-column>
          <el-table-column
          prop="remark"
          label="备注"
          min-width="120"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="website(scope.row)">官网</el-button>

              <el-button size="mini" v-if='scope.row.active==1' @click="goto(scope.row)">填报</el-button>
              <el-button size="mini" @click="detail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
</div>
<script src="../../static/js/js/competition.js?v=1.5"></script>
</body>
</html>