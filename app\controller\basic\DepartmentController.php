<?php

namespace app\controller\basic;

use app\BaseController;
use app\model\Department;
use app\model\Major;
use think\facade\Db;

class DepartmentController extends BaseController
{
    public function index(){
        return view('basic/department');
    }
    
    // 获取部门列表（包含专业信息）
    public function get_departments(){
        $departments = Department::where('is_delete', 0)
            ->order('id', 'asc')
            ->select();
            
        $result = [];
        foreach ($departments as $dept) {
            $majors = Major::where('department_id', $dept->id)
                ->where('is_delete', 0)
                ->select();
                
            $result[] = [
                'id' => $dept->id,
                'name' => $dept->name,
                'created_at' => $dept->created_at,
                'updated_at' => $dept->updated_at,
                'majors' => $majors
            ];
        }
        
        return json(['status' => 'success', 'message' => $result]);
    }
    
    // 获取单个部门信息
    public function get_department(){
        $id = input('post.id');
        $department = Department::where('id', $id)->where('is_delete', 0)->find();
        
        if (!$department) {
            return json(['status' => 'error', 'message' => '部门不存在']);
        }
        
        $majors = Major::where('department_id', $id)->where('is_delete', 0)->select();
        
        return json(['status' => 'success', 'message' => [
            'department' => $department,
            'majors' => $majors
        ]]);
    }
    
    // 新增部门
    public function add_department(){
        $data = input('post.data');
        
        // 检查部门名称是否已存在
        if (Department::where('name', $data['name'])->where('is_delete', 0)->find()) {
            return json(['status' => 'error', 'message' => '部门名称已存在']);
        }
        
        $department = new Department();
        $department->name = $data['name'];
        
        if ($department->save()) {
            return json(['status' => 'success', 'message' => '部门添加成功']);
        } else {
            return json(['status' => 'error', 'message' => '部门添加失败']);
        }
    }
    
    // 修改部门
    public function update_department(){
        $data = input('post.data');
        
        // 检查部门是否存在
        $department = Department::where('id', $data['id'])->where('is_delete', 0)->find();
        if (!$department) {
            return json(['status' => 'error', 'message' => '部门不存在']);
        }
        
        // 检查部门名称是否重复（排除自己）
        if (Department::where('name', $data['name'])->where('id', '<>', $data['id'])->where('is_delete', 0)->find()) {
            return json(['status' => 'error', 'message' => '部门名称已存在']);
        }
        
        $department->name = $data['name'];
        
        if ($department->save()) {
            return json(['status' => 'success', 'message' => '部门修改成功']);
        } else {
            return json(['status' => 'error', 'message' => '部门修改失败']);
        }
    }
    
    // 删除部门
    public function delete_department(){
        $id = input('post.id');
        
        // 检查部门是否存在
        $department = Department::where('id', $id)->where('is_delete', 0)->find();
        if (!$department) {
            return json(['status' => 'error', 'message' => '部门不存在']);
        }
        
        // 检查是否有用户使用该部门
        $userCount = Db::name('user')->where('college', $id)->where('status', 0)->count();
        if ($userCount > 0) {
            return json(['status' => 'error', 'message' => '该部门下还有'.$userCount.'个用户，无法删除']);
        }
        
        // 软删除部门
        if (Department::where('id', $id)->update(['is_delete' => 1])) {
            // 同时软删除该部门下的所有专业
            Major::where('department_id', $id)->update(['is_delete' => 1]);
            return json(['status' => 'success', 'message' => '部门删除成功']);
        } else {
            return json(['status' => 'error', 'message' => '部门删除失败']);
        }
    }
    
    // 新增专业
    public function add_major(){
        $data = input('post.data');
        
        // 检查部门是否存在
        $department = Department::where('id', $data['department_id'])->where('is_delete', 0)->find();
        if (!$department) {
            return json(['status' => 'error', 'message' => '部门不存在']);
        }
        
        // 检查专业名称是否已存在（同一部门下）
        if (Major::where('name', $data['name'])->where('department_id', $data['department_id'])->where('is_delete', 0)->find()) {
            return json(['status' => 'error', 'message' => '该部门下已存在同名专业']);
        }
        
        $major = new Major();
        $major->department_id = $data['department_id'];
        $major->name = $data['name'];
        
        if ($major->save()) {
            return json(['status' => 'success', 'message' => '专业添加成功']);
        } else {
            return json(['status' => 'error', 'message' => '专业添加失败']);
        }
    }
    
    // 修改专业
    public function update_major(){
        $data = input('post.data');
        
        // 检查专业是否存在
        $major = Major::where('id', $data['id'])->where('is_delete', 0)->find();
        if (!$major) {
            return json(['status' => 'error', 'message' => '专业不存在']);
        }
        
        // 检查专业名称是否重复（同一部门下，排除自己）
        if (Major::where('name', $data['name'])->where('department_id', $major->department_id)->where('id', '<>', $data['id'])->where('is_delete', 0)->find()) {
            return json(['status' => 'error', 'message' => '该部门下已存在同名专业']);
        }
        
        $major->name = $data['name'];
        
        if ($major->save()) {
            return json(['status' => 'success', 'message' => '专业修改成功']);
        } else {
            return json(['status' => 'error', 'message' => '专业修改失败']);
        }
    }
    
    // 删除专业
    public function delete_major(){
        $id = input('post.id');
        
        // 检查专业是否存在
        $major = Major::where('id', $id)->where('is_delete', 0)->find();
        if (!$major) {
            return json(['status' => 'error', 'message' => '专业不存在']);
        }
        
        // 检查是否有用户使用该专业
        $userCount = Db::name('user')->where('major', $id)->where('status', 0)->count();
        if ($userCount > 0) {
            return json(['status' => 'error', 'message' => '该专业下还有'.$userCount.'个用户，无法删除']);
        }
        
        // 软删除专业
        if (Major::where('id', $id)->update(['is_delete' => 1])) {
            return json(['status' => 'success', 'message' => '专业删除成功']);
        } else {
            return json(['status' => 'error', 'message' => '专业删除失败']);
        }
    }
    
    /**
     * 导出部门数据
     */
    public function export_departments()
    {
        // 获取部门列表（包含专业信息）
        $departments = Department::where('is_delete', 0)
            ->order('id', 'asc')
            ->select();
            
        $result = [];
        foreach ($departments as $dept) {
            $majors = Major::where('department_id', $dept->id)
                ->where('is_delete', 0)
                ->select();
                
            $result[] = [
                'id' => $dept->id,
                'name' => $dept->name,
                'created_at' => $dept->created_at,
                'updated_at' => $dept->updated_at,
                'majors' => $majors
            ];
        }
        
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '部门名称',
            '专业列表',
            '创建时间',
            '更新时间'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatDepartmentData($result);
        
        // 生成文件名
        $filename = $exportService->generateFilename('部门管理列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
} 