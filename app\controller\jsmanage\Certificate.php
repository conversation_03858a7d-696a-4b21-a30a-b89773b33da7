<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Jsprogress;
use app\model\Jsproject;
use think\facade\Db;

class Certificate extends BaseController
{
    public function addcertificate($uid){
        LogExecution($uid.'上传获奖情况');
        if (!$uid){
            return json(['status'=>'error','message' => '参数异常']);
        }
        $project=Jsproject::where('is_delete',0)->where('uid',$uid)->find();
        if (!$project){
            LogExecution($uid.'项目不存在');

            return json(['status'=>'error','message' => '项目不存在']);
        }
        $new_status=5;
        if ($project['status']!=4&&$project['status']!=5&&$project['status']!=12&&$project['status']!=13&&$project['status']!=14){
            LogExecution($uid.'当前不是可以提交证书状态');

            return json(['status'=>'error','message' => '当前不是可以提交证书状态']);
        }else{
            if ($project['status']==4){
                $new_status=5;
            } elseif ($project['status']==5){
                $new_status=5;
            }elseif ($project['status']==12){
                $new_status=5;
            }elseif ($project['status']==13){
                $new_status=6;
            }elseif ($project['status']==14){
                $new_status=7;
            }
        }
        $data=input('data');
        //去给data做验证
        $award=[
          'award'=>$data['award'],
          'certificate'=>$data['certificate'],
            'award_remark'=>$data['remark'],
            'status'=>$new_status
        ];
        //添加项目进度
        $progress=[
            'uid'=>$uid,
            'action'=>'证书上传',
            'remark'=>'学生提交获奖信息'
        ];
        try {
            // 开启事务
            Db::startTrans();

            // 尝试更新 Jsproject 表
            $updateResult = Jsproject::where('is_delete', 0)->where('uid', $uid)->update($award);

            // 尝试插入 Jsprogress 表
            $insertResult = Jsprogress::insert($progress);

            // 检查前面的操作是否都成功
            if ($updateResult && $insertResult) {
                // 如果所有操作成功，提交事务
                Db::commit();

                // 记录成功日志
                LogExecution($uid . '获奖信息上传成功');

                // 返回成功响应
                return json(['status' => 'success', 'message' => '获奖信息上传成功']);
            } else {
                // 如果前面的操作中有任何一个失败，回滚事务
                Db::rollback();

                // 记录失败日志
                LogExecution($uid . '获奖信息上传失败，数据表操作异常');

                // 返回错误响应
                return json(['status' => 'error', 'message' => '数据异常']);
            }
        } catch (\Exception $e) {
            // 捕获异常，回滚事务（如果在 try 块中已经开始事务但尚未提交，则此回滚是必要的）
            Db::rollback();

            // 记录异常日志（可以包含异常信息）
            LogExecution($uid . '获奖信息上传失败，捕获到异常：' . $e->getMessage());

            // 返回错误响应
            return json(['status' => 'error', 'message' => '系统异常，请稍后再试']);
        }
    }
}