<?php

namespace app\controller\newsmanage;

use app\BaseController;
use app\model\Newsclass;
use app\model\UserGroup;
use app\model\Usermode;
use Exception;
use think\facade\Db;

class Classes extends BaseController
{
    public function index(){
        if (session('user.usermode')!=11){
            LogExecution(session('user.username') . '不是超级管理员');

            // 返回错误响应
            return json(['status' => 'error', 'message' => '您不是超级管理员']);
        }
    return view('newsmanage/classes');
}
    public function classlist(){
        if (session('user.usermode')!=11){
            LogExecution(session('user.username') . '不是超级管理员');

            // 返回错误响应
            return json(['status' => 'error', 'message' => '您不是超级管理员']);
        }
        //只有超级管理员有权限访问
        $classes=Newsclass::
            alias('c')
            ->field('c.name,c.rank,c.id')
//            ->join('newsdetail n', 'n.class = c.id', 'LEFT')
//
//            ->where('c.is_delete',0)
//            ->where('n.is_delete',0)
//            ->distinct(true)
            ->order('c.rank','asc')
            ->select();
        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($classes),
            'data'=>$classes
        ]]);
    }
    public function edit(){
        $data=input('post.data');
        //检验data
        if (!$data['id']){
            //添加
            //添加板块和板块管理组
            // 开启事务
            Db::startTrans();
            $usergroup=Usermode::insertgetid([
                'group_name'=>$data['name'].'新闻管理员'
            ]);
            $data['usermode']=$usergroup;
            $newsclass=Newsclass::insert($data);

            if (
                $usergroup
                &&
                $newsclass
            ){
                // 如果所有操作成功，提交事务
                Db::commit();
                // 记录成功日志并返回成功信息
                LogExecution($data['name'] . '新建新闻板块成功');
                return ['status' => 'success', 'message' => '新建新闻板块成功'];
            } else {
                // 如果校外指导教师或学生的插入操作失败，回滚事务
                Db::rollback();
                // 抛出异常（这将在 catch 块中被捕获）
                LogExecution($data['name'] . '新建新闻板块失败，数据库插入异常');
                return ['status' => 'error', 'message' => '新建新闻板块失败'];
            }
        }else{
            //修改
            $classname=$data['name'];
            $classrank=$data['rank'];

            $usergroup=Newsclass::where('is_delete',0)->where('id',$data['id'])->find();
            $usermode=$usergroup['usermode'];
            // 开启事务
            Db::startTrans();
            if (
                Newsclass::where('is_delete',0)->where('id',$data['id'])->update(['name'=>$classname,'rank'=>$classrank])
                ||
                Newsclass::where('is_delete',0)->where('id',$usermode)->update(['name'=>$classname.'新闻管理员'])

            ){
                Db::commit();
                // 记录成功日志并返回成功信息
                LogExecution($data['id'] . '修改新闻板块成功');
                return ['status' => 'success', 'message' => '修改新闻板块成功'];
            }else{
                Db::rollback();
                LogExecution($data['id'] . '修改新闻板块失败，数据库插入异常');
                return ['status' => 'error', 'message' => '无修改'];
            }
        }

    }
}