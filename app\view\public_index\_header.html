<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>沈阳师范大学大创中心</title>
    <link rel="stylesheet" href="../../static/css/<EMAIL>">
    <script src="../../static/js/vue2.js"></script>
    <script src="../../static/js/<EMAIL>"></script>
    <script src="../../static/js/axios.min.js"></script>
    <link rel="stylesheet" href="../../static/css/index/header.css">

</head>
<body>
  <div id="header">
        <!-- 头部导航 -->
        <header class="main-header"> 
          <div class="logo">
            <img @click="handleSelect(1)" style="width: 100%;height: 100%;" src="../../static/image/index/logo.png" alt="">
          </div> 
          <el-menu 
            class="main-nav" 
            mode="horizontal" 
            background-color="#3498db" 
            text-color="#ecf0f1" 
            active-text-color="#fff" 
            :default-active="activeIndex" 
            @select="handleSelect"> 
            <el-menu-item index="1">首页</el-menu-item> 
            <el-menu-item index="2">大创平台</el-menu-item> 
            <el-menu-item index="3">竞赛平台</el-menu-item> 
            <el-menu-item index="4">英才库</el-menu-item> 
            <el-submenu index="5">
              <template slot="title">
                {if session('user.username')}
                {:session('user.name')}
                {else}
                登录/注册
                {/if}
              </template>
              <el-menu-item index="5-1">个人空间</el-menu-item>
              <el-menu-item index="5-2">退出登录</el-menu-item>
            </el-submenu> 
          </el-menu> 
        </header> 
        </div>
    <script src="../../static/js/index/footer.js"></script>
