<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>延期结题申请</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #409EFF;
        }
        .header h1 {
            color: #303133;
            margin: 0;
            font-size: 24px;
        }
        .form-section {
            margin-bottom: 30px;
        }
        .form-section h3 {
            color: #606266;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid #409EFF;
        }
        .submit-section {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #EBEEF5;
        }
        .el-form-item__label {
            font-weight: bold;
            color: #303133;
        }
        .el-textarea__inner {
            min-height: 120px;
        }
        .info-box {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #409EFF;
        }
        .info-box p {
            margin: 5px 0;
            color: #606266;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>延期结题申请</h1>
            </div>

            <!-- 项目信息 -->
            <div class="info-box">
                <h4>项目信息</h4>
                <p><strong>项目名称：</strong>{{project.name}}</p>
                <p><strong>当前状态：</strong>{{getStatusText(project.status)}}</p>
            </div>

            <el-form :model="form" :rules="rules" ref="form" label-width="120px">
                <div class="form-section">
                    <h3>延期申请信息</h3>
                    
                    <el-form-item label="延期时间" prop="extension_time">
                        <el-select v-model="form.extension_time" placeholder="请选择延期时间" style="width: 100%;">
                            <el-option label="6个月" value="6"></el-option>
                            <el-option label="1年" value="12"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="延期理由" prop="reason">
                        <el-input 
                            type="textarea" 
                            v-model="form.reason" 
                            placeholder="请详细说明申请延期的理由，包括项目进展情况、遇到的问题、需要延期的具体原因等"
                            :rows="6">
                        </el-input>
                    </el-form-item>
                </div>

                <div class="submit-section">
                    <el-button type="primary" @click="submitForm" :loading="loading" size="large">
                        {{loading ? '提交中...' : '提交申请'}}
                    </el-button>
                    <el-button @click="goBack" size="large">返回</el-button>
                </div>
            </el-form>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    project: {
                        name: '{$project.name}',
                        leader: '{$project.leader}',
                        level: '{$project.level}',
                        status: {$project.status},
                        uid: '{$project.uid}'
                    },
                    extension: <?= $extension ? json_encode($extension) : 'null' ?>,
                    form: {
                        extension_time: '',
                        reason: ''
                    },
                    rules: {
                        extension_time: [
                            { required: true, message: '请选择延期时间', trigger: 'change' }
                        ],
                        reason: [
                            { required: true, message: '请填写延期理由', trigger: 'blur' },
                            { min: 10, message: '延期理由至少10个字符', trigger: 'blur' }
                        ]
                    },
                    loading: false
                }
            },
            mounted() {
                // 获取uid参数
                const url = window.location.href;
                const urlObj = new URL(url);
                const params = new URLSearchParams(urlObj.search);
                this.project.uid = params.get('uid');
                
                // 如果有已存在的延期申请，填充表单
                if (this.extension) {
                    this.form.extension_time = this.extension.extension_time;
                    this.form.reason = this.extension.reason;
                }
            },
            methods: {
                getStatusText(status) {
                    const statusMap = {
                        8: '待提交结题报告',
                        22: '等待教师延期审核',
                        23: '等待学院延期审核',
                        24: '等待学校延期审核',
                        29: '教师延期审核驳回（可重新申请延期或提交结题报告）',
                        30: '学院延期审核驳回（可重新申请延期或提交结题报告）',
                        31: '学校延期审核驳回（可重新申请延期或提交结题报告）',
                        32: '延期审核通过，可提交结题报告'
                    };
                    return statusMap[status] || '未知状态';
                },
                submitForm() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            this.loading = true;
                            
                            axios.post('dc-applyextension?uid=' + this.project.uid, {
                                data: this.form
                            })
                            .then(response => {
                                if (response.data.status === 'success') {
                                    this.$message.success(response.data.message);
                                    setTimeout(() => {
                                        this.goBack();
                                    }, 1500);
                                } else {
                                    this.$message.error(response.data.message);
                                }
                            })
                            .catch(error => {
                                console.error('提交失败:', error);
                                this.$message.error('提交失败，请重试');
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                        } else {
                            this.$message.warning('请完善表单信息');
                        }
                    });
                },
                goBack() {
                    window.history.back();
                }
            }
        });
    </script>
</body>
</html> 