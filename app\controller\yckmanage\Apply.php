<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\User;
use app\model\Yckdomain;
use app\model\Yckjob;
use app\model\Yckprogress;
use app\model\Yckproject;
use app\model\Ycktag;
use app\model\Yckuser;
use think\facade\Db;

class Apply extends BaseController
{
    public function apply(){
        if (session('user.usermode')!=1&&session('user.usermode')!=2){
            return json(['status'=>'error','message'=>'只有学生和教师才能申请英才库']);
        }

        $data=input('post.data');

        if (!$data){
            $jobs=Yckjob::where('is_delete',0)->where('type',session('user.usermode'))->select();
            $mine=User::alias('u')
                ->field('u.username,u.name,d.name as college,m.name as major,u.phone,u.email')
                ->join('department d', 'd.id = u.college', 'LEFT')
                ->join('major m', 'm.id = u.major', 'LEFT')
                ->where('u.username',session('user.username'))
                ->where('u.status',0)
//                ->where('d.is_delete',0)
//                ->where('m.is_delete',0)
                ->find();
//            return json($mine);

            return view('yckmanage/apply',['jobs'=>$jobs,'mine'=>$mine]);
        }else{
            $data=input('post.data');
            //做数据检验
            //数据整理
            $yck=[
                'username'=>session('user.username'),
                'gender'=>$data['gender'],
                'job'=>$data['job'],
                'intro'=>$data['intro'],
                'avatar'=>$data['avatar'],
                'wx'=>$data['wx'],
                'qq'=>$data['qq'],
                'dd'=>$data['dd'],
                'qywx'=>$data['qywx'],
                'status'=>1


            ];
//            $user=[
//                'phone'=>$data['phone'],
//                'email'=>$data['email']
//            ];

            $domains=[];
            foreach ($data['domain'] as $domain){
                $domains[]=[
                  'username'=>session('user.username'),
                  'name'=>$domain
                ];
            }

            $tags=[];
            foreach ($data['tag'] as $tag){
                $tags[]=[
                    'username'=>session('user.username'),
                    'name'=>$tag
                ];
            }
            $projects=[];
            if (!$data['dc']&&!$data['js']){
                LogExecution(session('user.username').'申请失败，请至少上传一项大创或竞赛');
                return ['status'=>'error','message'=>'申请失败，请至少上传一项大创或竞赛'];
            }
            if ($data['dc']){
                foreach ($data['dc'] as $dc){
                    $projects[]=[
                        'username'=>session('user.username'),
                        'uid'=>$dc,
                        'type'=>'dc'
                    ];
                }

            }
            if ($data['js']){
                foreach ($data['js'] as $js){
                    $projects[]=[
                        'username'=>session('user.username'),
                        'uid'=>$js,
                        'type'=>'js'
                    ];
                }

            }
            //添加项目进度
            $progress=[
                'username'=>session('user.username'),
                'action'=>'英才申请',
                'remark'=>'提交英才申请'
            ];
            //配置
//            return json([
//                0=>$yck,
//                2=>$domains,
//                3=>$tags,
//                4=>$projects
//            ]);
            //开始插入
            // 开启事务
            Db::startTrans();
            if (
                Yckuser::insert($yck)
                &&
                Yckdomain::insertall($domains)
                &&
                Ycktag::insertall($tags)
                &&
                Yckproject::insertall($projects)
                &&
                Yckprogress::insert($progress)
            ){
                // 如果所有操作成功，提交事务
                Db::commit();
                LogExecution(session('user.username').'申请成功');
                return ['status'=>'success','message'=>'提交申请成功'];
            }else{
                // 如果前面的操作中有任何一个失败，回滚事务
                Db::rollback();
                LogExecution(session('user.username').'申请失败，请检查环境及填写内容');
                return ['status'=>'error','message'=>'申请失败，请检查环境及填写内容'];
            }
        }
    }
}