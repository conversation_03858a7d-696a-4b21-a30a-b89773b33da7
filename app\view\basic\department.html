{include file="public/_header"}
<link rel="stylesheet" href="../../static/css/basic/department.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">管理</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="/bs-department">部门管理</a>
</p>

  <!-- 部门对话框 -->
  <el-dialog
    :title="departmentType === 'add' ? '新增部门' : '修改部门'"
    :visible.sync="departmentVisible"
    width="500px"
  >
    <el-form 
      :model="departmentForm" 
      :rules="departmentRules" 
      ref="departmentForm" 
      label-width="100px"
    >
      <el-form-item label="部门名称" prop="name">
        <el-input v-model="departmentForm.name" placeholder="请输入部门名称"></el-input>
      </el-form-item>
    </el-form>
 
    <span slot="footer" class="dialog-footer">
      <el-button @click="departmentVisible = false">取消</el-button>
      <el-button type="primary" @click="handleDepartmentConfirm">{{ departmentType === 'add' ? '立即添加' : '确认修改' }}</el-button>
    </span>
  </el-dialog>

  <!-- 专业对话框 -->
  <el-dialog
    :title="majorType === 'add' ? '新增专业' : '修改专业'"
    :visible.sync="majorVisible"
    width="500px"
  >
    <el-form 
      :model="majorForm" 
      :rules="majorRules" 
      ref="majorForm" 
      label-width="100px"
    >
      <el-form-item label="所属部门">
        <el-input v-model="currentDepartment.name" disabled></el-input>
      </el-form-item>
      <el-form-item label="专业名称" prop="name">
        <el-input v-model="majorForm.name" placeholder="请输入专业名称"></el-input>
      </el-form-item>
    </el-form>
 
    <span slot="footer" class="dialog-footer">
      <el-button @click="majorVisible = false">取消</el-button>
      <el-button type="primary" @click="handleMajorConfirm">{{ majorType === 'add' ? '立即添加' : '确认修改' }}</el-button>
    </span>
  </el-dialog>

  <!-- 导出按钮 -->
  <el-row style="margin-bottom: 20px;">
    <el-col :span="24">
      <el-button plain type="success" @click="exportDepartments">导出当前{{ departments.length }}个部门</el-button>
    </el-col>
  </el-row>

  <el-row :gutter="20">
    <!-- 左侧部门列表 -->
    <el-col :span="12">
      <el-card>
        <div slot="header" class="clearfix">
          <span>部门管理</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="addDepartment">新增部门</el-button>
        </div>
        
        <el-table
          :data="departments"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          @row-click="selectDepartment"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            prop="name"
            label="部门名称"
            min-width="150"
          >
          </el-table-column>
          <el-table-column
            prop="majors"
            label="专业数量"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.majors ? scope.row.majors.length : 0 }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click.stop="editDepartment(scope.row)">修改</el-button>
              <el-popconfirm
                title="确定删除该部门吗？删除后该部门下的所有专业也会被删除"
                @confirm="deleteDepartment(scope.row)"
              >
                <el-button slot="reference" size="mini" type="danger" @click.stop>删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-col>

    <!-- 右侧专业列表 -->
    <el-col :span="12">
      <el-card>
        <div slot="header" class="clearfix">
          <span>专业管理 - {{ currentDepartment.name || '请选择部门' }}</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="addMajor"
            :disabled="!currentDepartment.id"
          >
            新增专业
          </el-button>
        </div>
        
        <el-table
          :data="currentMajors"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          v-loading="loading"
        >
          <el-table-column
            prop="name"
            label="专业名称"
            min-width="150"
          >
          </el-table-column>
          <el-table-column
            prop="created_at"
            label="创建时间"
            width="180"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="editMajor(scope.row)">修改</el-button>
              <el-popconfirm
                title="确定删除该专业吗？"
                @confirm="deleteMajor(scope.row)"
              >
                <el-button slot="reference" size="mini" type="danger">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-col>
  </el-row>
</div>

<script src="../../static/js/basic/department.js?v=1.0"></script>
</body>
</html> 