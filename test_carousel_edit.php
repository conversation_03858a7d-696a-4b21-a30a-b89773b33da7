<?php
// 测试轮播编辑功能
require_once 'vendor/autoload.php';

use think\facade\Db;
use app\model\Carousel;

echo "=== 轮播编辑功能测试 ===\n\n";

// 1. 检查轮播数据是否存在
echo "1. 检查轮播数据:\n";
$carousels = Carousel::where('is_delete', 0)->select();
echo "总轮播数量: " . count($carousels) . "\n";

if (count($carousels) > 0) {
    echo "第一个轮播信息:\n";
    $firstCarousel = $carousels[0];
    echo "ID: " . $firstCarousel->id . "\n";
    echo "标题: " . $firstCarousel->title . "\n";
    echo "图片路径: " . $firstCarousel->image_path . "\n";
    echo "新闻ID: " . $firstCarousel->news_id . "\n";
    echo "状态: " . $firstCarousel->status . "\n";
    echo "排序: " . $firstCarousel->sort_order . "\n";
    echo "创建时间: " . $firstCarousel->created_at . "\n";
    echo "更新时间: " . $firstCarousel->updated_at . "\n\n";
    
    // 2. 测试根据ID查询轮播
    echo "2. 测试根据ID查询轮播:\n";
    $testId = $firstCarousel->id;
    $carousel = Carousel::where('id', $testId)->where('is_delete', 0)->find();
    
    if ($carousel) {
        echo "✓ 轮播存在，ID: " . $carousel->id . "\n";
        echo "标题: " . $carousel->title . "\n\n";
    } else {
        echo "✗ 轮播不存在，ID: " . $testId . "\n\n";
    }
    
    // 3. 测试模拟编辑请求
    echo "3. 模拟编辑请求数据:\n";
    $editData = [
        'id' => $testId,
        'data' => [
            'title' => '测试编辑轮播',
            'image_path' => $firstCarousel->image_path,
            'news_id' => $firstCarousel->news_id,
            'status' => 1
        ]
    ];
    
    echo "编辑数据: " . json_encode($editData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 4. 检查数据库连接
    echo "4. 数据库连接测试:\n";
    try {
        $testQuery = Carousel::where('id', 1)->find();
        echo "✓ 数据库连接正常\n\n";
    } catch (Exception $e) {
        echo "✗ 数据库连接失败: " . $e->getMessage() . "\n\n";
    }
    
} else {
    echo "✗ 没有找到任何轮播数据\n\n";
}

echo "=== 测试完成 ===\n";
?> 