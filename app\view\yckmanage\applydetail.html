{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/yck/applydetail.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">英才库</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="yck-applylist">申请列表</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="yck-applydetail?username={$data.user.username}">{$data.user.name}</a>
</p>

  <!-- 审核框 -->
  <el-dialog
  :title="checktitle"
  :visible.sync="checkVisible"
  width="30%"
  >
  <el-form ref="checkform" :rules="check_rules" label-position="left" :model="checkform" label-width="6rem">
    <el-form-item label="审核结果" prop="check">
      <el-radio  :value="checkform.check" label="0">通过</el-radio>
      <el-radio  :value="checkform.check" label="1">驳回</el-radio>
    </el-form-item>
    <el-form-item label="评星" prop="mark">
      <div class="block" >
        <span class="demonstration">三星以上即为英才，三星以下即为驳回</span>
        <el-rate v-model="checkform.mark" allow-half></el-rate>
      </div>
    </el-form-item>
    <el-form-item label="审核意见" prop="remark">
      <el-input
      type="textarea"
      :autosize="{ minRows: 2, maxRows: 20}"
      placeholder="请输入审核意见"
      v-model="checkform.remark">
     </el-input>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="checkVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_check">确 定</el-button>
  </span>
</el-dialog>


 <!-- 悬浮按钮 -->
 <el-button
 class="floating-button"
 type="primary"
 icon="el-icon-more"
 circle
 @click="toggleMenu"
></el-button>
<!-- 悬浮菜单 -->
<div v-if="isMenuVisible" class="floating-menu">
 <el-menu>
   <el-menu-item-group>
   <template slot="title">操作</template>
   <el-menu-item index="1" @click="handleMenuClick(1)">修改</el-menu-item>
 </el-menu-item-group>
 <el-menu-item-group>
   <template slot="title">学校操作</template>
   <el-menu-item index="2" @click="handleMenuClick(2)">审核</el-menu-item>
 </el-menu-item-group>
 <el-menu-item-group>
   <template slot="title">超级管理员操作</template>
   <el-menu-item index="3" @click="handleMenuClick(3)">切换状态</el-menu-item>
 </el-menu-item-group>
 </el-menu>
</div>

<el-row>
  <el-col :xs="24" :sm="20">
    <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="英才信息" :column="6" border>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          卡号
        </template>
        {$data.user.username}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          姓名
        </template>
        {$data.user.name}
      </el-descriptions-item>
      <el-descriptions-item span="3">
        <template slot="label" >
          <i class="el-icon-user"></i>
          部门
        </template>
        {$data.user.department}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          专业
        </template>
        {$data.user.major}
      </el-descriptions-item>

      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          学历/职务
        </template>
        {$data.user.job}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          年级
        </template>
        {$data.user.grade}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          状态
        </template>
        {$data.user.status}
      </el-descriptions-item>
      <el-descriptions-item  span="3">
        <template slot="label">
          <i class="el-icon-user"></i>
          评分
        </template>
        <el-rate
          value="{$data.user.mark}"
          disabled
          show-score
          text-color="#ff9900"
          score-template="{value}">
        </el-rate>
        
      </el-descriptions-item>
      <el-descriptions-item  span="6">
        <template slot="label">
          <i class="el-icon-user"></i>
          个人简介
        </template>
        {$data.user.intro}
      </el-descriptions-item>
      {if $data.user.remark}
      <el-descriptions-item  span="6">
        <template slot="label">
          <i class="el-icon-user"></i>
          备注
        </template>
        {$data.user.remark}
      </el-descriptions-item>
      {/if}
      <el-descriptions-item  span="6"> 
        <template slot="label" >
          <i class="el-icon-user"></i>
          图片
        </template>
        <el-image
        src="/static/files/upload/{$data.user.avatar}"
      ></el-image>
      </el-descriptions-item>
    </el-descriptions>
    {if $data.dcs}
    <el-table
    :data="{$data.dcs}"
    style="width: 100%">
    <el-table-column label="大创项目" >
      <el-table-column
      prop="name"
      label="项目名称"
      min-width="100">
      </el-table-column>
      <el-table-column
        prop="l_level"
        label="项目级别"
        min-width="100">
      </el-table-column>
      <el-table-column
      prop="time"
      label="立项时间"
      min-width="120">
      </el-table-column>
      <el-table-column
      prop="rank"
      label="排位"
      min-width="120">
      </el-table-column>
    
      </el-table-column>
    </el-table-column>
    </el-table>
      {/if}

      {if $data.jss}
      <el-table
      :data="{$data.jss}"
      style="width: 100%">
      <el-table-column label="竞赛项目" >
        <el-table-column
        prop="c_name"
        label="竞赛名称"
        min-width="100">
        </el-table-column>
        <el-table-column
          prop="l_level"
          label="竞赛级别"
          min-width="100">
        </el-table-column>
        <el-table-column
        prop="name"
        label="项目名称"
        min-width="100">
        </el-table-column>

        <el-table-column
        prop="time"
        label="立项时间"
        min-width="120">
        </el-table-column>
        <el-table-column
        prop="rank"
        label="排位"
        min-width="120">
        </el-table-column>
        <el-table-column
        prop="award"
        label="获奖信息"
        min-width="120">
        </el-table-column>
        </el-table-column>
      </el-table-column>
      </el-table>
        {/if}

        <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="联系方式" :column="2" border>
          <el-descriptions-item  >
            <template slot="label">
              <i class="el-icon-user"></i>
              电话
            </template>
            {$data.user.phone}
          </el-descriptions-item>
          <el-descriptions-item  >
            <template slot="label">
              <i class="el-icon-user"></i>
              电子邮箱
            </template>
            {$data.user.email}
          </el-descriptions-item>
          {if $data.user.wx}
          <el-descriptions-item > 
            <template slot="label" >
              <i class="el-icon-user"></i>
              微信
            </template>
            <el-image
            src="/static/files/upload/{$data.user.wx}"
          ></el-image>
          </el-descriptions-item>
          {/if}
          {if $data.user.qq}
          <el-descriptions-item > 
            <template slot="label" >
              <i class="el-icon-user"></i>
              QQ
            </template>
            <el-image
            src="/static/files/upload/{$data.user.qq}"
          ></el-image>
          </el-descriptions-item>
          {/if}
          {if $data.user.dd}
          <el-descriptions-item > 
            <template slot="label" >
              <i class="el-icon-user"></i>
              钉钉
            </template>
            <el-image
            src="/static/files/upload/{$data.user.dd}"
          ></el-image>
          </el-descriptions-item>
          {/if}
          {if $data.user.qywx}
          <el-descriptions-item > 
            <template slot="label" >
              <i class="el-icon-user"></i>
              企业微信
            </template>
            <el-image
            src="/static/files/upload/{$data.user.qywx}"
          ></el-image>
          </el-descriptions-item>
          {/if}

        </el-descriptions>


        {if  isset($data.checks['status1']['school'][0]['created_at'])}
        <el-descriptions :label-style="label_style" :content-style="content_style" class="margin-top" title="审核意见" direction="vertical"   border>
      {if count($data.checks['status1']['school']) neq 0}
        <el-descriptions-item>
          <template slot="label" >
            <i class="el-icon-user"></i>
            学校申请意见
          </template>
          {foreach $data.checks['status1']['school'] as $check}
          <p>
            {if !$check['check']}
            <span style="color: #1fd11f97;">{$check['mark']}分通过</span>
            {else}
            <span style="color: #ff4400;">{$check['mark']}分驳回</span>
            {/if}
            <span style="color: #00000097;">{$check['created_at']}</span>
            <span>{$check['remark']}</span>
          </p>
          {/foreach}
        </el-descriptions-item>
      {/if}
        </el-descriptions>
        {/if}


  </el-col>
  <el-col :xs="24" :sm="4">
    <el-timeline :reverse="false">
      {foreach $data.progesses as $progess}
      <el-timeline-item
      timestamp="{$progess.created_at}">
      {$progess.action}
      <br>
      {$progess.remark}
    </el-timeline-item>
    {/foreach}
    </el-timeline>
  </el-col>

</el-row>




</div>
<script src="../../static/js/yck/applydetail.js"></script>
</body>
</html>