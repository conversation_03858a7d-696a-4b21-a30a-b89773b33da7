<?php

namespace app\model;

use think\Model;

class Dcmidchange extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'dcmidchange';
    // 新增快照字段
    protected $schema = [
        'id' => 'int',
        'uid' => 'varchar',
        'change_items' => 'text',
        'change_reason' => 'text',
        'new_members' => 'text',
        'new_teachers' => 'text',
        'old_members' => 'text',
        'old_teachers' => 'text',
        // ... 其他字段 ...
    ];
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
} 