<?php

namespace app\controller\jsmanage;

use app\BaseController;
use app\model\Dccheck;
use app\model\Dcprogress;
use app\model\File;
use app\model\Jsaward;
use app\model\Jscheck;
use app\model\Jsprogress;
use app\model\Jsproject;
use app\model\Member;
use app\model\Teacher;

class Detail extends BaseController
{
    public function index($uid){
        LogExecution($uid.'进入竞赛项目详情');

        // 检查用户是否有权限查看该项目
        if (!$this->checkPermission('js_view') || !$this->canViewProject($uid, 'js')) {
            LogExecution('用户尝试查看无权限的项目：' . $uid);
            return json(['status' => 'error', 'message' => '权限不足']);
        }

        $project = Jsproject::alias('p')
            ->field('
        p.uid,p.name,p.time,p.class,p.introduction,p.status as p_status,p.certificate,p.award_remark,
        c.name as c_name,
        l.name as c_level,
        s.name as status,
        CONCAT(a.level, a.type) AS award

        

    ')
            ->join('jscompetition c', 'c.cuid = p.cuid', 'LEFT')
            ->join('jsaward a', 'a.id = p.award', 'LEFT')
            ->join('jsstatus s','s.id=p.status','LEFT')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->where('p.uid',$uid)
            ->where('p.is_delete', 0)
            ->where('c.is_delete', 0)
            ->group('p.uid')
            ->find();
        if (!$project){
            LogExecution($uid.'项目不存在');
            return json(['status' => 'error', 'message' => '项目不存在']);

        }
        $member=Member::alias('m')
            ->join('user u', 'u.username=m.username','LEFT')
            ->where('m.is_delete',0)
            ->where('u.status',0)
            ->where('m.uid',$uid)
            ->field('u.name,m.username,m.rank,d.name as college,mj.name as major,u.phone,u.email')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major mj','mj.id=u.major','LEFT')
            ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $teacher=Teacher::alias('t')
            ->join('user u', 'u.username=t.username','LEFT')
            ->where('t.is_delete',0)
            ->where('u.status',0)
            ->where('t.uid',$uid)
            ->where('t.type',0)
            ->field('t.username,t.rank,u.name,d.name as college,m.name as major,u.phone,u.email,u.job')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $outstudent=Member::alias('m')
            ->where('m.is_delete',0)
            ->where('m.uid',$uid)
            ->where('m.type',1)
            ->field('m.name,m.unit,m.phone,m.email')
            ->order('m.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $outteacher=Teacher::alias('t')
            ->where('t.is_delete',0)
            ->where('t.uid',$uid)
            ->where('t.type',1)
            ->field('t.name,t.unit,t.job,t.phone,t.email')
            ->order('t.rank', 'asc') // 根据m表的rank字段升序排序
            ->select();
        $progesses=Jsprogress::where('is_delete',0)->where('uid',$uid)->order('created_at', 'asc')->select();
        $checks=[
          //立项审核
            //立项
            'status1'=>[
                'teacher'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',1)->select(),
                'college'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',2)->select(),
                'school'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',1)->where('type',3)->select(),
            ],
            //证书审核
            'status2'=>[
                'teacher'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',6)->where('type',1)->select(),
                'college'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',6)->where('type',2)->select(),
                'school'=>Jscheck::order('created_at', 'asc')->where('is_delete',0)->where('uid',$uid)->where('status',6)->where('type',3)->select(),
            ],
        ];
        $files=File::where('is_delete',0)->where('uid',$uid)->select();
//        return $files;
        //获奖级别
        $award_level=Jsaward::order('id','asc')->select();
//        return json($award_level);
        return view('jsmanage/projectdetail',[
            'project'=>$project,
            'member'=>$member,
            'teacher'=>$teacher,
            'outstudent'=>$outstudent,
            'outteacher'=>$outteacher,
            'progesses'=>$progesses,
            'checks'=>$checks,
            'award_level'=>$award_level,
            'files'=>$files
        ]);
    }
}