{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/yck/lists.css">

<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">英才库</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="yck-lists">英才列表</a>
</p>

<el-dialog
  title="排序"
  :visible.sync="indexVisible"
  width="30%">
  <h3>注意：序号越小越靠前，前10将在官网首页滚动展示</h3>
  <el-form :model="indexform" ref="indexform" label-width="100px" class="demo-ruleForm">
    <el-form-item
      label="排序"
      prop="index"
      :rules="[
        { required: true, message: '排序不能为空'},
        { type: 'number', message: '排序必须为数字值'}
      ]"
    >
    <el-input-number v-model="indexform.index" :min="1"  ></el-input-number>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="indexVisible = false">取 消</el-button>
    <el-button type="primary" @click="submit_index()">确 定</el-button>
  </span>
</el-dialog>

<el-row :gutter="10">
  <el-col :span="5">

    <el-button plain type="primary" @click="exportLists">导出当前{{ totalData.length }}个{{search.type}}</el-button>
    </el-col>
  <el-col :span="7">

  <el-radio-group v-model="search.type">
    <el-radio-button label="英才学生"></el-radio-button>
    <el-radio-button label="精英教师"></el-radio-button>
  </el-radio-group>
</el-col>

  <el-col :span="3">
    <el-select v-model="search.department" placeholder="请选择部门" style="width: 100%">
      <el-option value="all" label="全部部门"></el-option>

      {foreach $search.department as $department}
      <el-option value="{$department.id}" label="{$department.name}"></el-option>
      {/foreach}
    </el-select>
  </el-col>
  <el-col :span="3">
    <el-select
    style="width: 100%;"
    v-model="search.user"
    filterable
    remote
    clearable
    reserve-keyword
    placeholder="全部成员"
    :remote-method="remoteusers"
    :loading="loading">
    <el-option
      v-for="item in users"
      :key="item.value"
      :label="item.label"
      :value="item.value">
      <span style="float: left">{{ item.label }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
    </el-option>
  </el-select>
  </el-col>
  <el-col :span="3">
    <el-button plain type="primary" @click="select_lists">搜索</el-button>
  </el-col>
</el-row>
    <div>
        <el-table
        id="table"
          :data="paginatedData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          height="600"
          border
          default-expand-all
        >
        <el-table-column
        prop="index"
        label="序号"
        min-width="60"
        align="center"
        
>
<template v-slot="scope">
    <!-- 动态计算序号 -->
    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
  </template>
</el-table-column>
        <el-table-column
        prop="index"
        label="排序"
        min-width="120"
        align="center"
        ></el-table-column>
          <el-table-column
            prop="name"
            label="姓名"
            min-width="180"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="department"
          label="学院"
          min-width="180"
          align="center"
        ></el-table-column>
          <el-table-column
            prop="major"
            label="专业"
            min-width="120"
            align="center"
          ></el-table-column>
          <el-table-column
          prop="tags"
          label="标签"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="mark"
          label="评分"
          min-width="120"
          align="center"
        >
        <template slot-scope="scope">
          <div class="block">
          <el-rate v-model="scope.row.mark" disabled></el-rate>
        </div>
        </template>
      </el-table-column>
          <el-table-column
            prop="management"
            label="操作"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button size="mini" @click="goto(scope.row)">简历</el-button>
              <el-button size="mini" @click="index(scope.row)">排序</el-button>

            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes,jumper"
          :total="totalData.length"
          style="text-align: right; margin-top: 20px;"
        >
        </el-pagination>
      </div>
</div>
<script src="../../static/js/yck/lists.js?v=1.1"></script>
</body>
</html>