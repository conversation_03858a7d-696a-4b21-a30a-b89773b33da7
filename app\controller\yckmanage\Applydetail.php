<?php

namespace app\controller\yckmanage;

use app\BaseController;
use app\model\Jscheck;
use app\model\Jsprogress;
use app\model\User;
use app\model\Yckcheck;
use app\model\Yckdomain;
use app\model\Yckprogress;
use app\model\Yckproject;
use app\model\Ycktag;

class Applydetail extends BaseController
{
    public function index($username){
        $user=User::alias('u')
            ->field('
                u.username,u.name,u.usermode,u.grade,
                y.intro,j.name as job,y.status,y.avatar,y.remark,y.status,y.avatar,y.mark,
                d.name as department,
                m.name as major,
                u.phone,u.email,y.qq,y.wx,y.dd,y.qywx,
                s.name as status
                ')
            ->join('yckuser y','y.username=u.username','LEFT')
            ->join('yckjob j','y.job=j.id','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->join('yckstatus s','s.id=y.status','LEFT')


            ->where('y.is_delete',0)
            ->where('u.status',0)
            ->where('u.username',$username)
            ->find();
        $domains=Yckdomain::where('is_delete',0)->where('username',$username)->column('name');
        $tags=Ycktag::where('is_delete',0)->where('username',$username)->column('name');
        $where=[];
        $field='p.uid,p.name,l.name as l_level,p.time';
        if ($user['usermode']==1){
            $where[]=['m.username','=',$username];
            $field=$field.',m.rank';
        }elseif ($user['usermode']==2){
            $where[]=['t.username','=',$username];
            $field=$field.',t.rank';
        }
        $dcs = Yckproject::alias('y')
            ->field($field)
            ->join('dcproject p', 'p.uid = y.uid', 'LEFT')
            ->join('member m', 'm.uid = y.uid', 'LEFT')
            ->join('teacher t', 't.uid = y.uid', 'LEFT')
            ->join('dclevel l','l.id=p.level','LEFT')
            ->where('y.is_delete', 0)
            ->where('p.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('y.type','dc')
            ->where($where)
            //必须结项
            ->where('p.status',21)
            ->distinct(true)
            ->select();
        $where=[];
        $field='p.name,l.name as l_level,CONCAT(a.level, a.type) AS award,p.created_at as time,c.name as c_name';
//            $field='p.uid, p.name';
        if ($user['usermode']==1){
            $where[]=['m.username','=',$username];
            $field=$field.',m.rank';
        }elseif ($user['usermode']==2){
            $where[]=['t.username','=',$username];
            $field=$field.',t.rank';
        }
        $jss = Yckproject::alias('y')
            ->field($field)
            ->join('jsproject p', 'p.uid = y.uid', 'LEFT')

            ->join('jscompetition c', 'c.cuid = p.cuid', 'LEFT')
            ->join('jsaward a', 'a.id = p.award', 'LEFT')
            ->join('member m', 'm.uid = y.uid', 'LEFT')
            ->join('teacher t', 't.uid = y.uid', 'LEFT')
            ->join('jslevel l','l.id=c.level','LEFT')
            ->where('p.is_delete', 0)
            ->where('c.is_delete', 0)
            ->where('m.is_delete', 0)
            ->where('t.is_delete', 0)
            ->where('y.is_delete', 0)
            ->where('y.type','js')
            ->where($where)
            //必须结项
            ->where('p.status',8)
            ->distinct(true)

            ->select();
        $progesses=Yckprogress::where('is_delete',0)->where('username',$username)->order('created_at', 'asc')->select();
        $checks=[
            //申请审核
            'status1'=>[
                'school'=>Yckcheck::order('created_at', 'asc')->where('is_delete',0)->where('username',$username)->where('status',1)->where('type',3)->select(),
            ],
        ];
        $data=[
            'user'=>$user,
            'domains'=>$domains,
            'tags'=>$tags,
            'dcs'=>$dcs,
            'jss'=>$jss,
            'progesses'=>$progesses,
            'checks'=>$checks
        ];
        return view('yckmanage/applydetail',['data'=>$data]);
    }
}