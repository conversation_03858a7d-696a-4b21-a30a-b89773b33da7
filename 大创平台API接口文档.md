# 大创平台API接口文档

## 1. 接口概述

### 1.1 接口规范
- **基础URL**: `http://your-domain.com`
- **请求方式**: GET/POST
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 响应格式
```json
{
    "status": "success|error",
    "message": "响应消息",
    "data": {}
}
```

### 1.3 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 2. 用户认证接口

### 2.1 用户登录
**接口地址**: `POST /basic/login`

**请求参数**:
```json
{
    "username": "学号/工号",
    "password": "密码",
    "usermode": "用户角色(1:学生,2:教师,3:学院,4:学校,11:超级管理员)"
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1,
            "username": "2021001",
            "name": "张三",
            "usermode": 1,
            "department": "计算机学院"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

### 2.2 用户登出
**接口地址**: `POST /basic/logout`

**请求参数**: 无

**响应示例**:
```json
{
    "status": "success",
    "message": "登出成功"
}
```

## 3. 项目立项接口

### 3.1 项目立项申请
**接口地址**: `POST /dcmanage/addProject/addProject`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "name": "项目名称",
    "type": 1,
    "period": 1,
    "level": 1,
    "introduction": "项目简介",
    "reason": "申请理由",
    "innovation": "项目特色及创新点",
    "schedule": "项目进度安排",
    "budget": 1000.00,
    "plan": "经费使用计划",
    "members": [
        {
            "username": "2021001",
            "rank": 1
        }
    ],
    "teachers": [
        {
            "username": "T001",
            "rank": 1,
            "type": 0
        }
    ]
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "项目立项申请提交成功"
}
```

### 3.2 获取项目详情
**接口地址**: `GET /dcmanage/detail/index?uid={uid}`

**请求参数**:
- `uid`: 项目唯一标识

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "project": {
            "uid": "DC2025001",
            "name": "智能校园管理系统",
            "type": 1,
            "period": 1,
            "level": 1,
            "status": 1,
            "introduction": "项目简介...",
            "reason": "申请理由...",
            "innovation": "创新点...",
            "schedule": "进度安排...",
            "budget": 1000.00,
            "plan": "经费计划..."
        },
        "members": [
            {
                "username": "2021001",
                "name": "张三",
                "rank": 1
            }
        ],
        "teachers": [
            {
                "username": "T001",
                "name": "李老师",
                "rank": 1,
                "type": 0
            }
        ],
        "checks": [
            {
                "type": 1,
                "status": 1,
                "check": 0,
                "remark": "审核通过",
                "created_at": "2025-01-13 10:00:00"
            }
        ]
    }
}
```

## 4. 项目审核接口

### 4.1 项目审核
**接口地址**: `POST /dcmanage/check/checkProject`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "check": {
        "check": 0,
        "remark": "审核意见"
    }
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "审核成功"
}
```

### 4.2 获取待审核项目列表
**接口地址**: `GET /dcmanage/lists/index`

**请求参数**:
- `page`: 页码（可选，默认1）
- `limit`: 每页数量（可选，默认10）
- `status`: 项目状态（可选）

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "total": 100,
        "list": [
            {
                "uid": "DC2025001",
                "name": "智能校园管理系统",
                "status": 1,
                "created_at": "2025-01-13 10:00:00"
            }
        ]
    }
}
```

## 5. 中期报告接口

### 5.1 提交中期报告
**接口地址**: `POST /dcmanage/intermproject/add_interm`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "progress": "项目进展情况",
    "achievement": "阶段性成果",
    "problem": "存在的问题",
    "plan": "下一步计划"
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "中期报告提交成功"
}
```

### 5.2 获取中期报告详情
**接口地址**: `GET /dcmanage/intermproject/index?uid={uid}`

**请求参数**:
- `uid`: 项目唯一标识

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "interm": {
            "progress": "项目进展情况...",
            "achievement": "阶段性成果...",
            "problem": "存在的问题...",
            "plan": "下一步计划...",
            "created_at": "2025-01-13 10:00:00"
        }
    }
}
```

## 6. 结题报告接口

### 6.1 提交结题报告
**接口地址**: `POST /dcmanage/concludeproject/add_conclude`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "results": "项目成果简介",
    "conclusion": "项目总结报告",
    "problem": "项目实施过程中存在的问题和建议",
    "excellent_project": 0,
    "excellent_lunwen": 0
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "结题报告提交成功"
}
```

### 6.2 获取结题报告详情
**接口地址**: `GET /dcmanage/concludeproject/index?uid={uid}`

**请求参数**:
- `uid`: 项目唯一标识

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "conclude": {
            "results": "项目成果简介...",
            "conclusion": "项目总结报告...",
            "problem": "问题与建议...",
            "excellent_project": 0,
            "excellent_lunwen": 0,
            "created_at": "2025-01-13 10:00:00"
        }
    }
}
```

## 7. 延期申请接口

### 7.1 提交延期申请
**接口地址**: `POST /dcmanage/extension/apply_extension`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "extension_time": 6,
    "reason": "延期理由"
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "延期申请提交成功"
}
```

### 7.2 获取延期申请详情
**接口地址**: `GET /dcmanage/extension/index?uid={uid}`

**请求参数**:
- `uid`: 项目唯一标识

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "extension": {
            "extension_time": 6,
            "reason": "延期理由...",
            "created_at": "2025-01-13 10:00:00"
        }
    }
}
```

## 8. 中期变更接口

### 8.1 提交中期变更申请
**接口地址**: `POST /dcmanage/midchange/apply_midchange`

**请求参数**:
```json
{
    "uid": "项目唯一标识",
    "change_items": "变更事项",
    "change_reason": "变更原因",
    "new_members": [
        {
            "username": "2021002",
            "rank": 2
        }
    ],
    "new_teachers": [
        {
            "username": "T002",
            "rank": 1,
            "type": 0
        }
    ]
}
```

**响应示例**:
```json
{
    "status": "success",
    "message": "中期变更申请提交成功"
}
```

### 8.2 获取中期变更详情
**接口地址**: `GET /dcmanage/midchange/index?uid={uid}`

**请求参数**:
- `uid`: 项目唯一标识

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "midchange": {
            "change_items": "变更事项...",
            "change_reason": "变更原因...",
            "new_members": "[{\"username\":\"2021002\",\"rank\":2}]",
            "new_teachers": "[{\"username\":\"T002\",\"rank\":1,\"type\":0}]",
            "created_at": "2025-01-13 10:00:00"
        }
    }
}
```

## 9. 用户搜索接口

### 9.1 搜索用户
**接口地址**: `POST /basic/searchuser`

**请求参数**:
```json
{
    "type": 1,
    "query": "搜索关键词"
}
```

**参数说明**:
- `type`: 用户类型（1:学生, 2:教师）
- `query`: 搜索关键词（姓名或学号/工号）

**响应示例**:
```json
{
    "status": "success",
    "message": "搜索成功",
    "data": [
        {
            "username": "2021001",
            "name": "张三",
            "department": "计算机学院"
        }
    ]
}
```

## 10. 文件上传接口

### 10.1 上传文件
**接口地址**: `POST /basic/files/upload`

**请求参数**:
- `file`: 文件对象
- `type`: 文件类型（可选）

**响应示例**:
```json
{
    "status": "success",
    "message": "上传成功",
    "data": {
        "filename": "project_doc.pdf",
        "url": "/uploads/project_doc.pdf"
    }
}
```

## 11. 数据导出接口

### 11.1 导出项目列表
**接口地址**: `GET /dcmanage/lists/export`

**请求参数**:
- `type`: 导出类型（excel, pdf）
- `status`: 项目状态（可选）
- `date_range`: 日期范围（可选）

**响应示例**:
```json
{
    "status": "success",
    "message": "导出成功",
    "data": {
        "download_url": "/exports/项目列表_20250113.xlsx"
    }
}
```

## 12. 统计接口

### 12.1 获取项目统计
**接口地址**: `GET /dcmanage/lists/statistics`

**请求参数**: 无

**响应示例**:
```json
{
    "status": "success",
    "message": "获取成功",
    "data": {
        "total": 100,
        "by_status": {
            "1": 10,
            "2": 15,
            "3": 20,
            "4": 25,
            "5": 10,
            "6": 5,
            "7": 5,
            "8": 5,
            "9": 3,
            "10": 1,
            "11": 1
        },
        "by_type": {
            "1": 60,
            "2": 30,
            "3": 10
        },
        "by_level": {
            "1": 50,
            "2": 30,
            "3": 20
        }
    }
}
```

## 13. 错误处理

### 13.1 常见错误响应

#### 参数错误
```json
{
    "status": "error",
    "message": "参数错误: uid不能为空"
}
```

#### 权限不足
```json
{
    "status": "error",
    "message": "权限不足，无法执行此操作"
}
```

#### 状态错误
```json
{
    "status": "error",
    "message": "当前项目状态不允许执行此操作"
}
```

#### 数据不存在
```json
{
    "status": "error",
    "message": "项目不存在"
}
```

### 13.2 错误码说明
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权访问 | 检查用户登录状态 |
| 403 | 权限不足 | 检查用户角色和权限 |
| 404 | 资源不存在 | 检查请求的资源是否存在 |
| 500 | 服务器内部错误 | 联系系统管理员 |

## 14. 接口调用示例

### 14.1 JavaScript调用示例
```javascript
// 用户登录
async function login(username, password, usermode) {
    try {
        const response = await fetch('/basic/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password,
                usermode: usermode
            })
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('登录失败:', error);
        return { status: 'error', message: '网络错误' };
    }
}

// 提交项目立项
async function submitProject(projectData) {
    try {
        const response = await fetch('/dcmanage/addProject/addProject', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(projectData)
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('提交失败:', error);
        return { status: 'error', message: '网络错误' };
    }
}
```

### 14.2 PHP调用示例
```php
// 用户登录
function login($username, $password, $usermode) {
    $url = '/basic/login';
    $data = [
        'username' => $username,
        'password' => $password,
        'usermode' => $usermode
    ];
    
    $response = httpPost($url, $data);
    return json_decode($response, true);
}

// 提交项目立项
function submitProject($projectData) {
    $url = '/dcmanage/addProject/addProject';
    $response = httpPost($url, $projectData);
    return json_decode($response, true);
}

function httpPost($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return $response;
}
```

## 15. 接口安全

### 15.1 认证机制
- 所有接口都需要用户登录认证
- 使用Session或Token进行身份验证
- 定期刷新Token确保安全

### 15.2 权限控制
- 基于用户角色的权限控制
- 接口级别的权限验证
- 数据级别的权限过滤

### 15.3 数据验证
- 输入参数验证和过滤
- SQL注入防护
- XSS攻击防护

### 15.4 日志记录
- 记录所有接口调用日志
- 记录用户操作行为
- 异常情况监控和告警

---

**文档版本**: v1.0  
**最后更新**: 2025-01-13  
**维护人员**: 开发团队 