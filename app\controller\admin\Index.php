<?php

namespace app\controller\admin;

use app\BaseController;
use app\model\User;
use app\model\Yckuser;
use app\model\Usermode;
use app\service\DashboardService;

class Index extends BaseController
{
    public function index()
    {
        LogExecution('进入后台');
        $user = User::alias('u')
            ->join('usermode md', 'md.id=u.usermode', 'LEFT')
            ->where('u.status',0)
            ->where('u.username',session('user.username'))
            ->field('u.username,u.name,u.usermode,u.college,md.group_name as usermode_text')
            ->find();
        $yck = Yckuser::where('username',session('user.username'))->where('is_delete',0)->where('status',2)->find();
            return view('admin/index',['user'=>$user,'yck'=>$yck]);
    }
    
    /**
     * 仪表盘页面
     */
    public function dashboard()
    {
        LogExecution('进入仪表盘页面');
        $user = User::alias('u')
            ->join('usermode md', 'md.id=u.usermode', 'LEFT')
            ->where('u.status',0)
            ->where('u.username',session('user.username'))
            ->field('u.username,u.name,u.usermode,u.college,md.group_name as usermode_text')
            ->find();
        return view('admin/dashboard',['user'=>$user]);
    }
    
    /**
     * 获取仪表盘数据
     */
    public function getDashboardData()
    {
        $userMode = session('user.usermode');
        $username = session('user.username');
        $college = session('user.college');
        
        try {
            $data = DashboardService::getDashboardData($userMode, $username, $college);
            return json(['status' => 'success', 'message' => $data]);
        } catch (\Exception $e) {
            LogExecution('获取仪表盘数据失败：' . $e->getMessage());
            return json(['status' => 'error', 'message' => '获取数据失败']);
        }
    }
}