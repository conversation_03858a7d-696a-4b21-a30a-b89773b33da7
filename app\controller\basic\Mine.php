<?php

namespace app\controller\basic;

use app\BaseController;
use app\model\User;
use think\facade\Db;

class Mine extends BaseController
{
    public function index(){
        return view('basic/mine');
    }
    public function mine(){
        $mine=User::
            alias('u')
            ->join('usermode md','md.id=u.usermode','LEFT')
            ->join('department d','d.id=u.college','LEFT')
            ->join('major m','m.id=u.major','LEFT')
            ->where('u.status',0)
            ->where('md.is_delete',0)
            ->where('u.username',session('user.username'))
            ->field('
                u.username,u.name,u.email,u.phone,u.job,u.grade,
                md.group_name as m_name,
                d.name as department,
                m.name as major
            ')
            ->find();
        return json(['status' => 'success', 'message' => $mine]);
    }

    public function update(){
        $data=input('post.data');
        //先修改信息再修改密码
        $user=User::where('status',0)->where('username',session('user.username'))->update(
            ['phone'=>$data['phone'],'email'=>$data['email']]
        );
        //修改密码
        $pwd=0;//状态
        if ($data['oldPassword']&&$data['newPassword']&&$data['confirmPassword']){
            if ($data['newPassword']!=$data['confirmPassword']){
                LogExecution(session('user.username').'修改个人信息失败，两次密码不一致');
                return ['status'=>'success','message'=>'两次密码不一致'];
            }
            $user = User::where('username', session('user.username'))->where('status',0)->find();
            if ($user && password_verify($data['oldPassword'], $user->password)) {
                $pwd=User::where('username', session('user.username'))->where('status',0)->update(['password'=>password_hash($data['newPassword'],PASSWORD_BCRYPT)]);
            }else{
                LogExecution(session('user.username').'修改个人信息失败，原密码错误');
                return ['status'=>'success','message'=>'原密码错误'];
            }

        }
        if ($pwd&&$user){
            LogExecution(session('user.username').'修改个人信息成功');
            return ['status'=>'success','message'=>'提交修改成功'];
        }else{
            LogExecution(session('user.username').'修改个人信息失败，插入异常');
            return ['status'=>'success','message'=>'提交修改失败'];
        }
    }
}