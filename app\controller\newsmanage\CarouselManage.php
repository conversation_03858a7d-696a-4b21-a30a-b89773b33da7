<?php

namespace app\controller\newsmanage;

use app\BaseController;
use app\model\Carousel;
use app\model\Newsdetail;
use app\model\Newsclass;

class CarouselManage extends BaseController
{
    /**
     * 轮播管理首页
     */
    public function index()
    {
        LogExecution('进入轮播管理');
        return view('newsmanage/carousel');
    }
    
    /**
     * 获取轮播列表
     */
    public function getCarouselList()
    {
        $search = input('post.search', []);
        $where = [];
        $where[] = ['c.is_delete', '=', 0];
        
        // 搜索条件
        if (!empty($search['title'])) {
            $where[] = ['c.title', 'like', '%' . $search['title'] . '%'];
        }
        if (isset($search['status']) && $search['status'] !== '') {
            $where[] = ['c.status', '=', $search['status']];
        }
        
        $data = Carousel::alias('c')
            ->field('c.*, n.title as news_title, nc.name as news_class')
            ->join('newsdetail n', 'n.id = c.news_id', 'LEFT')
            ->join('newsclass nc', 'nc.id = n.class', 'LEFT')
            ->where($where)
            ->order('c.sort_order', 'asc')
            ->order('c.id', 'desc')
            ->select();
            
        return json([
            'status' => 'success', 
            'message' => [
                'total' => count($data),
                'data' => $data
            ]
        ]);
    }
    
    /**
     * 添加轮播
     */
    public function addCarousel()
    {
        $data = input('post.data');
        
        if (!$data) {
            // 获取新闻列表供选择
            $newsList = Newsdetail::alias('n')
                ->field('n.id, n.title, nc.name as class_name')
                ->join('newsclass nc', 'nc.id = n.class', 'LEFT')
                ->where('n.is_delete', 0)
                ->where('nc.is_delete', 0)
                ->order('n.created_at', 'desc')
                ->select();
                
            return view('newsmanage/carousel_edit', ['newsList' => $newsList]);
        }
        
        // 验证数据
        $validate = [
            'title' => 'require',
            'image_path' => 'require',
            'news_id' => 'require|number'
        ];
        
        $message = [
            'title.require' => '请输入轮播标题',
            'image_path.require' => '请上传轮播图片',
            'news_id.require' => '请选择关联新闻',
            'news_id.number' => '新闻ID格式错误'
        ];
        
        try {
            $this->validate($data, $validate, $message);
        } catch (\Exception $e) {
            return json(['status' => 'error', 'message' => $e->getMessage()]);
        }
        
        // 获取最大排序值
        $maxSort = Carousel::where('is_delete', 0)->max('sort_order') ?: 0;
        $data['sort_order'] = $maxSort + 1;
        
        // 创建轮播
        $carousel = new Carousel();
        $carousel->title = $data['title'];
        $carousel->image_path = $data['image_path'];
        $carousel->news_id = $data['news_id'];
        $carousel->sort_order = $data['sort_order'];
        $carousel->status = $data['status'] ?? 1;
        
        if ($carousel->save()) {
            LogExecution('添加轮播成功：' . $data['title']);
            return json(['status' => 'success', 'message' => '添加成功']);
        } else {
            return json(['status' => 'error', 'message' => '添加失败']);
        }
    }
    
    /**
     * 编辑轮播
     */
    public function editCarousel($id = '')
    {
        $data = input('post.data');
        
        if (!$data) {
            // 获取轮播信息
            $carousel = Carousel::where('id', $id)->where('is_delete', 0)->find();
            if (!$carousel) {
                return json(['status' => 'error', 'message' => '轮播不存在']);
            }
            
            // 获取新闻列表
            $newsList = Newsdetail::alias('n')
                ->field('n.id, n.title, nc.name as class_name')
                ->join('newsclass nc', 'nc.id = n.class', 'LEFT')
                ->where('n.is_delete', 0)
                ->where('nc.is_delete', 0)
                ->order('n.created_at', 'desc')
                ->select();
                
            return view('newsmanage/carousel_edit', [
                'carousel' => $carousel,
                'newsList' => $newsList
            ]);
        }
        
        // 从POST数据中获取ID
        $editId = input('post.id');
        if (!$editId) {
            return json(['status' => 'error', 'message' => '缺少轮播ID']);
        }
        
        // 验证数据
        $validate = [
            'title' => 'require',
            'image_path' => 'require',
            'news_id' => 'require|number'
        ];
        
        $message = [
            'title.require' => '请输入轮播标题',
            'image_path.require' => '请上传轮播图片',
            'news_id.require' => '请选择关联新闻',
            'news_id.number' => '新闻ID格式错误'
        ];
        
        try {
            $this->validate($data, $validate, $message);
        } catch (\Exception $e) {
            return json(['status' => 'error', 'message' => $e->getMessage()]);
        }
        
        // 更新轮播
        $carousel = Carousel::where('id', $editId)->where('is_delete', 0)->find();
        if (!$carousel) {
            return json(['status' => 'error', 'message' => '轮播不存在']);
        }
        
        $carousel->title = $data['title'];
        $carousel->image_path = $data['image_path'];
        $carousel->news_id = $data['news_id'];
        $carousel->status = $data['status'] ?? 1;
        
        if ($carousel->save()) {
            LogExecution('编辑轮播成功：' . $data['title']);
            return json(['status' => 'success', 'message' => '编辑成功']);
        } else {
            return json(['status' => 'error', 'message' => '编辑失败']);
        }
    }
    
    /**
     * 删除轮播
     */
    public function deleteCarousel()
    {
        $id = input('post.id');
        
        if (!$id) {
            return json(['status' => 'error', 'message' => '参数错误']);
        }
        
        $carousel = Carousel::where('id', $id)->where('is_delete', 0)->find();
        if (!$carousel) {
            return json(['status' => 'error', 'message' => '轮播不存在']);
        }
        
        $carousel->is_delete = 1;
        if ($carousel->save()) {
            LogExecution('删除轮播成功：' . $carousel->title);
            return json(['status' => 'success', 'message' => '删除成功']);
        } else {
            return json(['status' => 'error', 'message' => '删除失败']);
        }
    }
    
    /**
     * 更新轮播状态
     */
    public function updateStatus()
    {
        $id = input('post.id');
        $status = input('post.status');
        
        if (!$id || !isset($status)) {
            return json(['status' => 'error', 'message' => '参数错误']);
        }
        
        $carousel = Carousel::where('id', $id)->where('is_delete', 0)->find();
        if (!$carousel) {
            return json(['status' => 'error', 'message' => '轮播不存在']);
        }
        
        $carousel->status = $status;
        if ($carousel->save()) {
            $statusText = $status ? '启用' : '禁用';
            LogExecution('更新轮播状态成功：' . $carousel->title . ' - ' . $statusText);
            return json(['status' => 'success', 'message' => '状态更新成功']);
        } else {
            return json(['status' => 'error', 'message' => '状态更新失败']);
        }
    }
    
    /**
     * 更新轮播排序
     */
    public function updateSort()
    {
        $data = input('post.data');
        
        if (!$data || !is_array($data)) {
            return json(['status' => 'error', 'message' => '参数错误']);
        }
        
        $success = true;
        foreach ($data as $item) {
            if (!isset($item['id']) || !isset($item['sort_order'])) {
                continue;
            }
            
            $carousel = Carousel::where('id', $item['id'])->where('is_delete', 0)->find();
            if ($carousel) {
                $carousel->sort_order = $item['sort_order'];
                if (!$carousel->save()) {
                    $success = false;
                }
            }
        }
        
        if ($success) {
            LogExecution('更新轮播排序成功');
            return json(['status' => 'success', 'message' => '排序更新成功']);
        } else {
            return json(['status' => 'error', 'message' => '排序更新失败']);
        }
    }
    
    /**
     * 获取前台轮播数据
     */
    public function getHomepageCarousel()
    {
        // 调试信息
        LogExecution('开始获取前台轮播数据');
        
        // 先查询所有轮播数据
        $allCarousels = Carousel::where('status', 1)->where('is_delete', 0)->select();
        LogExecution('查询到轮播数据数量: ' . count($allCarousels));
        
        $data = Carousel::alias('c')
            ->field('c.id, c.title, c.image_path, c.news_id, n.title as news_title')
            ->join('newsdetail n', 'n.id = c.news_id', 'LEFT')
            ->where('c.status', 1)
            ->where('c.is_delete', 0)
            ->order('c.sort_order', 'asc')
            ->order('c.id', 'desc')
            ->select();
            
        LogExecution('最终轮播数据数量: ' . count($data));
        
        return json([
            'status' => 'success',
            'message' => [
                'data' => $data
            ]
        ]);
    }
    
    /**
     * 获取新闻列表
     */
    public function getNewsList()
    {
        $newsList = Newsdetail::alias('n')
            ->field('n.id, n.title, nc.name as class_name')
            ->join('newsclass nc', 'nc.id = n.class', 'LEFT')
            ->where('n.is_delete', 0)
            ->where('nc.is_delete', 0)
            ->order('n.created_at', 'desc')
            ->select();
            
        return json([
            'status' => 'success',
            'message' => [
                'data' => $newsList
            ]
        ]);
    }
    
    /**
     * 获取单个轮播信息
     */
    public function getCarouselInfo()
    {
        $id = input('get.id');
        
        if (!$id) {
            return json(['status' => 'error', 'message' => '参数错误']);
        }
        
        $carousel = Carousel::where('id', $id)->where('is_delete', 0)->find();
        if (!$carousel) {
            return json(['status' => 'error', 'message' => '轮播不存在']);
        }
        
        return json([
            'status' => 'success',
            'message' => $carousel
        ]);
    }
} 