# 大学生创新创业管理系统权限控制实现说明

## 概述

本文档详细说明了大学生创新创业管理系统的权限控制实现方案，包括用户组权限划分、权限控制机制、以及具体的实现代码。

## 用户组权限矩阵

### 用户组定义
| 用户组ID | 用户组名称 | 权限范围 |
|---------|-----------|----------|
| 1 | 学生 | 查看所有信息，可以申请项目，可以查看竞赛，可以查看英才库，可以查看新闻 |
| 2 | 教师 | 可以查看所有信息，可以查看竞赛，可以查看英才库，可以查看新闻，可以审核自己指导的项目 |
| 3 | 大创院级管理员 | 对本学院的大创项目进行查看和审核，对本学院学生进行重置密码操作 |
| 4 | 大创校级管理员 | 对所有学院的大创项目进行查看和审核，对所有学院学生进行重置密码操作 |
| 5 | 竞赛院级管理员 | 对本学院的竞赛项目进行查看和审核，对本学院学生进行重置密码操作 |
| 6 | 竞赛校级管理员 | 对所有学院的竞赛项目进行查看和审核，对所有学院学生进行重置密码操作 |
| 7 | 英才库校级管理员 | 对所有学院的英才库进行查看和审核，对所有学院学生进行重置密码操作 |
| 8-13 | 新闻管理员 | 对指定新闻板块进行查看和编辑 |
| 11 | 超级管理员 | 拥有所有权限 |

## 权限控制实现

### 1. 权限服务类 (PermissionService.php)

创建了统一的权限控制服务类，提供以下功能：

#### 核心方法
- `hasPermission($permission)`: 检查用户是否有指定权限
- `canOperateCollege($targetCollege)`: 检查用户是否可以操作指定学院
- `canViewProject($projectUid, $projectType)`: 检查用户是否可以查看项目
- `canEditProject($projectUid, $projectType)`: 检查用户是否可以编辑项目
- `canCheckProject($projectUid, $projectType)`: 检查用户是否可以审核项目
- `isProjectLeader($projectUid, $projectType)`: 检查用户是否为项目负责人
- `isProjectTeacher($projectUid, $projectType)`: 检查用户是否为项目指导教师

#### 权限标识定义
```php
// 大创项目权限
'dc_view'           // 查看大创项目
'dc_apply'          // 申请大创项目
'dc_edit'           // 编辑大创项目
'dc_check_teacher'  // 教师审核大创项目
'dc_check_college'  // 学院审核大创项目
'dc_check_school'   // 学校审核大创项目

// 竞赛项目权限
'js_view'           // 查看竞赛项目
'js_apply'          // 申请竞赛项目
'js_edit'           // 编辑竞赛项目
'js_check_teacher'  // 教师审核竞赛项目
'js_check_college'  // 学院审核竞赛项目
'js_check_school'   // 学校审核竞赛项目

// 英才库权限
'yck_view'          // 查看英才库
'yck_apply'         // 申请英才库
'yck_check'         // 审核英才库

// 新闻管理权限
'news_view'         // 查看新闻
'news_edit'         // 编辑新闻
'news_manage'       // 管理新闻

// 用户管理权限
'user_manage'       // 管理用户
'user_reset_password' // 重置密码

// 系统管理权限
'system_manage'     // 管理系统
```

### 2. 权限中间件 (CheckPermission.php)

创建了权限检查中间件，用于在路由层面进行权限控制：

```php
// 使用示例
Route::get('dc-detail', '/dcmanage/detail/index')->middleware('CheckPermission:dc_view');
```

### 3. 基础控制器扩展 (BaseController.php)

在基础控制器中添加了权限检查方法：

```php
protected function checkPermission($permission)
protected function checkCollegePermission($targetCollege)
protected function checkProjectPermission($projectUid, $projectType)
protected function getCurrentUser()
protected function getCurrentUserMode()
```

## 数据过滤实现

### 1. 大创项目列表过滤
- 院级管理员只能查看本学院的项目
- 校级管理员可以查看所有项目

### 2. 竞赛项目列表过滤
- 院级管理员只能查看本学院的项目
- 校级管理员可以查看所有项目

### 3. 英才库列表过滤
- 英才库校级管理员只能查看本学院的数据

### 4. 用户管理列表过滤
- 院级管理员只能查看本学院的用户
- 校级管理员可以查看所有用户

## 前端权限控制

### 1. 管理后台菜单权限控制

在 `app/view/admin/index.html` 中根据用户权限显示菜单：

```html
<el-submenu index="dc" v-if="menuPermissions.dc">
  <el-menu-item index="dc-addproject" v-if="canAddProject">
```

### 2. 悬浮菜单权限控制

在项目详情页面中，根据用户权限显示不同的操作选项：

```html
<!-- 学生操作 - 只有项目负责人或超级管理员可见 -->
<el-menu-item-group v-if="isStudentOperation">

<!-- 教师操作 - 只有指导教师或超级管理员可见 -->
<el-menu-item-group v-if="isTeacherOperation">

<!-- 学院操作 - 只有院级管理员或超级管理员可见 -->
<el-menu-item-group v-if="isCollegeOperation">

<!-- 学校操作 - 只有校级管理员或超级管理员可见 -->
<el-menu-item-group v-if="isSchoolOperation">

<!-- 超级管理员操作 - 只有超级管理员可见 -->
<el-menu-item-group v-if="isSuperAdmin">
```

### 3. JavaScript权限控制

在 `public/static/js/admin.js` 中实现权限初始化：

```javascript
initPermissions() {
  const userMode = window.userinfo ? window.userinfo.usermode : null;
  
  switch (userMode) {
    case 1: // 学生
      this.menuPermissions.dc = true;
      this.menuPermissions.js = true;
      this.menuPermissions.yck = true;
      this.menuPermissions.news = true;
      this.canAddProject = true;
      break;
    // ... 其他用户组
  }
}
```

## 控制器权限检查

### 1. 项目详情页面权限检查

```php
// 检查用户是否有权限查看该项目
if (!$this->checkPermission('dc_view') || !$this->canViewProject($uid, 'dc')) {
    LogExecution('用户尝试查看无权限的项目：' . $uid);
    return json(['status' => 'error', 'message' => '权限不足']);
}
```

### 2. 项目审核权限检查

```php
// 检查用户是否有权限审核该项目
if (!$this->checkPermission('dc_check_teacher') && 
    !$this->checkPermission('dc_check_college') && 
    !$this->checkPermission('dc_check_school')) {
    LogExecution('用户尝试审核无权限的项目：' . $uid);
    return json(['status' => 'error', 'message' => '权限不足']);
}

// 检查用户是否可以审核该项目
if (!$this->canCheckProject($uid, 'dc')) {
    LogExecution('用户尝试审核无权限的项目：' . $uid);
    return json(['status' => 'error', 'message' => '您没有权限审核该项目']);
}
```

### 3. 用户管理权限检查

```php
// 检查用户是否有权限管理用户
if (!$this->checkPermission('user_manage')) {
    LogExecution(session('user.username') . '尝试访问用户管理');
    return json(['status' => 'error', 'message' => '权限不足']);
}
```

## 权限控制特点

### 1. 多层次权限控制
- **路由层**: 使用中间件进行权限检查
- **控制器层**: 在方法中进行权限验证
- **数据层**: 根据权限过滤查询结果
- **视图层**: 根据权限显示/隐藏界面元素

### 2. 细粒度权限控制
- 项目级别的权限控制（查看、编辑、审核）
- 学院级别的数据过滤
- 功能级别的菜单控制

### 3. 安全性保障
- 前后端双重权限验证
- 完整的操作日志记录
- 统一的错误处理机制

### 4. 灵活性
- 基于用户组的权限控制
- 支持动态权限配置
- 易于扩展和维护

## 使用说明

### 1. 添加新的权限检查
```php
// 在控制器中使用
if (!$this->checkPermission('new_permission')) {
    return json(['status' => 'error', 'message' => '权限不足']);
}
```

### 2. 添加新的权限标识
```php
// 在PermissionService.php中添加
case 'new_permission':
    return in_array($userMode, [1, 2, 11]);
```

### 3. 前端权限控制
```javascript
// 在Vue组件中添加权限检查
computed: {
    canDoSomething() {
        return this.userMode == 11 || this.userMode == 2;
    }
}
```

## 注意事项

1. **权限检查顺序**: 先检查用户组权限，再检查具体操作权限
2. **数据过滤**: 在查询数据时根据用户权限进行过滤
3. **日志记录**: 所有权限相关的操作都要记录日志
4. **错误处理**: 权限不足时返回统一的错误信息
5. **前端验证**: 前端权限控制只是用户体验优化，后端验证才是安全保障

## 扩展建议

1. **角色权限管理**: 可以添加角色管理功能，支持更灵活的权限配置
2. **权限继承**: 可以实现权限继承机制，简化权限配置
3. **动态权限**: 支持运行时动态调整用户权限
4. **权限审计**: 添加权限使用审计功能，便于权限优化 