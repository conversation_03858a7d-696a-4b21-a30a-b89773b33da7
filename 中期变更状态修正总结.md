# 中期变更状态修正总结

## 修正内容

### 1. 状态值修正
将错误的中期变更状态值（39-42）修正为正确的状态值（33-38）：

- 39 → 33：等待指导教师中期变更审核
- 40 → 34：等待学院中期变更审核  
- 41 → 35：等待学校中期变更审核
- 42 → 4：中期变更审核通过，可提交中期报告

### 2. 修正的文件

#### 后端控制器
1. **app/controller/dcmanage/Check.php**
   - 修正审核权限验证中的状态列表
   - 修正状态映射逻辑
   - 修正审核通过和驳回的状态流转

2. **app/controller/dcmanage/Midchange.php**
   - 修正状态计算逻辑
   - 添加成员和教师数量验证（成员最多4名，教师最多2名）

3. **app/controller/dcmanage/Intermproject.php**
   - 修正中期报告提交的状态检查
   - 移除错误的状态42引用

#### 前端页面
1. **app/view/dcmanage/midchange.html**
   - 修正状态显示文本
   - 添加成员和教师数量限制
   - 添加选择数量提示和禁用逻辑

#### 数据库
1. **add_midchange_status.sql**
   - 创建SQL文件添加缺失的中期变更状态（33-38）

### 3. 成员和教师数量限制

根据数据库member表的rank字段设计：
- **成员限制**：最多选择4名成员（不含队长，rank=1为队长）
- **教师限制**：最多选择2名教师（rank=1为第一指导教师）

### 4. 状态流转逻辑

#### 中期变更申请流程
1. 状态4（已立项待提交中期报告）→ 状态33（等待教师审核）
2. 状态36（教师驳回）→ 状态33（重新申请）
3. 状态37（学院驳回）→ 状态34（重新申请）
4. 状态38（学校驳回）→ 状态35（重新申请）

#### 中期变更审核流程
1. 教师审核：状态33 → 状态34（通过）或状态36（驳回）
2. 学院审核：状态34 → 状态35（通过）或状态37（驳回）
3. 学校审核：状态35 → 状态4（通过，数据更新）或状态38（驳回）

### 5. 数据更新逻辑

当学校审核通过中期变更后（状态35→4）：
1. 软删除原有成员和教师数据
2. 插入新成员数据（rank从2开始，1为队长）
3. 插入新教师数据（rank从1开始）
4. 记录变更日志

### 6. 验证规则

#### 前端验证
- 成员选择最多4名（不含队长）
- 教师选择最多2名
- 变更事项和变更原因必填，最多500字

#### 后端验证
- 验证项目负责人权限
- 验证项目状态是否允许申请
- 验证成员和教师数量限制
- 验证字数限制

### 7. 需要执行的数据库操作

请执行以下SQL语句添加缺失的中期变更状态：

```sql
-- 添加中期变更相关状态
INSERT INTO `dcstatus` VALUES (33, '等待指导教师中期变更审核', '2025-03-12 10:25:30', '2025-03-12 10:25:30', 0);
INSERT INTO `dcstatus` VALUES (34, '等待学院中期变更审核', '2025-03-12 10:25:40', '2025-03-12 10:25:40', 0);
INSERT INTO `dcstatus` VALUES (35, '等待学校中期变更审核', '2025-03-12 10:25:50', '2025-03-12 10:25:50', 0);
INSERT INTO `dcstatus` VALUES (36, '指导教师中期变更驳回', '2025-03-12 10:26:00', '2025-03-12 10:26:00', 0);
INSERT INTO `dcstatus` VALUES (37, '学院中期变更驳回', '2025-03-12 10:26:10', '2025-03-12 10:26:10', 0);
INSERT INTO `dcstatus` VALUES (38, '学校中期变更驳回', '2025-03-12 10:26:20', '2025-03-12 10:26:20', 0);
```

## 修正完成

所有中期变更相关的状态值已修正为正确的33-38状态，并添加了成员和教师数量限制，确保与数据库设计一致。 