<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英才库滚动播放测试</title>
    <link rel="stylesheet" href="static/css/<EMAIL>">
    <link rel="stylesheet" href="static/css/index/index.css">
    <script src="static/js/vue2.js"></script>
    <script src="static/js/<EMAIL>"></script>
    <script src="static/js/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <div style="max-width: 800px; margin: 50px auto; padding: 20px;">
            <h2>英才库滚动播放测试</h2>
            
            <!-- 英才库展示区域 -->
            <div class="talent-box" style="height: 400px; border: 1px solid #ddd; border-radius: 8px; padding: 20px;">
                <h3 class="section-title">
                    英才库
                    <el-radio-group v-model="yck.active" @change="onTalentTypeChange">
                        <el-radio-button label="英才学生"></el-radio-button>
                        <el-radio-button label="精英教师"></el-radio-button>
                    </el-radio-group>
                </h3>
                
                <!-- 英才库滚动展示 -->
                <div class="talent-showcase">
                    <div class="talent-card" v-if="talents.length > 0">
                        <div class="talent-info">
                            <div class="talent-name">{{ getCurrentTalent().name }}</div>
                            <div class="talent-college">{{ getCurrentTalent().college }}</div>
                            <div class="talent-major" v-if="getCurrentTalent().major">{{ getCurrentTalent().major }}</div>
                            <div class="talent-tags" v-if="getCurrentTalent().tags">{{ getCurrentTalent().tags }}</div>
                        </div>
                        <div class="talent-score">
                            <el-rate disabled v-model="getCurrentTalent().score" :max="5"></el-rate>
                            <span class="score-text">{{ getCurrentTalent().score }}分</span>
                        </div>
                    </div>
                    
                    <!-- 轮播指示器 -->
                    <div class="talent-indicators" v-if="talents.length > 1">
                        <span 
                            v-for="(talent, index) in talents" 
                            :key="index"
                            :class="['indicator', { active: index === talentIndex }]"
                            @click="talentIndex = index">
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 调试信息 -->
            <div style="margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 8px;">
                <h4>调试信息：</h4>
                <p>当前类型：{{ yck.active }}</p>
                <p>数据总数：{{ talents.length }}</p>
                <p>当前索引：{{ talentIndex }}</p>
                <p>当前显示：{{ getCurrentTalent().name }}</p>
                <el-button @click="fetchTalents" type="primary">手动刷新数据</el-button>
            </div>
        </div>
    </div>

    <script>
        // 官网首页 JavaScript
        new Vue({
            el: '#app',
            data() {
                return {
                    // 英才库相关
                    yck: {
                        active: '英才学生'
                    },
                    talents: [],
                    talentIndex: 0, // 当前显示的英才索引
                    talentTimer: null, // 定时器
                }
            },
            created() {
                this.fetchTalents();
                this.startTalentRotation();
            },
            beforeDestroy() {
                this.stopTalentRotation();
            },
            methods: {
                // 英才库相关方法
                fetchTalents() {
                    console.log('开始获取英才库数据...');
                    axios.get('/yck-homepage-talents', {
                        params: {
                            type: this.yck.active,
                            limit: 10
                        }
                    }).then(response => {
                        console.log('API响应:', response);
                        if (response.data.status === 'success') {
                            // 格式化数据
                            this.talents = response.data.message.data.map(item => ({
                                name: item.name || '未知',
                                college: item.department || '未知学院',
                                score: item.mark || 0,
                                major: item.major_name || '',
                                tags: item.tags || ''
                            }));
                            
                            // 如果数据为空，显示默认信息
                            if (this.talents.length === 0) {
                                this.talents = [{
                                    name: '暂无数据',
                                    college: '暂无',
                                    score: 0,
                                    major: '',
                                    tags: ''
                                }];
                            }
                            
                            console.log('处理后的数据:', this.talents);
                        } else {
                            console.error('API返回错误:', response.data);
                            this.$message.error('获取英才库数据失败');
                            this.talents = [{
                                name: '数据加载失败',
                                college: '请稍后重试',
                                score: 0,
                                major: '',
                                tags: ''
                            }];
                        }
                    }).catch(error => {
                        console.error('获取英才库数据错误:', error);
                        this.$message.error('获取英才库数据失败');
                        this.talents = [{
                            name: '网络错误',
                            college: '请检查网络连接',
                            score: 0,
                            major: '',
                            tags: ''
                        }];
                    });
                },
                
                // 开始英才轮播
                startTalentRotation() {
                    if (this.talents.length > 1) {
                        this.talentTimer = setInterval(() => {
                            this.talentIndex = (this.talentIndex + 1) % this.talents.length;
                        }, 3000); // 每3秒切换一次
                    }
                },
                
                // 停止英才轮播
                stopTalentRotation() {
                    if (this.talentTimer) {
                        clearInterval(this.talentTimer);
                        this.talentTimer = null;
                    }
                },
                
                // 英才类型切换
                onTalentTypeChange() {
                    this.talentIndex = 0; // 重置索引
                    this.fetchTalents();
                },
                
                // 获取当前显示的英才数据
                getCurrentTalent() {
                    if (this.talents.length === 0) {
                        return {
                            name: '暂无数据',
                            college: '暂无',
                            score: 0,
                            major: '',
                            tags: ''
                        };
                    }
                    return this.talents[this.talentIndex];
                }
            },
            watch: {
                'yck.active': function() {
                    this.onTalentTypeChange();
                }
            }
        });
    </script>
</body>
</html> 