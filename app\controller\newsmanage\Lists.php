<?php

namespace app\controller\newsmanage;

use app\BaseController;
use app\model\Department;
use app\model\Newsclass;
use app\model\Newsdetail;
use app\model\Yckstatus;

class Lists extends BaseController
{

    public function index(){
        LogExecution('进入新闻列表');
        $search=[
            'classes'=>Newsclass::where('is_delete',0)->select(),
        ];
        return view('newsmanage/lists',['search'=>$search]);
    }
    public function newslist (){
        // 检查用户是否有权限查看新闻
        if (!$this->checkPermission('news_view')) {
            LogExecution(session('user.username') . '不是新闻管理员');
            return json(['status' => 'error', 'message' => '您不是新闻管理员']);
        }
        
        $search=input('post.search');

        $where=[];
        $where[]=['n.is_delete','=',0];
        $where[]=['c.is_delete','=',0];
        if ($search['classes']!='all'){
            $where[]=['n.class','=',$search['classes']];
        }
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[]=['n.title|n.auth','like','%'.$text.'%'];
            }
        }

        //返回新闻
        $news=Newsdetail::
            alias('n')
            ->field('c.name as c_name,n.id,n.title,n.created_at,n.updated_at,n.auth')
            ->join('newsclass c', 'n.class = c.id', 'LEFT')
            ->order('created_at','desc')
            ->where($where)
            ->select();
        return json(['status' => 'success', 'message' => [
            'total'=>sizeof($news),
            'data'=>$news
        ]]);
    }
    
    /**
     * 导出新闻数据
     */
    public function export_news()
    {
        $search = input('post.search');
        $where = [];
        
        if ($search['texts']){
            foreach ($search['texts'] as $text){
                $where[] = ['n.title|n.auth','like','%'.$text.'%'];
            }
        }
        if ($search['classes']!='all'){
            $where[] = ['n.class','=',$search['classes']];
        }
        
        $data = Newsdetail::alias('n')
            ->field('
        n.id,n.title,n.auth,n.created_at,n.updated_at,
        c.name as c_name
    ')
            ->join('newsclass c','c.id=n.class','LEFT')
            ->where('n.is_delete', 0)
            ->where($where)
            ->order('n.created_at', 'desc')
            ->select();
            
        // 使用导出服务
        $exportService = new \app\service\ExportService();
        
        // 定义表头
        $headers = [
            '板块',
            '标题',
            '发布者',
            '发布时间',
            '修改时间'
        ];
        
        // 格式化数据
        $formattedData = $exportService->formatNewsData($data);
        
        // 生成文件名
        $filename = $exportService->generateFilename('新闻列表');
        
        // 生成Excel文件
        $filepath = $exportService->generateExcel($formattedData, $headers, $filename);
        
        // 返回下载信息
        return json([
            'status' => 'success', 
            'message' => '导出成功',
            'data' => [
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $exportService->getFileUrl($filename)
            ]
        ]);
    }
}