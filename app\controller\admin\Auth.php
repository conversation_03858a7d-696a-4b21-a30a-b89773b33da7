<?php

namespace app\controller\admin;

use app\BaseController;
use app\model\User;
use app\model\Usermode;
use think\facade\Request;
use think\facade\Session;
use think\facade\View;

class Auth extends BaseController
{
    public function login()
    {
        if (Request::isPost()) {
            LogExecution('点击登录按钮');
            $data = Request::post();
            $user = User::where('username', $data['username'])->where('status',0)->find();
            if ($user && password_verify($data['password'], $user->password)) {
                // 密码验证成功，设置会话
                $usermode = Usermode::where('id', $user['usermode'])->where('is_delete', 0)->find();
                $usermode_text = $usermode ? $usermode['group_name'] : '未知';
                $data=[
                    'username'=>$user['username'],
                    'name'=>$user['name'],
                    'usermode'=>$user['usermode'],
                    'usermode_text'=>$usermode_text,
                    'college'=>$user['college']
                ];
                session::set('user',$data);
                // 可以根据需要设置更多会话变量
                // 登陆成功
                // 记录日志
                LogExecution('登录成功');
                return json(['status' => 'success', 'message' => '登录成功']);
            } else {
                // 密码验证失败
                LogExecution('登录失败，用户名或密码错误');
                return json(['status' => 'error', 'message' => '用户名或密码错误']);
            }
        }

        // 显示登录表单
        LogExecution('进入登录页');
        return View::fetch('basic/login');
    }

    public function logout()
    {
        // 销毁会话
        Session::clear();
        LogExecution('用户主动退出登录');
        // 重定向到登录页面
        return redirect('auth/login');
    }
}