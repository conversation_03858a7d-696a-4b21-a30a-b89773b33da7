{include file="public/_header"}
  <link rel="stylesheet" href="../../static/css/dc/intermproject.css">
<style>
  body{
    overflow-x: hidden;
    /* padding: 0 3rem; */
  }
</style>
<div id="app">
  <p style="margin: 0 0 1.5rem 0;color: #00000097;">当前位置：
    <a style="text-decoration: none;color: #00000097;" href="">首页</a>
    >
    <a style="text-decoration: none;color: #00000097;" href="">大创平台</a>
    >
    <a style="text-decoration: none;color: #00000097;" :href="'dc-intermproject?uid=' + uid" >中期报告</a>
  </p>

  <el-row >
    <h2>“大学生创新创业训练计划”项目中期检查报告书</h2>
    <el-form ref="form" :rules="rules" label-position="left" :model="form" label-width="15rem" >
      <el-form-item label="研究工作进展情况" prop="progress">
        <el-input
        type="textarea"
        maxlength="1000"
        show-word-limit
        :autosize="{ minRows: 5, maxRows: 20}"
        placeholder="工作方案实施情况，拟开展工作、存在的问题，能否按时完成研究计划，推迟或终止研究工作的原因等（限1000字）"
        v-model="form.progress">
       </el-input>
      </el-form-item>
      <el-form-item label="阶段性成果" prop="outcomes">
        <el-button plain type="primary" @click="addOutcome">添加成果</el-button>
        <div v-for="(outcome, index) in form.outcomes" :key="index" class="outcome-row">
          <el-input v-model="outcome.name" placeholder="成果名称" style="width: 200px;"></el-input>
          <el-input v-model="outcome.form" placeholder="成果形式" style="width: 200px;"></el-input>
          <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="outcome.date" type="date" placeholder="完成时间" style="width: 200px;"></el-date-picker>
          <el-input v-model="outcome.remark" placeholder="备注" style="width: 400px;"></el-input>
          <el-button plain type="danger" @click="removeOutcome(index)">删除</el-button>
        </div>
      </el-form-item>
      <el-form-item label="项目中期经费支出情况" prop="expenses">
        <el-button plain type="primary" @click="addExpense" style="margin-top: 20px;">添加支出</el-button>
        <div v-for="(expense, index) in form.expenses" :key="index" class="expense-row">
          <el-input v-model="expense.detail" placeholder="支出明细" style="width: 600px;"></el-input>
          <el-input-number :min="0" :max="10000" v-model="expense.amount" placeholder="金额" style="width: 200px; margin-left: 20px;"></el-input-number>
          <el-button plain type="danger" @click="removeExpense(index)">删除</el-button>
        </div>
      </el-form-item>
      <el-form-item label="支出总计" prop="total">
        <el-input type="text"  readonly :value="computedTotalAmount"> </el-input>
      </el-form-item>
      <el-form-item label="剩余资金" prop="total">
        <el-input type="text"  readonly :value="{$budget}- computedTotalAmount"> </el-input>
      </el-form-item>
      <el-form-item label="项目中期经费支出情况" prop="statement"> 
        <el-input
        type="textarea"
        maxlength="1000"
        show-word-limit
        :autosize="{ minRows: 5, maxRows: 20}"
        placeholder="资金具体流向"
        v-model="form.statement">
       </el-input>
      </el-form-item>
      <el-col  :sm="24"  style="text-align: center;">
        <el-button type="primary " plain @click="onSubmit">立即提交中期报告</el-button>
      
          </el-col>

    </el-form>
  
  </el-row>




</div>
<script src="../../static/js/dc/intermproject.js"></script>
<script>
      {if isset($data)}
    app.updateform({:json_encode($data)})
{/if}
</script>
</body>
</html>